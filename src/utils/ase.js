// 登录页滑块验证
import CryptoJS from 'crypto-js'
// export function aesEncrypt(word) {
//   var key = CryptoJS.enc.Utf8.parse('BGxdEUOZkXka4HSj')
//   var srcs = CryptoJS.enc.Utf8.parse(word)
//   var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
//   return encrypted.toString()
// }
export function aesEncrypt(word, keyWord) {
  // var keyWord = keyWord || "XwKsGlMcdPMEhR1B"
  var key = CryptoJS.enc.Utf8.parse(keyWord)
  var srcs = CryptoJS.enc.Utf8.parse(word)
  var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
  return encrypted.toString()
}
export function aesDecrypt(word) {
  var key = CryptoJS.enc.Utf8.parse('BGxdEUOZkXka4HSj')
  var decrypt = CryptoJS.AES.decrypt(word, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
  return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}
