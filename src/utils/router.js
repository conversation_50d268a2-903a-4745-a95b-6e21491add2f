import router from '@/router'

/**
 * * 根据名称获取路由信息
 * @param pageName
 * @param key
 */
export const fetchPathByName = (pageName, key) => {
  try {
    const pathData = router.resolve({
      name: pageName,
    })
    return key ? pathData[key] : pathData
  } catch (error) {
    console.warning('查询路由信息失败！')
  }
}

/**
 * * 根据路径跳转路由
 * @param path
 * @param query
 * @param isReplace
 * @param windowOpen
 */
export const routerTurnByPath = (path, query, isReplace, windowOpen) => {
  let fullPath = ''
  if (query?.length) {
    fullPath = `${path}/${query.join('/')}`
  }
  if (windowOpen) {
    return openNewWindow(fullPath)
  }
  if (isReplace) {
    router.replace({
      path: fullPath
    })
    return
  }
  router.push({
    path: fullPath
  })
}

/**
 * * 新开页面
 * @param url
 */
export const openNewWindow = (url) => {
  return window.open(url, '_blank')
}
