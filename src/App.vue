<template>
  <a-config-provider :locale="locale" :autoInsertSpaceInButton="false">
    <div id="app">
      <router-view />
    </div>
  </a-config-provider>
</template>
<script>
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
import enquireScreen from '@/utils/device'

export default {
  data() {
    return {
      locale: zhCN
    }
  },
  created() {
    let that = this
    enquireScreen((deviceType) => {
      // tablet
      if (deviceType === 0) {
        that.$store.commit('TOGGLE_DEVICE', 'mobile')
        that.$store.dispatch('setSidebar', false)
      }
      // mobile
      else if (deviceType === 1) {
        that.$store.commit('TOGGLE_DEVICE', 'mobile')
        that.$store.dispatch('setSidebar', false)
      } else {
        that.$store.commit('TOGGLE_DEVICE', 'desktop')
        that.$store.dispatch('setSidebar', true)
      }
    })
  }
}
</script>
<style>
html,
body,
#app {
  height: 100%;
}
* {
  margin: 0;
  padding: 0;
}
.formItemDiv {
  width: 100%;
  height: 30px;
  border-radius: 4px;
  background-color: #fff;
  text-indent: 1em;
  font-size: 13px;
  cursor: pointer;
  color: #666;
}
*::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
*::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* background: #5e708d; */
}
*::-webkit-scrollbar-track {
  border-radius: 4px;
  /* background: #ddd; */
}
</style>
