:root {
  /* 颜色 */
  --color-red: #fc625d;
  --color-warn: #fcbc40;
  --color-success: #34c749;

  /* 文字 */
  --color-text: #1d2129;
  --color-text-1: #4e5969;
  --color-text-2: #86909c;
  --color-text-3: #c9cdd4;
  --color-text-4: #f2f3f5;

  /* spacing */
  --spacing-base-size: 1em;

  /* 变亮值 */
  --light-shallow: 2%;

  /* 白色 */
  --color-light-bg: #fff;
  --color-light-bg-1: #fafafc;
  --color-light-bg-1-shallow: #fcfcfe;
  --color-light-bg-2: #f2f3f5;
  --color-light-bg-2-shallow: #f4f5f7;
  --color-light-bg-3: #e5e6eb;
  --color-light-bg-3-shallow: #e7e8ed;
  --color-light-bg-4: #e3e3e4;
  --color-light-bg-4-shallow: #e5e5e6;
  --color-light-bg-5: #bebebe;
  --color-light-bg-5-shallow: #c0c0c0;

  /* 变暗值 */
  --dark-shallow: 2%;

  /* 黑色 */
  --color-dark-black: #000;
  --color-dark-bg-1: #18181c;
  --color-dark-bg-1-shallow: #16161a;
  --color-dark-bg-2: #232324;
  --color-dark-bg-2-shallow: #212122;
  --color-dark-bg-3: #2a2a2b;
  --color-dark-bg-3-shallow: #282829;
  --color-dark-bg-4: #313132;
  --color-dark-bg-4-shallow: #2f2f30;
  --color-dark-bg-5: #373739;
  --color-dark-bg-5-shallow: #353537;

  /* 最大宽度 */
  --max-width: 1920px;
  /* 顶部距离 */
  --header-height: 60px;
  /* 底部距离 */
  --footer-height: 50px;
  /* 模糊 */
  --filter-blur-base: blur(20px);
  /* 边框 */
  --border-radius-base: 8px;
  /* 阴影 */
  --border-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  /* 渐变背景 */
  --background-image: linear-gradient(120deg, var(--color-light-bg) 0%, var(--color-light-bg) 100%);
  /* 斑点背景 */
  --background-point:
  (
    linear-gradient(var(--color-light-bg-1) 14px, transparent 0),
    linear-gradient(90deg, transparent 14px, var(--color-dark-bg-5) 0)
  );
  /* 阴影 */
  --box-shadow: 0 8px 10px #00000012;
}
