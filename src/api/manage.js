import Vue from 'vue'
import { axios } from '@/utils/request'
import signMd5Utils from '@/utils/encryption/signMd5Utils'
function getSignHeaders(url, parameter) {
  const sign = signMd5Utils.getSign(url, parameter)
  const timeStamp = signMd5Utils.getDateTimeToString()
  return {
    'X-Sign': sign,
    'X-TIMESTAMP': timeStamp
  }
}
const api = {
  user: '/mock/api/user',
  role: '/mock/api/role',
  service: '/mock/api/service',
  permission: '/mock/api/permission',
  permissionNoPager: '/mock/api/permission/no-pager'
}

export default api

// post
export function postAction(url, parameter) {
  return axios({
    url: url,
    method: 'post',
    data: parameter,
    headers: getSignHeaders(url, parameter)
  })
}
// post 方式但是某些参数通过param传参，某些参数是data
export function postParamAction(url, parameter, bodyData) {
  return axios({
    url: url,
    method: 'post',
    params: parameter,
    data: bodyData,
    headers: getSignHeaders(url, parameter)
  })
}
// post method= {post | put}
export function httpAction(url, parameter, method) {
  return axios({
    url: url,
    method: method,
    data: parameter,
    headers: getSignHeaders(url, parameter)
  })
}

// put
export function putAction(url, parameter) {
  return axios({
    url: url,
    method: 'post',
    data: parameter,
    headers: getSignHeaders(url, parameter)
  })
}

// get
export function getAction(url, parameter) {
  return axios({
    url: url,
    method: 'get',
    params: parameter,
    headers: getSignHeaders(url, parameter)
  })
}

// deleteAction
export function deleteAction(url, parameter) {
  return axios({
    url: url,
    method: 'get',
    params: parameter,
    headers: getSignHeaders(url, parameter)
  })
}

export function getUserList(parameter) {
  return axios({
    url: api.user,
    method: 'get',
    params: parameter,
    headers: getSignHeaders(url, parameter)
  })
}

export function getRoleList(parameter) {
  return axios({
    url: api.role,
    method: 'get',
    params: parameter,
    headers: getSignHeaders(url, parameter)
  })
}

export function getServiceList(parameter) {
  return axios({
    url: api.service,
    method: 'get',
    params: parameter,
    headers: getSignHeaders(url, parameter)
  })
}

export function getPermissions(parameter) {
  return axios({
    url: api.permissionNoPager,
    method: 'get',
    params: parameter,
    headers: getSignHeaders(url, parameter)
  })
}

// id == 0 add     post
// id != 0 update  put
export function saveService(parameter) {
  return axios({
    url: api.service,
    method: parameter.id == 0 ? 'post' : 'post',
    data: parameter,
    headers: getSignHeaders(url, parameter)
  })
}

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export function downFile(url, parameter) {
  return axios({
    url: url,
    params: parameter,
    method: 'get',
    responseType: 'blob',
    headers: getSignHeaders(url, parameter)
  })
}

/**
 * 下载文件
 * @param url 文件路径
 * @param fileName 文件名
 * @param parameter
 * @returns {*}
 */
export function downloadFile(url, fileName, parameter) {
  return downFile(url, parameter).then((data) => {
    if (!data || data.size === 0) {
      Vue.prototype['$message'].warning('文件下载失败')
      return
    }
    if (typeof window.navigator.msSaveBlob !== 'undefined') {
      window.navigator.msSaveBlob(new Blob([data]), fileName)
    } else {
      let url = window.URL.createObjectURL(new Blob([data]))
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) // 下载完成移除元素
      window.URL.revokeObjectURL(url) // 释放掉blob对象
    }
  })
}

/**
 * 文件上传 用于富文本上传图片
 * @param url
 * @param parameter
 * @returns {*}
 */
export function uploadAction(url, parameter) {
  const signHeader = getSignHeaders(url, parameter)

  // 合并请求头
  const headers = {
    ...signHeader, // 这里添加签名和时间戳
    'Content-Type': 'multipart/form-data' // 保证 Content-Type 为 multipart/form-data
  }
  return axios({
    url: url,
    data: parameter,
    method: 'post',
    headers: headers
  })
}

/**
 * 获取文件服务访问路径
 * @param avatar
 * @param subStr
 * @returns {*}
 */
export function getFileAccessHttpUrl(avatar, subStr) {
  if (!subStr) subStr = window._CONFIG['staticDomainURL']
  try {
    if (avatar && avatar.includes(subStr)) {
      return avatar
    } else {
      if (avatar && avatar.length > 0 && avatar.indexOf('[') == -1) {
        return window._CONFIG['staticDomainURL'] + '/' + avatar
      }
    }
  } catch (err) { }
}

/**
 * 将Base64编码的字符串转换为File对象。
 * 这个函数接受一个Base64编码的字符串，一个可选的文件名和一个可选的MIME类型，
 * 并返回一个代表原始数据的File对象。
 *
 * @param base64 Base64编码的字符串。
 * @param fileName 文件名，默认为"file.txt"。
 * @param mimeType MIME类型，默认为"text/plain"。
 * @returns 返回一个File对象，包含解码后的数据。
 */
export function base64ToFile(base64, fileName = 'file.png', mimeType = 'image/png') {
  const base64String = base64.replace(/^data:.+;base64,/, '')

  const fixedBase64 = base64String.replace(/-/g, '+').replace(/_/g, '/')
  const padding = '='.repeat((4 - (fixedBase64.length % 4)) % 4)
  const validBase64 = fixedBase64 + padding

  const byteCharacters = atob(validBase64)

  // 将字节序列分割成512字节的块，并将每个块转换为Uint8Array，以便创建Blob对象。
  const byteArrays = []

  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512)
    const byteNumbers = new Array(slice.length)

    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i)
    }

    byteArrays.push(new Uint8Array(byteNumbers))
  }

  // 使用前面创建的字节数组块创建Blob对象。
  const blob = new Blob(byteArrays, { type: mimeType })

  return new File([blob], fileName, { type: mimeType })
}
