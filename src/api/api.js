import { getAction, deleteAction, postAction, httpAction } from '@/api/manage'
import Vue from 'vue'
import { UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types'

////根路径
// const doMian = "/jeecg-boot/";
////图片预览请求地址
// const imgView = "http://localhost:8080/jeecg-boot/sys/common/view/";

//角色管理
const addRole = (params) => postAction('/sys/role/add', params)
const editRole = (params) => postAction('/sys/role/edit', params)
// const getRoleList = (params)=>getAction("/sys/role/list",params);
// const deleteRole = (params)=>getAction("/sys/role/delete",params);
// const deleteRoleList = (params)=>getAction("/sys/role/deleteBatch",params);
const checkRoleCode = (params) => getAction('/sys/role/checkRoleCode', params)
const queryall = (params) => getAction('/sys/role/queryall', params)

//用户管理
const addUser = (params) => postAction('/sys/user/add', params)
const editUser = (params) => postAction('/sys/user/edit', params)
const queryUserRole = (params) => getAction('/sys/user/queryUserRole', params)
const getUserList = (params) => getAction('/sys/user/list', params)
// const deleteUser = (params)=>getAction("/sys/user/delete",params);
// const deleteUserList = (params)=>getAction("/sys/user/deleteBatch",params);
const frozenBatch = (params) => postAction('/sys/user/frozenBatch', params)
//验证用户是否存在
const checkOnlyUser = (params) => getAction('/sys/user/checkOnlyUser', params)
//改变密码
const changePassword = (params) => postAction('/sys/user/changePassword', params)

//权限管理
const addPermission = (params) => postAction('/sys/permission/add', params)
const editPermission = (params) => postAction('/sys/permission/edit', params)
const getPermissionList = (params) => getAction('/sys/permission/list', params)
/*update_begin author:wuxianquan date:20190908 for:添加查询一级菜单和子菜单查询api */
const getSystemMenuList = (params) => getAction('/sys/permission/getSystemMenuList', params)
const getSystemSubmenu = (params) => getAction('/sys/permission/getSystemSubmenu', params)
const getSystemSubmenuBatch = (params) => getAction('/sys/permission/getSystemSubmenuBatch', params)
/*update_end author:wuxianquan date:20190908 for:添加查询一级菜单和子菜单查询api */

// const deletePermission = (params)=>getAction("/sys/permission/delete",params);
// const deletePermissionList = (params)=>getAction("/sys/permission/deleteBatch",params);
const queryTreeList = (params) => getAction('/sys/permission/queryTreeList', params)
const queryTreeListForRole = (params) => getAction('/sys/role/queryTreeList', params)
const queryListAsync = (params) => getAction('/sys/permission/queryListAsync', params)
const queryRolePermission = (params) => getAction('/sys/permission/queryRolePermission', params)
const saveRolePermission = (params) => postAction('/sys/permission/saveRolePermission', params)
//const queryPermissionsByUser = (params)=>getAction("/sys/permission/queryByUser",params);
const queryPermissionsByUser = () => getAction('/sys/permission/getUserPermissionByToken')
const loadAllRoleIds = (params) => getAction('/sys/permission/loadAllRoleIds', params)
const getPermissionRuleList = (params) =>
  getAction('/sys/permission/getPermRuleListByPermId', params)
const queryPermissionRule = (params) => getAction('/sys/permission/queryPermissionRule', params)

// 部门管理
const queryDepartTreeList = (params) => getAction('/sys/sysDepart/queryTreeList', params)
const queryIdTree = (params) => getAction('/sys/sysDepart/queryIdTree', params)
const queryParentName = (params) => getAction('/sys/sysDepart/queryParentName', params)
const searchByKeywords = (params) => getAction('/sys/sysDepart/searchBy', params)
const deleteByDepartId = (params) => getAction('/sys/sysDepart/delete', params)

//二级部门管理
const queryDepartPermission = (params) => getAction('/sys/permission/queryDepartPermission', params)
const saveDepartPermission = (params) => postAction('/sys/permission/saveDepartPermission', params)
const queryTreeListForDeptRole = (params) =>
  getAction('/sys/sysDepartPermission/queryTreeListForDeptRole', params)
const queryDeptRolePermission = (params) =>
  getAction('/sys/sysDepartPermission/queryDeptRolePermission', params)
const saveDeptRolePermission = (params) =>
  postAction('/sys/sysDepartPermission/saveDeptRolePermission', params)
const queryMyDepartTreeList = (params) => getAction('/sys/sysDepart/queryMyDeptTreeList', params)

//日志管理
//const getLogList = (params)=>getAction("/sys/log/list",params);
const deleteLog = (params) => getAction('/sys/log/delete', params)
const deleteLogList = (params) => getAction('/sys/log/deleteBatch', params)

//数据字典
const addDict = (params) => postAction('/sys/dict/add', params)
const editDict = (params) => postAction('/sys/dict/edit', params)
//const getDictList = (params)=>getAction("/sys/dict/list",params);
const treeList = (params) => getAction('/sys/dict/treeList', params)
// const delDict = (params)=>getAction("/sys/dict/delete",params);
//const getDictItemList = (params)=>getAction("/sys/dictItem/list",params);
const addDictItem = (params) => postAction('/sys/dictItem/add', params)
const editDictItem = (params) => postAction('/sys/dictItem/edit', params)
//const delDictItem = (params)=>getAction("/sys/dictItem/delete",params);
//const delDictItemList = (params)=>getAction("/sys/dictItem/deleteBatch",params);

//字典标签专用（通过code获取字典数组）
export const ajaxGetDictItems = (code, params) =>
  getAction(`/sys/dict/getDictItems/${code}`, params)
//从缓存中获取字典配置
function getDictItemsFromCache(dictCode) {
  if (Vue.ls.get(UI_CACHE_DB_DICT_DATA) && Vue.ls.get(UI_CACHE_DB_DICT_DATA)[dictCode]) {
    let dictItems = Vue.ls.get(UI_CACHE_DB_DICT_DATA)[dictCode]
    // console.log('-----------getDictItemsFromCache----------dictCode=' + dictCode + '---- dictItems=', dictItems)
    return dictItems
  }
}

//系统通告
const doReleaseData = (params) => getAction('/sys/annountCement/doReleaseData', params)
const doReovkeData = (params) => getAction('/sys/annountCement/doReovkeData', params)
//获取系统访问量
const getLoginfo = (params) => getAction('/sys/loginfo', params)
const getVisitInfo = (params) => getAction('/sys/visitInfo', params)
//数据日志访问
// const getDataLogList = (params)=>getAction("/sys/dataLog/list",params);

// 根据部门主键查询用户信息
const queryUserByDepId = (params) => getAction('/sys/user/queryUserByDepId', params)

// 查询用户角色表里的所有信息
// const queryUserRoleMap = (params)=>getAction("/sys/user/queryUserRoleMap",params);
// 重复校验
const duplicateCheck = (params) => getAction('/sys/duplicate/check', params)
// 加载分类字典
const loadCategoryData = (params) => getAction('/sys/category/loadAllData', params)
const checkRuleByCode = (params) => getAction('/sys/checkRule/checkByCode', params)
//加载站点信息
const getSiteInfo = (params) => getAction('/eoa/cms/eoaCmsSite/getSiteInfo', params)
//加载我的计划信息
const getPlanInfo = (params) => getAction('/eoa/plan/queryMyCreationByDate', params)
//加载我的通告信息
const getUserNoticeInfo = (params) =>
  getAction('/sys/sysAnnouncementSend/getMyAnnouncementSend', params)
//查询图表数据
const getChartData = (params) => getAction('/joa/dataStatistics/getChartDate', params)
const getPieData = (params) => getAction('/joa/dataStatistics/getPieDate', params)
const getTransitURL = (url) => `/sys/common/transitRESTful?url=${encodeURIComponent(url)}`
// 中转HTTP请求
export const transitRESTful = {
  get: (url, parameter) => getAction(getTransitURL(url), parameter),
  post: (url, parameter) => postAction(getTransitURL(url), parameter),
  put: (url, parameter) => postAction(getTransitURL(url), parameter),
  http: (url, parameter) => httpAction(getTransitURL(url), parameter)
}
//查询会议室
const getMettingRoomList = (params) => getAction('/eoa/metting/eoaMettingRoom/list', params)

// 滑动验证码
const reqGet = (params) => postAction('/captcha/get', params)
const reqCheck = (params) => postAction('/captcha/check', params)
const traverseTree = (node, func) => {
  const callback = func(node)
  if (callback === 'returnFunc') return 'breakLoop'
  if (node.childList && node.childList.length > 0) {
    node.childList.forEach((child) => {
      traverseTree(child, func)
    })
  } else if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      traverseTree(child, func)
    })
  }
}
function convertCssToJs(cssInput) {
  const originalInput = cssInput.trim()

  // 使用分隔符$拆分为多个CSS对象
  const cssParts = originalInput.split('$')
  let output = ''

  cssParts.forEach((part, index) => {
    let cssText = part.trim()

    // 处理第一个对象的开头花括号
    if (cssText.startsWith('{')) {
      cssText = cssText.substring(1).trim()
    }

    // 处理最后一个对象的结尾花括号
    if (cssText.endsWith('}')) {
      cssText = cssText.substring(0, cssText.length - 1).trim()
    }

    // 移除注释
    cssText = cssText.replace(/\/\*[\s\S]*?\*\//g, '')

    // 分割CSS属性行
    const lines = cssText
      .split(/[\n;]/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0)

    // 存储转换后的样式对象
    const styleObj = {}

    lines.forEach((line) => {
      if (line.includes(':')) {
        const colonIndex = line.indexOf(':')
        const prop = line.substring(0, colonIndex).trim()
        let value = line.substring(colonIndex + 1).trim()

        // 处理空值情况
        if (value === '""' || value === "''" || value === '') {
          value = ''
        } else {
          // 处理引号包裹的值
          if (typeof value === 'string') {
            if (
              (value.startsWith('"') && value.endsWith('"')) ||
              (value.startsWith("'") && value.endsWith("'"))
            ) {
              value = value.substring(1, value.length - 1)
            }
          }
        }

        // 处理属性名
        let propName = prop.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
        // 添加到样式对象
        styleObj[propName] = value
      }
    })

    // 构建对象字符串
    let objString = '{\n'
    for (const [key, value] of Object.entries(styleObj)) {
      let formattedValue

      if (value === '') {
        formattedValue = "''"
      } else if (typeof value === 'number') {
        formattedValue = value
      } else {
        formattedValue = `'${value}'`
      }
      objString += `  ${key}: ${formattedValue},\n`
    }

    // 移除最后一个逗号并添加结束花括号
    if (Object.keys(styleObj).length > 0) {
      objString = objString.slice(0, -2) + '\n'
    }
    objString += '}'

    // 添加到总输出
    if (index > 0) {
      output += '$\n'
    }
    output += objString
  })
  return output
}
export {
  // imgView,
  // doMian,
  addRole,
  editRole,
  checkRoleCode,
  addUser,
  editUser,
  queryUserRole,
  getUserList,
  queryall,
  frozenBatch,
  checkOnlyUser,
  changePassword,
  getPermissionList,
  addPermission,
  editPermission,
  queryTreeList,
  queryListAsync,
  queryRolePermission,
  saveRolePermission,
  queryPermissionsByUser,
  loadAllRoleIds,
  getPermissionRuleList,
  queryPermissionRule,
  queryDepartTreeList,
  queryIdTree,
  queryParentName,
  searchByKeywords,
  deleteByDepartId,
  deleteLog,
  deleteLogList,
  addDict,
  editDict,
  treeList,
  addDictItem,
  editDictItem,
  doReleaseData,
  doReovkeData,
  getLoginfo,
  getVisitInfo,
  queryUserByDepId,
  duplicateCheck,
  queryTreeListForRole,
  getSystemMenuList,
  getSystemSubmenu,
  getSystemSubmenuBatch,
  loadCategoryData,
  checkRuleByCode,
  queryDepartPermission,
  saveDepartPermission,
  queryTreeListForDeptRole,
  queryDeptRolePermission,
  saveDeptRolePermission,
  queryMyDepartTreeList,
  getSiteInfo,
  getUserNoticeInfo,
  getPlanInfo,
  getChartData,
  getPieData,
  getMettingRoomList,
  getDictItemsFromCache,
  reqGet,
  reqCheck,
  traverseTree,
  convertCssToJs
}
