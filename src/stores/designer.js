import { defineStore } from 'pinia'
import { cloneDeep } from 'lodash-es'

export const useDesignerStore = defineStore('designer', {
  state: () => ({
    componentData: [],
    designData: {
      id: '',
      topicName: '我的大屏',
      topicDescription: '',
      scaleX: 1920,
      scaleY: 1080,
      topicBackground: '',
      topicIcon: '',
      topicUrl: '',
      topicCode: '',
      bgColor: 'rgba(255,255,255)',
      status: 1,
      defaultBgImage: '',
      defaultMapUrl: '',
      defaultMenuUrl: '',
      topicTemplate: 0
    },
    currentCpt: { cptOption: undefined },
    currentCptId: '',
    // 多选相关状态
    selectedComponentIds: [], // 选中的组件ID数组
    isMultiSelecting: false, // 是否正在框选
    selectionBox: {
      // 框选矩形
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      visible: false
    },
    isModule: false,
    history: [],
    historyIndex: -1,
    maxHistoryLength: 10,
    isRestoring: false,
    allRole: [],
    indexSetTree: []
  }),
  getters: {
    canUndo(state) {
      return state.historyIndex > 0
    },
    canRedo(state) {
      return state.historyIndex < state.history.length - 1
    }
  },
  actions: {
    _pushStateToHistory(description = '操作', componentId = null) {
      if (this.isRestoring) return

      const snapshot = {
        componentData: cloneDeep(this.componentData),
        designData: cloneDeep(this.designData),
        description,
        timestamp: Date.now(),
        componentId, // 记录操作的组件ID（可以是单个ID或ID数组）
        componentIds: Array.isArray(componentId) ? componentId : componentId ? [componentId] : [] // 统一的组件ID数组
      }

      if (this.historyIndex < this.history.length - 1) {
        this.history = this.history.slice(0, this.historyIndex + 1)
      }

      this.history.push(snapshot)
      this.historyIndex++

      if (this.history.length > this.maxHistoryLength) {
        this.history.shift()
        this.historyIndex--
      }
    },
    setInitialState(designData, componentData) {
      this.isRestoring = true

      this.designData = cloneDeep(designData)
      this.componentData = cloneDeep(componentData)

      this.history = []
      const snapshot = {
        componentData: cloneDeep(this.componentData),
        designData: cloneDeep(this.designData),
        description: '初始状态',
        timestamp: Date.now()
      }
      this.history.push(snapshot)
      this.historyIndex = 0

      this.isRestoring = false
    },
    pushHistory(description = '操作', componentId = null) {
      this._pushStateToHistory(description, componentId)
    },
    undo() {
      if (this.canUndo) {
        this.isRestoring = true
        this.historyIndex--
        const previousState = this.history[this.historyIndex]

        this.componentData = cloneDeep(previousState.componentData)
        this.designData = cloneDeep(previousState.designData)

        this.isRestoring = false
        this.currentCpt = { cptOption: undefined }
        this.currentCptId = ''
      }
    },
    redo() {
      if (this.canRedo) {
        this.isRestoring = true
        this.historyIndex++
        const nextState = this.history[this.historyIndex]
        this.componentData = cloneDeep(nextState.componentData)
        this.designData = cloneDeep(nextState.designData)
        this.isRestoring = false
        this.currentCpt = { cptOption: undefined }
        this.currentCptId = ''
      }
    },
    jumpToHistory(index) {
      if (index >= 0 && index < this.history.length) {
        this.isRestoring = true
        this.historyIndex = index
        const targetState = this.history[index]
        this.componentData = cloneDeep(targetState.componentData)
        this.designData = cloneDeep(targetState.designData)
        this.isRestoring = false
        this.currentCpt = { cptOption: undefined }
        this.currentCptId = ''
      }
    },
    setDesignData(data) {
      this.designData = { ...this.designData, ...cloneDeep(data) }
    },
    setComponentData(data) {
      this.componentData = cloneDeep(data)
    },
    setIsModule(flag) {
      this.isModule = flag
    },
    addComponent(cpt) {
      this.componentData.push(cloneDeep(cpt))
    },
    deleteComponent(cptId) {
      const deleteRecursively = (components, id) => {
        for (let i = components.length - 1; i >= 0; i--) {
          const component = components[i]
          if (component.id === id) {
            components.splice(i, 1)
            return true
          }
          if (component.children && component.children.length > 0) {
            if (deleteRecursively(component.children, id)) {
              return true
            }
          }
        }
        return false
      }
      deleteRecursively(this.componentData, cptId)
    },
    updateComponent(updatedCpt) {
      const findAndUpdate = (components, cpt) => {
        for (let i = 0; i < components.length; i++) {
          if (components[i].id === cpt.id) {
            components[i] = cloneDeep(cpt)
            return true
          }
          if (components[i].children && findAndUpdate(components[i].children, cpt)) {
            return true
          }
        }
        return false
      }
      findAndUpdate(this.componentData, updatedCpt)
    },
    setCurrentCpt(cpt) {
      this.currentCpt = cpt ? cloneDeep(cpt) : { cptOption: undefined }
    },
    setCurrentCptId(id) {
      this.currentCptId = id
    },
    // 多选相关方法
    setSelectedComponentIds(ids) {
      this.selectedComponentIds = [...ids]
    },
    addSelectedComponentId(id) {
      if (!this.selectedComponentIds.includes(id)) {
        this.selectedComponentIds.push(id)
      }
    },
    removeSelectedComponentId(id) {
      const index = this.selectedComponentIds.indexOf(id)
      if (index > -1) {
        this.selectedComponentIds.splice(index, 1)
      }
    },
    clearSelectedComponents() {
      this.selectedComponentIds = []
    },
    setMultiSelecting(isSelecting) {
      this.isMultiSelecting = isSelecting
    },
    setSelectionBox(box) {
      this.selectionBox = { ...this.selectionBox, ...box }
    },
    updateSelectionBox(startX, startY, endX, endY) {
      this.selectionBox = {
        startX: Math.min(startX, endX),
        startY: Math.min(startY, endY),
        endX: Math.max(startX, endX),
        endY: Math.max(startY, endY),
        visible: true
      }
    },
    hideSelectionBox() {
      this.selectionBox.visible = false
    },
    // 批量移动选中的组件
    moveSelectedComponents(deltaX, deltaY) {
      this.selectedComponentIds.forEach((id) => {
        const component = this.findComponentById(id)
        if (component) {
          component.cptX += deltaX
          component.cptY += deltaY
        }
      })
    },
    // 查找组件的辅助方法
    findComponentById(id) {
      const findInArray = (components) => {
        for (const component of components) {
          if (component.id === id) {
            return component
          }
          if (component.children && component.children.length > 0) {
            const found = findInArray(component.children)
            if (found) return found
          }
        }
        return null
      }
      return findInArray(this.componentData)
    }
  }
})
