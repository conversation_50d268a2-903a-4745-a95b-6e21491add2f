export function resize(el, binding, vNode) {
  //组件拉伸，移动位置
  el.onmousedown = function (e) {
    const that = vNode.context
    const scaleClientX = e.clientX / that.containerScale
    const scaleClientY = e.clientY / that.containerScale
    const rbX = scaleClientX - el.parentNode.offsetWidth
    const rbY = scaleClientY - el.parentNode.offsetHeight
    const ltX = scaleClientX + el.parentNode.offsetWidth
    const ltY = scaleClientY + el.parentNode.offsetHeight
    const disX = scaleClientX - el.parentNode.offsetLeft
    const disY = scaleClientY - el.parentNode.offsetTop
    let cptWidth, cptHeight, cptX, cptY

    document.onmousemove = function (me) {
      const meScaleClientX = me.clientX / that.containerScale
      const meScaleClientY = me.clientY / that.containerScale
      if (binding.value === 'move') {
        cptX = meScaleClientX - disX
        cptY = meScaleClientY - disY
        Object.keys(that.cacheChoices).forEach((key) => {
          that.cacheChoices[key].cptX =
            that.cacheChoicesFixed[key].cptX + Math.round(meScaleClientX - scaleClientX)
          that.cacheChoices[key].cptY =
            that.cacheChoicesFixed[key].cptY + Math.round(meScaleClientY - scaleClientY)
        })
      } else {
        switch (binding.value) {
          case 'lt':
            cptWidth = ltX - meScaleClientX
            cptHeight = ltY - meScaleClientY
            cptX = meScaleClientX - disX
            cptY = meScaleClientY - disY
            that.currentCpt.cptX = Math.round(cptX)
            that.currentCpt.cptY = Math.round(cptY)
            break
          case 't':
            cptHeight = ltY - meScaleClientY
            cptY = meScaleClientY - disY
            that.currentCpt.cptY = Math.round(cptY)
            break
          case 'rt':
            cptWidth = meScaleClientX - rbX
            cptHeight = ltY - meScaleClientY
            cptY = meScaleClientY - disY
            that.currentCpt.cptY = Math.round(cptY)
            break
          case 'r':
            cptWidth = meScaleClientX - rbX
            break
          case 'rb':
            cptWidth = meScaleClientX - rbX
            cptHeight = meScaleClientY - rbY
            break
          case 'b':
            cptHeight = meScaleClientY - rbY
            break
          case 'lb':
            cptWidth = ltX - meScaleClientX
            cptHeight = meScaleClientY - rbY
            cptX = meScaleClientX - disX
            that.currentCpt.cptX = Math.round(cptX)
            break
          case 'l':
            cptWidth = ltX - meScaleClientX
            cptX = meScaleClientX - disX
            that.currentCpt.cptX = Math.round(cptX)
            break
        }
        cptWidth = cptWidth < 40 ? 40 : cptWidth //限制最小缩放
        cptHeight = cptHeight < 20 ? 20 : cptHeight
        if (cptWidth) that.currentCpt.cptWidth = Math.round(cptWidth)
        if (cptHeight) that.currentCpt.cptHeight = Math.round(cptHeight)
      }
    }
    document.onmouseup = function () {
      document.onmousemove = document.onmouseup = null
      that.cacheChoicesFixed = JSON.parse(JSON.stringify(that.cacheChoices)) //解决多选移动未松开ctrl键第二次以后拖动定位还原
      cptWidth &&
        cptHeight &&
        that.$refs['configBar'].setCssDataProperty(Math.round(cptWidth), Math.round(cptHeight))
    }
    return false
  }
}
