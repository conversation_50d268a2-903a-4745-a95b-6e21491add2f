<template>
  <div class="table-operator">
    <a-button v-if="buttonSwitch.add" type="primary" icon="plus" @click="handleAdd">新增</a-button>
    <a-button v-if="buttonSwitch.import" type="primary" icon="upload" @click="handleImportXls">导入</a-button>
    <a-button v-if="buttonSwitch.export" type="primary" icon="download" @click="handleExportXls">导出</a-button>

    <template v-if="buttonList">
      <a-button
        v-for="(item,index) in buttonList"
        :key="`cg-btn-${index}`"
        type="primary"
        :icon="item.buttonIcon"
        @click="handleClickCgButton(item)"
      >
        {{ item.buttonName }}
      </a-button>
    </template>

    <!-- 高级查询 -->
    <j-super-query
      ref="superQuery"
      :fieldList="superQuery.fieldList"
      :saveCode="$route.fullPath"
      :loading="table.loading"
      @handleSuperQuery="handleSuperQuery"
    />

    <a-button
      v-if="buttonSwitch.batch_delete"
      v-show="table.selectedRowKeys.length > 0"
      ghost
      type="primary"
      icon="delete"
      @click="handleDelBatch"
    >
      批量删除
    </a-button>

  </div>
</template>

<script>

  export default {
    name: 'CgformAutoListButtons',
    components: {  },
    inject: ['handleAdd', 'handleImportXls', 'handleExportXls', 'cgButtonJsHandler', 'cgButtonActionHandler', 'handleSuperQuery', 'handleDelBatch'],
    props: {
      url: {
        type: Object,
        required: true,
      },
      table: Object,
      buttonList: Array,
      buttonSwitch: Object,
      superQuery: Object,
    },
    data() {
      return {}
    },
    watch: {},
    methods: {

      handleClickCgButton(button) {
        if (button.optType === 'action') {
          this.cgButtonActionHandler(button.buttonCode)
        } else if (button.optType === 'js') {
          this.cgButtonJsHandler(button.buttonCode)
        }
      },

    },
  }
</script>

<style scoped>

</style>