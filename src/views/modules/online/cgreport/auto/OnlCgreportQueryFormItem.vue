<template>
  <a-form-item v-if="item.view=='Date' || item.view=='date'" :label="item.label">
    <template v-if="single_mode===item.mode">
      <j-date v-model="queryParam[item.field]" :placeholder=" '请选择'+item.label "></j-date>
    </template>
    <div v-else>
      <j-date v-model="queryParam[item.field+'_begin']" placeholder="开始日期" :style="getRangeItemStyle(item)"></j-date>
      <span class="group-query-strig">~</span>
      <j-date v-model="queryParam[item.field+'_end']" placeholder="结束日期" :style="getRangeItemStyle(item)"></j-date>
    </div>
  </a-form-item>

  <a-form-item v-else-if="item.view=='Datetime' || item.view=='datetime'" :label="item.label">
    <template v-if="single_mode===item.mode">
      <j-date v-model="queryParam[item.field]" :placeholder=" '请选择'+item.label " :show-time="true" date-format="YYYY-MM-DD HH:mm:ss"></j-date>
    </template>
    <div v-else>
      <div>
        <j-date v-model="queryParam[item.field+'_begin']" placeholder="开始时间" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" :style="getRangeItemStyle(item)" ></j-date>
        <span class="group-query-strig">~</span>
        <j-date v-model="queryParam[item.field+'_end']" placeholder="开始时间" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" :style="getRangeItemStyle(item)"></j-date>
      </div>

    </div>
  </a-form-item>

  <a-form-item v-else-if=" item.view=='list' || item.view=='radio' " :label="item.label">
    <a-select v-model="queryParam[item.field]" :placeholder=" '请选择'+item.label ">
      <a-select-option
        v-for="(op,opIndex) in dictOptions[item.field]"
        :key="opIndex"
        :value="op.value">
        {{ op.text }}
      </a-select-option>
    </a-select>
  </a-form-item>

  <a-form-item v-else-if=" item.view=='checkbox'" :label="item.label">
    <j-select-multiple v-model="queryParam[item.field]" :placeholder=" '请选择'+item.label " :options="dictOptions[item.field]"></j-select-multiple>
  </a-form-item>

  <a-form-item v-else :label="item.label">
    <div v-if="group_mode===item.mode">
      <a-input v-model="queryParam[item.field+'_begin']" :placeholder=" '开始'+item.label " :style="getRangeItemStyle(item)"></a-input>
      <span class="group-query-strig">~</span>
      <a-input v-model="queryParam[item.field+'_end']" :placeholder=" '结束'+item.label " :style="getRangeItemStyle(item)"></a-input>
    </div>
    <template v-else>
      <a-input v-model="queryParam[item.field]" :placeholder=" '请输入'+item.label "></a-input>
    </template>
  </a-form-item>

</template>

<script>
  import { mixinDevice } from '@/utils/mixin.js'

  export default {
    name: 'OnlineQueryFormItem',
    components:{
    },
    mixins: [mixinDevice],
    props:{
      item:{
        type:Object,
        default:()=>{},
        required:true
      },
      dictOptions:{
        type:Object,
        default:()=>{},
        required:true
      },
      queryParam:{
        type:Object,
        default:()=>{},
        required:true
      }
    },
    data(){
      return {
        single_mode: 'single',
        group_mode: 'group'
      }
    },
    methods:{
      // 范围选择控件的style
      getRangeItemStyle(item) {
        let width = ''
        if (this.isMobile()) {
          width = '100% !important'
        } else {
          width = 'calc(50% - 10px) !important'
        }
        return {
          width: width,
          minWidth: width,
        }
      }
    },
  }
</script>

<style>
  /* 解决移动端下，范围选择上下排列的输入框不能顶开高度的问题 */
  .j-jl-cgreport-query .ant-form-item-control {
    height: auto !important;
  }
</style>

<style scoped>
  .group-query-strig{

  }
</style>