<template>
  <a-modal
    destroyOnClose
    :title="title"
    :width="700"
    :visible="visible"
    :confirmLoading="confirmLoading"
    cancelText="关闭"
    okText="校验"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="initLoading">
      <j-editable-table
        ref="paramsTable"
        :columns="paramsTableColumns"
        :dataSource="params"
        :maxHeight="183"
      />
      <div class="result-title-description">验证结果</div>
      <a-table
        v-if="testResult.length > 0"
        bordered
        class="j-table-force-nowrap"
        rowKey="testId"
        :scroll="{ x: resultTableWidth, y: resultTableHeight }"
        :pagination="false"
        :columns="resultTableColumns"
        :dataSource="testResult"
      />
      <a-empty v-else />
    </a-spin>
  </a-modal>
</template>

<script>
import { FormTypes } from '@/utils/JEditableTableUtil'
import { postAction } from '@/api/manage'

export default {
  name: 'InterfaceBySQLTestModal',
  data() {
    return {
      title: '数据验证',
      visible: false,
      initLoading: true,
      confirmLoading: false,
      sql: '',
      paramsTableColumns: [
        {
          title: '条件变量',
          key: 'paramName',
          width: '50%',
          align: 'center'
        },
        {
          title: '条件值',
          key: 'paramValue',
          width: '50%',
          align: 'center',
          type: FormTypes.input,
          defaultValue: null,
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        }
      ],
      params: [],
      resultTableWidth: 700,
      resultTableHeight: 268,
      resultTableColumns: [],
      resultTableIndexColumn: {
        title: '#',
        dataIndex: '',
        key: 'rowIndex',
        width: 60,
        align: 'center',
        customRender: function (t, r, index) {
          return parseInt(index) + 1
        }
      },
      testResult: [],
      dataSourceCode: '',
      url: {
        getSqlParam: '/topic/config/getSqlServParam',
        testSql: '/topic/config/testSQL'
      }
    }
  },
  methods: {
    /**
     * @description 初始化数据
     * @returns void
     */
    initData(dataSourceCode) {
      this.visible = true
      this.initLoading = true
      this.dataSourceCode = dataSourceCode
      this.params = []
      this.testResult = []
      postAction(this.url.getSqlParam, { sql: this.sql }).then((res) => {
        if (res.success) {
          for (const item of res.result) {
            this.params.push({
              paramName: item,
              paramValue: null
            })
          }
        }
        this.initLoading = false
      })
    },

    /**
     * @description 关闭操作
     * @returns void
     */
    close() {
      this.visible = false
      this.confirmLoading = false
    },

    /**
     * @description 取消操作
     * @returns void
     */
    handleCancel() {
      this.close()
    },

    /**
     * @description 确定操作
     * @returns void
     */
    handleOk() {
      if (this.initLoading) return // 初始化完成作为前置执行条件
      const { error, values } = this.$refs.paramsTable.getValuesSync({ validate: true }) // 同步获取paramsTable数据，并进行输入验证
      if (error === 0) {
        // 验证通过
        this.confirmLoading = true // 开启loading
        // 格式化(对象格式)条件变量以及值
        const formatParam = {}
        for (const item of values) {
          formatParam[item.paramName] = item.paramValue // 键值对
        }
        // 请求数据接口
        postAction(this.url.testSql, {
          sql: this.sql,
          parameters: formatParam,
          dataSource: this.dataSourceCode
        }).then((res) => {
          if (res.success) {
            this.$message.success('校验成功!') // 校验成功，提示信息
            this.showTestResult(res.result.records) // 显示查询结果
            this.$emit('ok', {
              query: this.params.map((param) => {
                return {
                  ...param,
                  queryValue: formatParam[param.paramName]
                }
              }),
              columns: res.result.records.length ? Object.keys(res.result.records[0]) : []
            }) // 消息通知回调
          } else {
            this.$message.error('校验失败，请检查SQL!') // 校验失败，提示信息
            this.testResult = []
          }
          this.confirmLoading = false // 关闭loading
        })
      } else {
        this.$message.warning('条件值不能为空!') // 验证未通过，提示信息
      }
    },

    /**
     * @description 表格形式显示验证结果
     * @returns void
     */
    showTestResult(result) {
      this.testResult = result // 获取结果数据
      result.map((item, index) => {
        item.testId = index // 拼装数据对应索引
      })
      // 获取结果表格表头数据(默认使用第一行数据作为获取基准)
      const defaultPropertyOfBusiness = ['property'] // 系统默认业务属性字段
      const allPropertyOfBusiness =
        result.length > 0 ? Object.keys(result[0]) : defaultPropertyOfBusiness // 获取全部业务属性字段(可枚举属性)
      if (allPropertyOfBusiness.length > 3)
        this.resultTableWidth = allPropertyOfBusiness.length * 200 // 当业务属性字段数量超过3时，重置结果表格宽度(以200为单个字段基准宽度，重新计算表格宽度)
      this.resultTableColumns = [this.resultTableIndexColumn] // 初始化表头数据(添加索引列)
      // 通过遍历添加业务属性字段至表头数据中
      allPropertyOfBusiness.map((item) => {
        if (item !== 'testId') {
          // 跳过自定义索引字段
          this.resultTableColumns.push({
            title: item,
            align: 'center',
            width: 200,
            dataIndex: item
          })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .input-table {
  text-align: center;
}
::v-deep .input-table .scroll-view {
  margin-bottom: -1px;
}
::v-deep .input-table .thead {
  background: #fafafa;
}
::v-deep .input-table .thead .td:first-child {
  border-right: 1px solid #e8e8e8;
}
::v-deep .input-table .tbody .tr:last-child {
  border-bottom: none;
}
::v-deep .input-table .tbody .td:first-child {
  margin-right: 7px;
  border-right: 1px solid #e8e8e8;
}
::v-deep .ant-empty {
  margin: 0;
  padding: 12px 0;
  border: 1px solid #e8e8e8;
}
.result-title-description {
  padding: 12px 0;
}
</style>
