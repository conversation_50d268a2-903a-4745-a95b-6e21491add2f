<template>
  <a-modal
    title="导入数据表"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top: 5%; height: 90%"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
      <a-button type="primary" :loading="btnLoading" @click="submitData">导入</a-button>
    </template>
    <a-spin :spinning="confirmLoading">
      <j-form-container>
        <a-form slot="detail" :form="form">
          <div class="basic-container" style="border-bottom: solid 0.3px #ccc; margin-bottom: 20px">
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="数据源">
                  <a-row :gutter="8">
                    <a-col :span="18">
                      <a-select
                        v-decorator="[
                          'code',
                          { rules: [{ required: true, message: '请选择数据源' }] }
                        ]"
                        show-search
                        :disabled="dataSourceStopSelected"
                        :loading="dataSourceStopSelected"
                        :filter-option="filterOption"
                        :getPopupContainer="(trigger) => trigger.parentNode"
                        placeholder="请选择数据源"
                        @change="changeDataSource"
                      >
                        <a-select-option
                          v-for="item in dataSourceList"
                          :key="item.code"
                          :value="item.code"
                        >
                          {{ item.name }}
                        </a-select-option>
                      </a-select>
                    </a-col>
                    <a-col :span="6">
                      <a-button
                        class="atn-testbtn"
                        type="primary"
                        style="width: 100%"
                        @click="handleTest"
                        >测试</a-button
                      >
                    </a-col>
                  </a-row>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="数据类型">
                  <a-radio-group
                    v-model="dataType"
                    v-decorator="['tableType', { initialValue: 1, rules: [{ required: true }] }]"
                    @change="changeDataType"
                  >
                    <a-radio :value="1">表</a-radio>
                    <a-radio :value="2">视图</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="数据源类型">
                  <j-dict-select-tag
                    v-model="sourceType"
                    placeholder="请选择数据源类型"
                    dictCode="source_type"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="数据表注释" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input v-decorator="['tableComment']" placeholder="请输入数据表注释"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="数据来源" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input
                    v-decorator="['dataOrigin', validatorRules.dataOrigin]"
                    placeholder="请输入数据来源"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="更新频率" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <j-dict-select-tag
                    v-decorator="['dataFrequency', validatorRules.dataFrequency]"
                    type="list"
                    :trigger-change="true"
                    dictCode="data_frequency"
                    placeholder="请选择更新频率"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="同步时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input
                    v-decorator="['syncTime', validatorRules.syncTime]"
                    placeholder="请输入同步时间"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="账期时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input
                    v-decorator="['dataTime', validatorRules.dataTime]"
                    placeholder="请输入账期时间"
                  ></a-input>
                  <!-- <j-date
                    v-decorator="['dataTime', validatorRules.dataTime]"
                    placeholder="请选择账期时间"
                    :trigger-change="true"
                    :show-time="true"
                    date-format="YYYY-MM-DD hh:mm:ss"
                    style="width: 100%"
                  /> -->
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </j-form-container>
      <div class="table-page-search-wrapper">
        <a-row :gutter="24">
          <a-col :md="8" :sm="8">
            <a-form-item
              label="数据表名称"
              :labelCol="{ span: 6 }"
              :wrapperCol="{ span: 14, offset: 1 }"
            >
              <a-input v-model="tableName" placeholder="请输入数据表名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" icon="search" @click="searchQuery">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </div>
      <!-- :scroll="tableHeight" -->
      <a-table
        ref="table"
        size="middle"
        :scroll="tableheight"
        bordered
        rowKey="tableName"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="tableComment" slot-scope="tableComment, record">
          <a-input
            v-if="selectedRowKeys.indexOf(record.tableName) != -1"
            v-model="record.tableComment"
            @change="changeTableComment(record)"
          />
          <span v-else>{{ tableComment }}</span>
        </template>
      </a-table>

      <!-- <a-table
        :scroll="tableHeight"
        :pagination="false"
        ref="table"
        size="middle"
        bordered
        rowKey="tableName"
        :columns="columns"
        :dataSource="dataSource"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap">
      </a-table> -->
      <a-spin v-show="!!selectedRowKeys.length" :spinning="tableLoading">
        <a-tabs v-model="activeKey">
          <a-tab-pane key="1" tab="数据库属性" :forceRender="true">
            <DBAttributeTable
              ref="table1"
              :dataSourceProp="fieldColumns"
              :action-button="false"
              :operate-button="0"
              @finishFieldLoad="handleFinishFieldLoad"
            />
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </a-spin>
  </a-modal>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DBAttributeTable from '../tables/DBAttributeTable'
import { getAction, httpAction, postAction } from '@/api/manage'
import { pick } from 'lodash-es'

export default {
  name: 'TransDb2Online',
  components: {
    DBAttributeTable
  },
  mixins: [JeecgListMixin, mixinDevice],
  data() {
    return {
      selectedRowTables: [],
      checkedDataSource: null,
      tableheight: {
        x: 1000,
        y: document.body.clientHeight - (document.body.clientHeight > 450 ? 440 : 200)
      },
      activeKey: '1',
      // tableHeight: { x: 1000, y: (document.body.clientHeight - (document.body.clientHeight > 650 ? 650 : 400)) },
      code: null,
      dataType: 1,
      tableLoading: false,
      dataSourceAll: [],
      form: this.$form.createForm(this),
      tableName: null,
      sourceType: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      dataSourceList: [],
      modalWidth: 1200,
      visible: false,
      confirmLoading: false,
      btnLoading: false,
      dataSource: [],
      dataSourceStopSelected: false,
      fieldColumns: [],
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '表名',
          align: 'left',
          dataIndex: 'tableName'
        },
        {
          title: '备注',
          align: 'left',
          dataIndex: 'tableComment',
          scopedSlots: { customRender: 'tableComment' }
        }
      ],
      validatorRules: {
        sourceName: {
          rules: [{ required: true, message: '请输入数据源名称!' }]
        },
        tableName: {
          rules: [{ required: true, message: '请输入数据表名称!' }]
        },
        dataOrigin: {
          rules: [{ required: false, message: '请输入数据来源!' }]
        },
        dataFrequency: {
          rules: [{ required: false, message: '请输入更新频率!' }]
        },
        syncTime: {
          rules: [{ required: false, message: '请输入同步时间!' }]
        },
        dataTime: {
          rules: [{ required: false, message: '请输入账期时间!' }]
        }
      },
      selectedRowKeys: []
    }
  },
  created() {},
  methods: {
    changeTableComment(record) {
      this.selectedRowTables.forEach((item) => {
        if (item.tableName == record.tableName) {
          item.tableComment = record.tableComment
        }
      })
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    handleTest() {
      if (this.checkedDataSource == undefined || this.checkedDataSource == null) {
        this.$message.error('请选择数据源!')
        return
      }
      const loading = this.$message.loading('连接中……', 0)
      postAction('/online/cgreport/api/testConnection', this.checkedDataSource)
        .then((res) => {
          if (res.success) {
            this.$message.success('连接成功')
          } else throw new Error(res.message)
        })
        .catch((error) => {
          this.$warning({ title: '连接失败', content: error.message || error })
        })
        .finally(() => loading())
    },
    handleTableChange(pagination, filters, sorter) {
      this.ipagination = pagination
      this.loadData()
    },
    searchQuery() {
      this.ipagination.total = 0
      this.ipagination.current = 1
      this.loadData()
    },
    changeDataType() {
      this.tableName = null
      this.ipagination.current = 1
      this.ipagination.total = 0
      this.loadData()
    },
    getDataSourceList() {
      getAction('/sys/dataSource/list', { pageNo: 1, pageSize: 10000 }).then((res) => {
        if (res.success) {
          if (res.result.records != null) {
            this.dataSourceList = res.result.records.filter((item) => item.dbStatus == '1')
          } else {
            this.dataSourceList = []
          }
        } else {
          this.dataSourceList = []
        }
      })
    },
    changeDataSource(val) {
      if (val == null || val == '') {
        this.checkedDataSource = null
      } else {
        this.checkedDataSource = this.dataSourceList.find((item) => item.code == val)
      }

      if (val == null) {
        return
      }
      this.code = val
      this.ipagination.current = 1
      this.ipagination.total = 0
      this.loadData()
    },
    loadData() {
      if (
        this.dataType == null ||
        this.dataType == '' ||
        this.code == null ||
        this.code == '' ||
        this.dataSourceStopSelected
      ) {
        return
      }
      this.dataSourceAll = []
      this.dataSource = []
      this.dataSourceStopSelected = true
      getAction('/data/scCockpitDataSource/tables', {
        tableName: this.tableName,
        pageNo: this.ipagination.current,
        pageSize: this.ipagination.pageSize,
        code: this.code,
        tableType: this.dataType
      })
        .then((res) => {
          if (res.success && res.result != '未查询到数据') {
            this.ipagination.total = res.result.total
            const recordsTemp = res.result.records
            recordsTemp.forEach((item) => {
              const selectedRecord = this.selectedRowTables.find(
                (item1) => item1.tableName == item.tableName
              )
              if (selectedRecord != undefined && selectedRecord != null) {
                item.tableComment = selectedRecord.tableComment
              }
            })

            this.dataSource = res.result.records
          } else {
            this.ipagination.total = 0
            this.dataSource = []
          }
        })
        .finally(() => {
          this.dataSourceStopSelected = false
        })
    },
    show() {
      this.getDataSourceList()
      this.visible = true
      this.btnLoading = false
      this.selectedRowKeys = []
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.tableLoading = true
      this.selectedRowKeys = selectedRowKeys
      this.selectedRowTables = selectedRows
      // this.form.setFieldsValue(
      //   pick(selectedRows[0], 'dataOrigin', 'dataTime', 'dataFrequency', 'tableComment', 'syncTime')
      // )
      getAction(
        `/data/scCockpitDataSource/getTableColumns?tableName=${selectedRows[0].tableName}&code=${selectedRows[0].tableSourceCode}`
      ).then((res) => {
        this.fieldColumns = res.result // 直接传值给DBTable
      })
    },
    onClearSelected() {
      this.selectedRowTables = []
      this.selectedRowKeys = []
    },
    handleFinishFieldLoad() {
      this.tableLoading = false
    },
    resetForm() {
      this.form.resetFields()
      this.tableName = null
      this.dataSource = []
      this.ipagination.current = 1
      this.ipagination.total = 0
      this.code = null
      this.selectedRowTables = []
      this.selectedRowKeys = []
    },
    handleCancel() {
      this.resetForm()
      this.visible = false
    },
    submitData() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          let formData = Object.assign(
            this.selectedRowTables[0],
            pick(
              this.form.getFieldsValue(),
              'dataOrigin',
              'tableComment',
              'dataFrequency',
              'syncTime',
              'dataTime'
            )
          )
          formData.dataColumnList = this.fieldColumns
          formData.sourceType = this.sourceType
          formData.sourceName = this.checkedDataSource.name
          formData.isNewTable = false
          delete formData.id
          console.log('form', formData)
          postAction('/data/scCockpitDataSource/add', formData)
            .then((res) => {
              if (res.success) {
                this.$message.success('保存成功!')
                this.resetForm()
                this.visible = false
                this.$emit('ok')
              } else {
                this.$message.error(res.message)
              }
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
