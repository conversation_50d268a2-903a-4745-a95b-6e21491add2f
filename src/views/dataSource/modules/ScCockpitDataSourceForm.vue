<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form slot="detail" :form="form">
        <a-list>
          <!-- 表名、表描述、表类型 -->
          <a-list-item>
            <a-row :span="24">
              <a-col :span="12">
                <a-form-item
                  style="width: 100%"
                  label="数据源名称"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    v-decorator="['sourceName', validatorRules.sourceName]"
                    show-search
                    placeholder="请选择数据源"
                    @change="changeDataSource"
                  >
                    <a-select-option
                      v-for="item in dataSourceList"
                      :key="item.code"
                      :value="item.name"
                    >
                      {{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="数据源类型">
                  <j-dict-select-tag
                    v-decorator="['sourceType', validatorRules.sourceType]"
                    placeholder="请选择数据源类型"
                    :trigger-change="true"
                    dictCode="source_type"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="数据表名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input
                    v-decorator="[
                      'tableName',
                      {
                        rules: [
                          { required: true, message: '请输入数据表名称' },
                          {
                            validator: checkDuplicateTableMethod
                          }
                        ]
                      }
                    ]"
                    placeholder="请输入数据表名称"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="数据表注释" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input v-decorator="['tableComment']" placeholder="请输入数据表注释"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="数据来源" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input
                    v-decorator="['dataOrigin', validatorRules.dataOrigin]"
                    placeholder="请输入数据来源"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="更新频率" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <j-dict-select-tag
                    v-decorator="['dataFrequency', validatorRules.dataFrequency]"
                    type="list"
                    :trigger-change="true"
                    dictCode="data_frequency"
                    placeholder="请选择更新频率"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="同步时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input
                    v-decorator="['syncTime', validatorRules.syncTime]"
                    placeholder="请输入同步时间"
                  ></a-input>
                  <!-- <j-date
                    v-decorator="['syncTime', validatorRules.syncTime]"
                    placeholder="请选择同步时间"
                    :trigger-change="true"
                    :show-time="true"
                    date-format="YYYY-MM-DD hh:mm:ss"
                    style="width: 100%"
                  /> -->
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="账期时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input
                    v-decorator="['dataTime', validatorRules.dataTime]"
                    placeholder="请输入账期时间"
                  ></a-input>
                  <!-- <j-date
                    v-decorator="['dataTime', validatorRules.dataTime]"
                    placeholder="请选择账期时间"
                    :trigger-change="true"
                    :show-time="true"
                    date-format="YYYY-MM-DD hh:mm:ss"
                    style="width: 100%"
                  /> -->
                </a-form-item>
              </a-col>
              <!-- <a-col :span="12">
                <a-form-item label="来源文件" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <j-upload v-model="fileList" :number="1"></j-upload>
                </a-form-item>
              </a-col> -->
              <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
                <a-button @click="submitForm">提 交</a-button>
              </a-col>
            </a-row>
          </a-list-item>
        </a-list>
      </a-form>
    </j-form-container>
    <a-spin :spinning="tableLoading">
      <a-tabs v-model="activeKey">
        <a-tab-pane key="1" tab="数据库属性" :forceRender="true">
          <DBAttributeTable
            ref="table1"
            :action-button="disabled"
            :operate-button="disabled ? 0 : 1"
            :dataSourceProp="fieldColumns"
            :allowDel="!model.id"
            @added="handleAdded"
            @deleted="handleDeleted"
            @dragged="handleDragged"
            @inserted="handleInserted"
          />
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { getTreeNeedFields } from '@/views/dataSource2/util/TableUtils.js'
import { pick } from 'lodash-es'
import { validateDuplicateValue } from '@/utils/util'
import DBAttributeTable from '../tables/DBAttributeTable'
import { randomUUID, simpleDebounce } from '@/utils/util.js'
import { validateTables, VALIDATE_NO_PASSED } from '@/utils/JEditableTableUtil'

export default {
  name: 'ScCockpitDataSourceForm',
  components: { DBAttributeTable },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      fileList: [],
      tableLoading: false,
      activeKey: '1',
      fieldColumns: [],
      disableSubmit: true,
      dataSourceList: [],
      currentSelectDataSource: undefined,
      syncAllTableNowDebounce: simpleDebounce(() => {
        this.syncAllTableNowPromise()
      }, 150),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        sourceName: {
          rules: [{ required: true, message: '请选择数据源!' }]
        },
        sourceType: {
          rules: [{ required: true, message: '请选择数据源类型!' }]
        },
        tableName: {
          rules: [{ required: true, message: '请输入数据表名称!' }]
        },
        dataOrigin: {
          rules: [{ required: false, message: '请输入数据来源!' }]
        },
        dataFrequency: {
          rules: [{ required: false, message: '请输入更新频率!' }]
        },
        syncTime: {
          rules: [{ required: false, message: '请输入同步时间!' }]
        },
        dataTime: {
          rules: [{ required: false, message: '请输入账期时间!' }]
        }
      },
      url: {
        add: '/data/scCockpitDataSource/add',
        edit: '/data/scCockpitDataSource/edit',
        queryById: '/data/scCockpitDataSource/queryById',
        checkDuplicateTable: '/data/scCockpitDataSource/checkTableName'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    async edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      if (this.model.id) {
        const res = await getAction('/data/scCockpitDataSource/queryById', {
          id: this.model.id
        })
        this.fieldColumns = res.result.dataColumnList
      } else {
        this.fieldColumns = [
          {
            columnName: 'id',
            columnComment: '主键',
            dbLength: 36,
            dbPointLength: 0,
            dbDefaultVal: '',
            dbType: 'string',
            dbIsKey: 1,
            dbIsNull: 0
          }
        ]
      }
      this.visible = true
      this.getDataSourceList()
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'sourceName',
            'tableName',
            'tableComment',
            'dataOrigin',
            'dataFrequency',
            'syncTime',
            'dataTime',
            'xlsFileName',
            'sourceType'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          this.validateTableFields().then((table) => {
            that.confirmLoading = true
            let formData = Object.assign(this.model, values)
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'post'
            }
            const tableColumns = table.table1.values
            const oldColumns = this.$refs.table1.dataSource
            oldColumns.forEach((item, index) => {
              tableColumns[index].id = item.id
            })
            tableColumns.forEach((item) => {
              item.tableName = this.form.getFieldValue('tableName')
            })
            formData.tableSourceCode = this.currentSelectDataSource.code
            formData.dataColumnList = tableColumns
            formData.isNewTable = true
            console.log('表单提交数据', formData)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  this.visible = false
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          })
        }
      })
    },
    /**
     * 获取所有的表
     */
    getAllTable() {
      return Promise.all([this.getRefPromise('table1')])
    },
    /**
     * 获取指定的 $refs 对象
     * 有时候可能会遇到组件未挂载到页面中的情况，导致无法获取 $refs 中的某个对象
     * 这个方法可以等待挂载完成之后再返回 $refs 的对象，避免报错
     * <AUTHOR>
     */
    getRefPromise(name) {
      const _this = this
      return new Promise((resolve) => {
        void (function next() {
          const ref = _this.$refs[name]
          if (ref) {
            resolve(ref)
          } else {
            setTimeout(() => {
              next()
            }, 10)
          }
        })()
      })
    },
    /**
     * 当新增了的时候应立即同步
     */
    handleAdded() {
      this.syncAllTableNow()
    },
    /**
     * 当删除的时候也应立即同步
     */
    handleDeleted() {
      this.syncAllTableNow()
    },
    /**
     * 当拖动后立即同步
     */
    handleDragged(event) {
      const { oldIndex, newIndex, target } = event
      this.syncAllOrderNumNow(oldIndex, newIndex)
    },
    /**
     * 当插入后立即同步
     */
    syncAllTableNow() {
      this.syncAllTableNowDebounce()
    },
    // 立即同步所有 table （返回Promise）
    syncAllTableNowPromise() {
      return this.getAllTable().then((tables) => {
        return new Promise((resolve, reject) => {
          const [table1] = tables
          resolve()
        })
      })
    },
    handleInserted(event) {
      const { insertIndex, target } = event
      this.syncAllTableNowPromise().then((res) => {
        const oldIndex = target.$refs.editableTable.rows.length - 1
        this.syncAllOrderNumNow(oldIndex, insertIndex)
      })
    },

    /**
     * 验证并获取五个表的数据
     */
    validateTableFields() {
      const _this = this
      return new Promise((resolve, reject) => {
        this.getAllTable()
          .then((tables) => {
            const cases = []
            for (const table of tables) {
              cases.push(table.$refs.editableTable)
            }
            return validateTables(cases, true)
          })
          .then((all) => {
            const options = {}
            all.forEach((item, index) => {
              options[`table${index + 1}`] = item
            })
            resolve(options)
          })
          .catch((e = {}) => {
            // 判断表单验证是否未通过
            if (e.error === VALIDATE_NO_PASSED) {
              // 未通过就跳转到相应的tab选项卡
              _this.activeKey = (e.index + 1).toString()
            }
            reject(e)
          })
      })
    },
    validateTableName(rule, value, callback) {
      if (!value) {
        callback()
      } else {
        const part1 = new RegExp('^[a-zA-Z].*')
        const part2 = new RegExp('^[a-zA-Z]{1}_.*')
        const part3 = new RegExp('^[0-9].*')
        const part4 = new RegExp('[\u4E00-\u9FA5]')
        const part5 = new RegExp('^[0-9]*$')
        const part6 = new RegExp('^[a-zA-Z][a-zA-Z0-9_]*$')
        if (value.length > 30) {
          callback('长度不能超过30')
        } else if (!part1.test(value)) {
          callback('必须以字母开头')
        } else if (part2.test(value)) {
          callback('不能以单个字母加下划线开头')
        } else if (part3.test(value)) {
          callback('不能以数字开头')
        } else if (part4.test(value)) {
          callback('不能包含汉字')
        } else if (part5.test(value)) {
          callback('不能全部是数字')
        } else if (!part6.test(value)) {
          callback('不能包含特殊符号')
        }
      }
    },
    deleteTreeNeedField() {
      if (this.tree_field_id && this.tree_field_id.length > 0) {
        this.getAllTable().then((tables) => {
          const [table1] = tables
          table1.tableDeleteLines(this.tree_field_id)
          this.tree_field_id = []
          this.treeFieldAdded = false
        })
      }
    },
    addTreeNeedField() {
      if (!this.treeFieldAdded) {
        this.getAllTable().then((tables) => {
          const [table1] = tables
          let datas = getTreeNeedFields()
          datas = datas.filter((item) => {
            const nameList = table1.dataSource.map((o) => o.dbFieldName)
            return !nameList.includes(item.dbFieldName)
          })
          this.tree_field_id = []
          datas.forEach((newData) => {
            const uuidTemp = randomUUID() + '__tempid'
            this.tree_field_id.push(uuidTemp)
            newData.id = uuidTemp
          })
          table1.$refs.editableTable._pushByDataSource(datas)
          this.$nextTick(() => this.syncAllTableNow())
        })
        this.treeFieldAdded = true
      }
      this.$nextTick(() => {
        this.form.setFieldsValue({ treeIdField: 'has_child', treeParentIdField: 'pid' })
      })
    },
    handleChangeInIsDesForm(value) {
      this.showDesFormCode = value === 'Y'
    },
    handleChangeTableName(e) {
      this.metaTableName = e.target.value
    },
    changeDataSource(name, e) {
      this.currentSelectDataSource = this.dataSourceList.filter((item) => {
        return item.code === e.key
      })[0]
      // 当数据源改变时，如果表名已有值，则触发其校验
      if (this.form.getFieldValue('tableName')) {
        this.form.validateFields(['tableName'], { force: true })
      }
    },
    getDataSourceList() {
      getAction('/sys/dataSource/list', { pageNo: 1, pageSize: 10000 }).then((res) => {
        if (res.success) {
          if (res.result.records != null) {
            this.dataSourceList = res.result.records.filter((item) => item.dbStatus == '1')
            // 新增：如果数据源列表不为空，并且是新增操作，则默认选中第一个
            if (!this.model.id && this.dataSourceList.length > 0) {
              this.model.sourceName = this.dataSourceList[0].name
              this.currentSelectDataSource = this.dataSourceList[0]
              // 同时更新表单的显示值
              this.$nextTick(() => {
                this.form.setFieldsValue({ sourceName: this.dataSourceList[0].name })
              })
            }
            // 原有的编辑模式下的选中逻辑
            if (this.model.id && this.model.tableSourceCode) {
              // 确保 tableSourceCode 也存在
              this.currentSelectDataSource = this.dataSourceList.filter((item) => {
                return item.code === this.model.tableSourceCode
              })[0]
            }
          } else {
            this.dataSourceList = []
          }
        } else {
          this.dataSourceList = []
        }
      })
    },
    checkDuplicateTableMethod(rule, value, callback) {
      if (!value || value === this.model.tableName) callback()
      else {
        getAction(this.url.checkDuplicateTable, {
          tableName: value,
          tableSource: this.currentSelectDataSource.code
        }).then((res) => {
          if (res.code === 500) callback('存在重复表名')
          else callback()
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .ant-modal-content {
  max-height: 640px;
}
</style>
