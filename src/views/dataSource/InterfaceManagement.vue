<template>
  <div>
    <!--使用draggable组件-->
    <div class="itxst">
      <div class="col">
        <div class="title">把下面元素拖拽到B组试试看</div>
        <draggable
          v-model="arrCharts"
          :group="groupA"
          animation="300"
          dragClass="dragClass"
          ghostClass="ghostClass"
          chosenClass="chosenClass"
          @start="onStart"
          @end="onEnd"
        >
          <transition-group :style="style">
            <div v-for="item in arrCharts" :key="item.id" class="item">
              <div :id="item.id" class="echarts-container"></div>
            </div>
          </transition-group>
        </draggable>
      </div>
      <div class="col">
        <div class="title">B组（本组是个空数组）</div>
        <draggable
          v-model="arrCharts2"
          :group="groupB"
          animation="300"
          dragClass="dragClass"
          ghostClass="ghostClass"
          chosenClass="chosenClass"
          @start="onStart"
          @end="onEnd"
        >
          <transition-group :style="style">
            <div v-for="item in arrCharts2" :key="item.id" class="item">
              <div :id="item.id" class="echarts-container"></div>
            </div>
          </transition-group>
        </draggable>
      </div>
    </div>
    <a-button class="confirm" @click="handleConfirm">确认</a-button>
  </div>
</template>
<script>
//导入draggable组件
import * as echarts from 'echarts'
import draggable from 'vuedraggable'
export default {
  //注册draggable组件
  components: {
    draggable,
  },
  data() {
    return {
      drag: false,
      groupA: {
        name: 'site',
        pull: true, //可以拖从
        put: true, //可以拖出
      },
      groupB: {
        name: 'site',
        pull: true,
        put: true,
      },
      groupC: {
        name: 'site',
        pull: true,
        put: false,
      },
      //定义要被拖拽对象的数组
      arr1: [
        { id: 1, name: 'www.itxst.com' },
        { id: 2, name: 'www.jd.com' },
        { id: 3, name: 'www.baidu.com' },
        { id: 4, name: 'www.taobao.com' },
      ],
      arr2: [], //空数组
      arrCharts: [{ id: 'chart1' }, { id: 'chart2' }],
      arrCharts2: [{ id: 'chart3' }, { id: 'chart4' }],
      arr3: [{ id: 'null', name: '空' }], //空数组
      //空数组之在的样式，设置了这个样式才能拖入
      style: 'min-height:800px;display: block;',
      seriesData: [],
      chartInstance1: null,
      chartInstance2: null,
      chartInstance3: null,
      chartInstance4: null,
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chartInstance1 = echarts.init(document.querySelector('#chart1'))
      this.chartInstance2 = echarts.init(document.querySelector('#chart2'))
      this.chartInstance3 = echarts.init(document.querySelector('#chart3'))
      this.chartInstance4 = echarts.init(document.querySelector('#chart4'))
      const that = this
      const option = {
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: that.seriesData,
            type: 'bar',
          },
        ],
      }
      this.chartInstance1.setOption(option)
      this.chartInstance2.setOption(option)
      this.chartInstance3.setOption(option)
      this.chartInstance4.setOption(option)
    },
    //开始拖拽事件
    onStart(e) {
      console.log('start', e)
      this.drag = true
    },
    //拖拽结束事件
    onEnd() {
      this.drag = false
      if (!this.arr3.length) this.arr3 = [{ id: `null-${Math.random(1000)}`, name: '空' }]
      const itemArr = document.querySelectorAll('.item')
      this.seriesData = [100, 200, 300, 400, 100, 200, 300]
      this.initChart()
    },
    handleConfirm() {
      this.arr1.forEach((item) => {
        const id = item.id
        const dom = document.getElementById(id)
        console.log('dom', dom.innerHTML)
      })
    },
  },
}
</script>
<style scoped>
/*定义要拖拽元素的样式*/
.ghostClass {
  background-color: blue !important;
}
.chosenClass {
  background-color: red !important;
  opacity: 1 !important;
}
.dragClass {
  background-color: blueviolet !important;
  opacity: 1 !important;
  box-shadow: none !important;
  outline: none !important;
  background-image: none !important;
}
.itxst {
  margin: 10px;
}
.title {
  padding: 6px 12px;
}
.col,
.col-null {
  width: 40%;
  flex: 1;
  padding: 10px;
  border: solid 1px #eee;
  border-radius: 5px;
  float: left;
}
.col + .col {
  margin-left: 10px;
}

.item {
  padding: 6px 12px;
  margin: 0px 10px 0px 10px;
  border: solid 1px #eee;
  background-color: #f1f1f1;
}
.item:hover {
  background-color: #fdfdfd;
  cursor: move;
}
.item + .item {
  border-top: none;
  margin-top: 6px;
}
.col-null {
  position: fixed;
  bottom: 0;
  right: 0;
}
.echarts-container {
  height: 200px;
  width: 80%;
}
</style>
