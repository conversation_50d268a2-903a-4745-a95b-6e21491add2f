<template>
  <div>
    <div
      style="position: relative; background-size: 100% 100%"
      :style="{
        width: conWidth + 'px',
        height: conHeight + 'px',
        backgroundColor: designCache.bgColor,
        backgroundImage: designCache.topicBackground
          ? 'url(' + getFileAccessHttpUrl(designCache.topicBackground) + ')'
          : designCache.defaultBgImage
          ? 'url(' + getFileAccessHttpUrl(designCache.defaultBgImage) + ')'
          : 'none'
      }"
    >
      <div
        v-for="item in componentData"
        v-show="item.cptOption.attribute.visible"
        :key="item.id"
        style="position: absolute"
        :style="{
          width: Math.round(item.cptWidth) + 'px',
          height: Math.round(item.cptHeight) + 'px',
          top: Math.round(item.cptY) + 'px',
          left: Math.round(item.cptX) + 'px',
          zIndex: item.cptZ
        }"
        @click="handleEvent($event, item)"
        @dblclick="handleEvent($event, item)"
        @contextmenu.prevent="handleEvent($event, item)"
        @mousemove="handleEvent($event, item)"
      >
        <!-- activeId用于将当前专题id传入菜单组件 -->
        <component
          :is="item.cptKey"
          :id="item.id"
          :key="item.id"
          :ref="item.id"
          :width="Math.round(item.cptWidth)"
          :height="Math.round(item.cptHeight)"
          :option="item.cptOption"
          :activeId="currentTopicId"
          :cssData="item.cptCssData"
          :indexId="item.cptIndexId"
          :children="item.children"
          :isPreview="true"
          @reload="loadCacheData"
          @changeActiveId="loadActiveTopicData"
          @popup="(e) => handlePopup(e, item)"
        />
      </div>
    </div>
    <!-- <a-button
      type="primary"
      style="
        width: 120px;
        position: fixed;
        bottom: 0;
        left: calc(50% - 120px);
        letter-spacing: 1px;
        padding: 0 10px;
        border-radius: 4px 4px 0 0;
      "
      @click="backToPremTopic"
      >跳转至当前专题</a-button
    > -->
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { fileUrl } from '/env'
import { getAction, getFileAccessHttpUrl } from '@/api/manage'

export default {
  name: 'PreviewIndex',
  data() {
    return {
      fileUrl: fileUrl,
      designCache: {},
      designCachePrem: {}, // 用于跳转回未保存的初始专题
      windowWidth: 0,
      windowHeight: 0,
      conWidth: 0,
      conHeight: 0,
      containerScale: 1,
      authCodeDialogVisible: false,
      viewCode: '',
      relWorkbench: '',
      currentTopicId: '',
      isDoubleClick: false
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['componentData'])
  },
  mounted() {
    const that = this
    that.loadCacheData()
    window.onresize = () => {
      return (() => {
        that.loadSize()
      })()
    }
  },
  methods: {
    loadCacheData() {
      const path = this.$route.path
      const id = this.$route.params.topicId // 专题id
      this.currentTopicId = id
      this.relWorkbench = this.$route.params.workbenchId // 工作台id
      if (this.relWorkbench) {
        getAction('/workbench/scCockpitWorkbench/queryById', { id: this.relWorkbench }).then(
          (res) => {
            if (res.success) {
              this.designCache = Object.assign(this.designCache, {
                defaultMapUrl: res.result.mapComponent,
                defaultMenuUrl: res.result.menuComponent,
                defaultBgImage: res.result.bgImage
              })
              // 加载默认组件
            }
          }
        )
      }
      if (path.includes('/chart/preview')) {
        let designCache = JSON.parse(localStorage.getItem('designCache'))
        this.designCachePrem = designCache
        this.loadDesign(designCache, false)
      }
    },
    loadDesign(design, componentPares) {
      console.log('🚀 ~ loadDesign ~ design:', design)
      if (componentPares) {
        design.components = JSON.parse(design.components)
      }
      document.title = design.topicName || design.moduleName
      this.designCache = design
      this.componentData = design.components
      this.loadSize()
    },
    loadSize() {
      this.windowWidth = document.documentElement.clientWidth
      this.windowHeight = document.documentElement.clientHeight
      this.containerScale = Math.round((this.windowWidth / this.designCache.scaleX) * 100) / 100
      this.conWidth = this.designCache.scaleX
      this.conHeight = this.designCache.scaleY
    },
    loadActiveTopicData(id) {
      //   // 点击菜单专题按钮
      //   getAction('/topic/scCockpitTopicInfo/queryById', { id }).then((topicRes) => {
      //     if (topicRes.success) {
      //       this.currentTopicId = id
      //       const { topicJson, ...rest } = topicRes.result
      //       const topicInfo = JSON.parse(topicJson)
      //       let designData = {
      //         ...rest,
      //         scaleX: topicInfo.topicWidth || topicInfo.moduleWidth,
      //         scaleY: topicInfo.topicHeight || topicInfo.moduleLength,
      //         defaultMapUrl: this.designCache.defaultMapUrl,
      //         defaultMenuUrl: this.designCache.defaultMenuUrl,
      //         defaultBgImage: this.designCache.defaultBgImage
      //       }
      //       designData.components = topicInfo.components || []
      //       this.loadDesign(designData, false)
      //     } else {
      //       this.$message.warning('专题信息查询失败')
      //     }
      //   })
      // },
      // backToPremTopic() {
      //   this.designCache = this.designCachePrem
      //   this.currentTopicId = this.$route.params.topicId
      //   this.loadDesign(this.designCache, false)
    },
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    handleEvent(e, item) {
      if (!item.events || !Array.isArray(item.events)) return
      const components = {}
      this.componentData.forEach((item) => {
        components[item.id] = this.$refs[item.id]?.[0] || this.$refs[item.id]
      })

      // 如果是双击事件，直接执行
      if (e.type === 'dblclick') {
        this.isDoubleClick = true
        for (const event of item.events) {
          if (event.triggerType === e.type) {
            this.dispatchEvent(event, e, components)
          }
        }
        return
      }

      // 如果是点击事件，延迟执行，避免覆盖双击事件
      if (e.type === 'click') {
        setTimeout(() => {
          // 检查是否已经触发了双击事件
          if (!this.isDoubleClick) {
            for (const event of item.events) {
              if (event.triggerType === e.type) {
                this.dispatchEvent(event, e, components)
              }
            }
          }
          this.isDoubleClick = false
        }, 300) // 延迟 300ms
      }
    },
    dispatchEvent(event, e, components) {
      try {
        if (event.responseType === 'link') {
          if (event.linkType === 'external') {
            window.open(event.linkUrl, '_blank')
          } else {
            this.$router.push(event.linkUrl)
          }
        } else if (event.responseType === 'jsevent') {
          // 执行自定义JS代码
          const fn = new Function('e', 'components', event.jsCode)
          fn(e, components)
        } else if (event.responseType === 'popup') {
          // 打开弹窗
          const popupComponent = components[event.popupComponentId]
          popupComponent.open()
        }
      } catch (error) {
        this.$message.error('事件执行失败')
        console.error('事件执行失败:', error)
      }
    },
    handlePopup(e, cpt) {
      cpt.cptOption.attribute.visible = e
    }
  }
}
</script>

<style scoped>
.bounce-enter-active {
  transition: all 1s;
  /*animation: bounce-in 1s;*/
}
.bounce-enter {
  opacity: 0;
  transform: scale(0.5);
}
@keyframes bounce-in {
  0% {
    transform: scale(0);
  }
  25% {
    transform: scale(0.5);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}
</style>
