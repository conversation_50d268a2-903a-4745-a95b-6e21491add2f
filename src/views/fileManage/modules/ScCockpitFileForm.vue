<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="disabled">
      <a-form-model slot="detail" ref="form" :model="form" :rules="validatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              label="图片名称"
              prop="fileName"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input v-model="form.fileName" placeholder="请输入文件名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              prop="filePath"
              label="上传图片"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <j-image-upload v-model="form.filePath"></j-image-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="所属专题" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="form.topic" placeholder="请输入所属专题"></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction } from '@/api/manage'

export default {
  name: 'ScCockpitFileForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false
    },
    //表单模式：true流程表单 false普通表单
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: {
        fileName: '',
        topic: '',
        filePath: ''
      },
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        fileName: { rules: [{ required: true, message: '请输入图片名称!', trigger: 'change' }] },
        filePath: { rules: [{ required: true, message: '请上传图片!', trigger: 'change' }] }
      },
      url: {
        add: '/file/scCockpitFile/add',
        edit: '/file/scCockpitFile/edit',
        queryById: '/file/scCockpitFile/queryById'
      }
    }
  },
  created() {},
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form = {
        fileName: '',
        topic: '',
        filePath: ''
      }
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form = this.model
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = 'post'
          if (!this.model.id) {
            httpurl += this.url.add
          } else {
            httpurl += this.url.edit
          }
          console.log('表单提交数据', this.form)
          httpAction(httpurl, this.form, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
