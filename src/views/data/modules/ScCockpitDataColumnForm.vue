<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form slot="detail" :form="form">
        <a-row>
          <a-col :span="24">
            <a-form-item label="数据源id" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['sourceId']" placeholder="请输入数据源id"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="数据表名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['tableName']" placeholder="请输入数据表名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['columnName']" placeholder="请输入字段名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段注释" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['columnComment']" placeholder="请输入字段注释"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段jsonpath" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['columnJsonPath']" placeholder="请输入字段jsonpath"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段文件头" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['fileHeader']" placeholder="请输入字段文件头"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['dbType']" placeholder="请输入字段类型"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="数据库字段长度" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                v-decorator="['dbLength']"
                placeholder="请输入数据库字段长度"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="小数点" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                v-decorator="['dbPointLength']"
                placeholder="请输入小数点"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="表字段默认值" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['dbDefaultVal']" placeholder="请输入表字段默认值"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="是否主键 0否 1是" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                v-decorator="['dbIsKey']"
                placeholder="请输入是否主键 0否 1是"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="是否允许为空0否 1是" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                v-decorator="['dbIsNull']"
                placeholder="请输入是否允许为空0否 1是"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { pick } from 'lodash-es'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'ScCockpitDataColumnForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/data/scCockpitDataColumn/add',
        edit: '/data/scCockpitDataColumn/edit',
        queryById: '/data/scCockpitDataColumn/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'sourceId',
            'tableName',
            'columnName',
            'columnComment',
            'columnJsonPath',
            'fileHeader',
            'dbType',
            'dbLength',
            'dbPointLength',
            'dbDefaultVal',
            'dbIsKey',
            'dbIsNull'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'sourceId',
          'tableName',
          'columnName',
          'columnComment',
          'columnJsonPath',
          'fileHeader',
          'dbType',
          'dbLength',
          'dbPointLength',
          'dbDefaultVal',
          'dbIsKey',
          'dbIsNull'
        )
      )
    }
  }
}
</script>
