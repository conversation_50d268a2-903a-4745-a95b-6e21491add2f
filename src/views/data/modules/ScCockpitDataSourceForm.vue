<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form slot="detail" :form="form">
        <a-row>
          <a-col :span="24">
            <a-form-item label="更新日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                v-decorator="['updateTime']"
                placeholder="请选择更新日期"
                :trigger-change="true"
                :show-time="true"
                date-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="来源名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['sourceName', validatorRules.sourceName]"
                placeholder="请输入来源名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="数据表名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['tableName', validatorRules.tableName]"
                placeholder="请输入数据表名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="数据表注释" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['tableComment']" placeholder="请输入数据表注释"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="数据来源" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['dataOrigin', validatorRules.dataOrigin]"
                placeholder="请输入数据来源"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="更新频率" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                v-decorator="['dataFrequency', validatorRules.dataFrequency]"
                type="list"
                :trigger-change="true"
                dictCode=""
                placeholder="请选择更新频率"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="同步时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                v-decorator="['syncTime', validatorRules.syncTime]"
                placeholder="请选择同步时间"
                :trigger-change="true"
                :show-time="true"
                date-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="账期时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                v-decorator="['dataTime', validatorRules.dataTime]"
                placeholder="请选择账期时间"
                :trigger-change="true"
                :show-time="true"
                date-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="文件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['xlsFileName']" placeholder="请输入文件名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { pick } from 'lodash-es'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'ScCockpitDataSourceForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        sourceName: {
          rules: [{ required: true, message: '请输入来源名称!' }]
        },
        tableName: {
          rules: [{ required: true, message: '请输入数据表名称!' }]
        },
        dataOrigin: {
          rules: [{ required: true, message: '请输入数据来源!' }]
        },
        dataFrequency: {
          rules: [{ required: true, message: '请输入更新频率!' }]
        },
        syncTime: {
          rules: [{ required: true, message: '请输入同步时间!' }]
        },
        dataTime: {
          rules: [{ required: true, message: '请输入账期时间!' }]
        }
      },
      url: {
        add: '/data/scCockpitDataSource/add',
        edit: '/data/scCockpitDataSource/edit',
        queryById: '/data/scCockpitDataSource/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'updateTime',
            'sourceName',
            'tableName',
            'tableComment',
            'dataOrigin',
            'dataFrequency',
            'syncTime',
            'dataTime',
            'xlsFileName'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'updateTime',
          'sourceName',
          'tableName',
          'tableComment',
          'dataOrigin',
          'dataFrequency',
          'syncTime',
          'dataTime',
          'xlsFileName'
        )
      )
    }
  }
}
</script>
