<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    cancelText="关闭"
    @ok="handleOk"
    @cancel="handleCancel">
    <sc-cockpit-data-column-form ref="realForm" :disabled="disableSubmit" @ok="submitCallback"></sc-cockpit-data-column-form>
  </j-modal>
</template>

<script>

  import ScCockpitDataColumnForm from './ScCockpitDataColumnForm'
  export default {
    name: 'ScCockpitDataColumnModal',
    components: {
      ScCockpitDataColumnForm
    },
    data () {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>