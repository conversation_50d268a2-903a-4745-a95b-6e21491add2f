<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="来源名称">
              <a-input v-model="queryParam.sourceName" placeholder="请输入来源名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="数据表名称">
              <a-input v-model="queryParam.tableName" placeholder="请输入数据表名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" icon="search" @click="searchQuery">查询</a-button>
              <a-button type="primary" icon="reload" style="margin-left: 8px" @click="searchReset"
                >重置</a-button
              >
              <a style="margin-left: 8px" @click="handleToggleSearch">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('数字驾驶舱数据源')"
        >导出</a-button
      >
      <a-upload
        name="file"
        :showUploadList="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        @change="handleImportExcel"
      >
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query
        ref="superQueryModal"
        :fieldList="superFieldList"
        @handleSuperQuery="handleSuperQuery"
      ></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)"
          >
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a @click="handleViewData(record)">查看数据</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <sc-cockpit-data-source-modal ref="modalForm" @ok="modalFormOk"></sc-cockpit-data-source-modal>
    <data-edit ref="dataEdit" @ok="modalFormOk"></data-edit>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ScCockpitDataSourceModal from './modules/ScCockpitDataSourceModal'
import DataEdit from '@/views/data-edit/DataEdit'

export default {
  name: 'ScCockpitDataSourceList',
  components: {
    ScCockpitDataSourceModal,
    DataEdit
  },
  mixins: [JeecgListMixin, mixinDevice],
  data() {
    return {
      description: '数字驾驶舱数据源管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '来源名称',
          align: 'center',
          dataIndex: 'sourceName'
        },
        {
          title: '数据表名称',
          align: 'center',
          dataIndex: 'tableName'
        },
        {
          title: '数据表注释',
          align: 'center',
          dataIndex: 'tableComment'
        },
        {
          title: '数据来源',
          align: 'center',
          dataIndex: 'dataOrigin'
        },
        {
          title: '更新频率',
          align: 'center',
          dataIndex: 'dataFrequency_dictText'
        },
        {
          title: '同步时间',
          align: 'center',
          dataIndex: 'syncTime'
        },
        {
          title: '账期时间',
          align: 'center',
          dataIndex: 'dataTime'
        },
        {
          title: '文件名称',
          align: 'center',
          dataIndex: 'xlsFileName'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/data/scCockpitDataSource/list',
        delete: '/data/scCockpitDataSource/delete',
        deleteBatch: '/data/scCockpitDataSource/deleteBatch',
        exportXlsUrl: '/data/scCockpitDataSource/exportXls',
        importExcelUrl: 'data/scCockpitDataSource/importExcel'
      },
      dictOptions: {},
      superFieldList: []
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    this.getSuperFieldList()
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'datetime', value: 'updateTime', text: '更新日期' })
      fieldList.push({ type: 'string', value: 'sourceName', text: '来源名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'tableName', text: '数据表名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'tableComment', text: '数据表注释', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dataOrigin', text: '数据来源', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dataFrequency', text: '更新频率', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'syncTime', text: '同步时间' })
      fieldList.push({ type: 'datetime', value: 'dataTime', text: '账期时间' })
      fieldList.push({ type: 'string', value: 'xlsFileName', text: '文件名称', dictCode: '' })
      this.superFieldList = fieldList
    },
    /**
     * @description 查看数据
     * @param {Object} record
     * @returns void
     */
    handleViewData(record) {
      this.$refs.dataEdit.handleOpenData(record, {
        dataSource: record.dataSource,
        serviceCode: record.serviceCode,
        tableName: record.tableName
      })
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
