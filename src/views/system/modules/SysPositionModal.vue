<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    cancelText="关闭"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="职务编码">
          <a-input
            v-decorator="['code', validatorRules.code]"
            placeholder="请输入职务编码"
            :read-only="readOnly"
          />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="职务名称">
          <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入职务名称" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="职级">
          <j-dict-select-tag
            v-decorator="['postRank', validatorRules.postRank]"
            placeholder="请选择职级"
            :triggerChange="true"
            dictCode="position_rank"
          />
        </a-form-item>
        <!--<a-form-item-->
        <!--  :labelCol="labelCol"-->
        <!--  :wrapperCol="wrapperCol"-->
        <!--  label="公司id">-->
        <!--  <a-input placeholder="请输入公司id" v-decorator="['companyId', {}]"/>-->
        <!--</a-form-item>-->
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { pick } from 'lodash-es'
import { httpAction } from '@/api/manage'
import { duplicateCheck } from '@/api/api'
import JDictSelectTag from '@/components/dict/JDictSelectTag'

let validatorCodeTimer = null

export default {
  name: 'SysPositionModal',
  components: { JDictSelectTag },
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        code: {
          rules: [
            { required: true, message: '请输入职务编码' },
            {
              validator: (rule, value, callback) => {
                // 函数消抖的简单实现，防止一段时间内发送多次请求
                if (validatorCodeTimer) {
                  // 停止上次开启的定时器
                  clearTimeout(validatorCodeTimer)
                }
                validatorCodeTimer = setTimeout(() => {
                  duplicateCheck({
                    tableName: 'sys_position',
                    fieldName: 'code',
                    fieldVal: value,
                    dataId: this.model.id
                  })
                    .then((res) => {
                      if (res.success) {
                        callback()
                      } else {
                        callback(res.message)
                      }
                    })
                    .catch(console.error)
                }, 300)
              }
            }
          ]
        },
        name: { rules: [{ required: true, message: '请输入职务名称' }] },
        postRank: { rules: [{ required: true, message: '请选择职级' }] }
      },
      url: {
        add: '/sys/position/add',
        edit: '/sys/position/edit'
      },
      readOnly: false
    }
  },
  created() {},
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      if (record.id) {
        this.readOnly = true
      } else {
        this.readOnly = false
      }
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'code',
            'name',
            'postRank'
            // 'companyId'
          )
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }

          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped></style>
