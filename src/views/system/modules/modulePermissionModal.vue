<template>
  <a-drawer
    :title="title"
    :maskClosable="true"
    width="650"
    placement="right"
    :closable="true"
    :visible="visible"
    style="overflow: auto; padding-bottom: 53px"
    @close="close"
  >
    <a-form>
      <a-form-item label="所拥有的模块权限">
        <a-select
          v-model="currentWorkbenchId"
          style="width: 100%; margin-top: 10px"
          placeholder="请选择工作台"
          @change="(currentWorkbenchId) => loadTopicList(currentWorkbenchId, true)"
        >
          <a-select-option
            v-for="item in workbenchList"
            :key="item.id"
            :value="item.id"
            :label="item.workbenchName"
            >{{ item.workbenchName }}</a-select-option
          >
        </a-select>
        <a-spin
          class="tree"
          :style="{ overflow: treeLoading ? 'hidden' : 'auto' }"
          :spinning="treeLoading"
        ></a-spin>
        <a-tree
          checkable
          :checkedKeys="checkedKeys"
          :treeData="treeData"
          :selectedKeys="selectedKeys"
          :expandedKeys="expandedKeysss"
          :checkStrictly="checkStrictly"
          :replaceFields="replaceFields"
          @check="onCheck"
          @expand="onExpand"
          @select="onTreeNodeSelect"
        >
        </a-tree>
      </a-form-item>
    </a-form>

    <div class="drawer-bootom-button">
      <a-dropdown style="float: left" :trigger="['click']" placement="topCenter">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="switchCheckStrictly(1)">父子关联</a-menu-item>
          <a-menu-item key="2" @click="switchCheckStrictly(2)">取消关联</a-menu-item>
          <a-menu-item key="3" @click="checkALL">全部勾选</a-menu-item>
          <a-menu-item key="4" @click="cancelCheckALL">取消全选</a-menu-item>
          <a-menu-item key="5" @click="expandAll">展开所有</a-menu-item>
          <a-menu-item key="6" @click="closeAll">合并所有</a-menu-item>
        </a-menu>
        <a-button> 树操作 <a-icon type="up" /> </a-button>
      </a-dropdown>
      <a-popconfirm title="确定放弃编辑？" okText="确定" cancelText="取消" @confirm="close">
        <a-button style="margin-right: 0.8rem">取消</a-button>
      </a-popconfirm>
      <a-button
        type="primary"
        :loading="loading"
        ghost
        style="margin-right: 0.8rem"
        @click="handleSubmit(false)"
        >仅保存</a-button
      >
      <a-button type="primary" :loading="loading" @click="handleSubmit(true)">保存并关闭</a-button>
    </div>

    <role-datarule-modal ref="datarule"></role-datarule-modal>
  </a-drawer>
</template>
<script>
import RoleDataruleModal from './RoleDataruleModal.vue'
import { getAction } from '@/api/manage'

export default {
  name: 'RoleModal',
  components: {
    RoleDataruleModal
  },
  data() {
    return {
      roleId: '',
      treeData: [],
      defaultCheckedKeys: [],
      checkedKeys: [],
      expandedKeysss: [],
      allTreeKeys: [],
      autoExpandParent: true,
      checkStrictly: false,
      title: '模块角色权限配置',
      visible: false,
      loading: false,
      selectedKeys: [],
      workbenchList: [],
      currentWorkbenchId: '',
      replaceFields: { title: 'treeName', key: 'id', children: 'childTreeList' },
      treeLoading: false,
      url: {
        listWorkbench: '/workbench/scCockpitWorkbench/allWorkbenchList',
        treeListTopic: '/workbench/scCockpitWorkbench/treeList'
      }
    }
  },
  methods: {
    onTreeNodeSelect(id) {
      if (id && id.length > 0) {
        this.selectedKeys = id
      }
      // this.$refs.datarule.show(this.selectedKeys[0], this.roleId)
    },
    onCheck(o) {
      if (this.checkStrictly) {
        this.checkedKeys = o.checked
      } else {
        this.checkedKeys = o
      }
    },
    show(roleId) {
      this.roleId = roleId
      this.visible = true
      this.loadWorkbenchList()
    },
    close() {
      this.reset()
      this.$emit('close')
      this.visible = false
    },
    onExpand(expandedKeys) {
      this.expandedKeysss = expandedKeys
      this.autoExpandParent = false
    },
    reset() {
      this.expandedKeysss = []
      this.checkedKeys = []
      this.defaultCheckedKeys = []
      this.loading = false
    },
    expandAll() {
      this.expandedKeysss = this.allTreeKeys
    },
    closeAll() {
      this.expandedKeysss = []
    },
    checkALL() {
      this.checkedKeys = this.allTreeKeys
    },
    cancelCheckALL() {
      //this.checkedKeys = this.defaultCheckedKeys
      this.checkedKeys = []
    },
    switchCheckStrictly(v) {
      if (v == 1) {
        this.checkStrictly = false
      } else if (v == 2) {
        this.checkStrictly = true
      }
    },
    handleCancel() {
      this.close()
    },
    handleSubmit(exit) {
      let that = this
      let params = {
        roleId: that.roleId,
        permissionIds: that.checkedKeys.join(','),
        lastpermissionIds: that.defaultCheckedKeys.join(',')
      }
      that.loading = true
      console.log('请求参数：', params)
      // saveRolePermission(params).then((res) => {
      //   if (res.success) {
      //     that.$message.success(res.message)
      //     that.loading = false
      //     if (exit) {
      //       that.close()
      //     }
      //   } else {
      //     that.$message.error(res.message)
      //     that.loading = false
      //     if (exit) {
      //       that.close()
      //     }
      //   }
      //   this.loadData()
      // })
    },
    loadTopicList(currentWorkbenchId, clearCurrentSelect = false) {
      if (clearCurrentSelect) {
        this.currentTreeNode = null
        this.currentTopicModuleId = ''
        this.currentTopicModuleInfo = {}
      }
      this.currentWorkbenchId = currentWorkbenchId
      getAction(this.url.treeListTopic, { workbenchId: currentWorkbenchId })
        .then((res) => {
          if (res.success) {
            this.treeData = res.result
          } else {
            this.$message.error(res.message || '加载专题列表失败')
            this.treeData = []
          }
        })
        .finally(() => {
          this.treeLoading = false
        })
    },
    loadWorkbenchList() {
      this.treeLoading = true
      const params = {
        pageNo: 1,
        pageSize: 10
      }
      getAction(this.url.listWorkbench, params).then((res) => {
        if (res.success) {
          this.workbenchList = res.result
          this.currentWorkbenchId = this.workbenchList[0]?.id
          this.loadTopicList(this.currentWorkbenchId)
        } else {
          this.$message.error(res.message || '加载工作台列表失败')
          this.workbenchList = []
          this.treeData = []
          this.currentWorkbenchId = undefined
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.drawer-bootom-button {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}
</style>
