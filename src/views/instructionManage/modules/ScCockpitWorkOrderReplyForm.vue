<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" slot="detail" :model="form" :rules="validatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              prop="replyContent"
              label="回复内容"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-textarea
                v-model="form.replyContent"
                :auto-size="{ minRows: 4, maxRows: 10 }"
                placeholder="请输入回复内容"
              ></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              prop="replyFile"
              label="回复文件"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <j-upload v-model="form.replyFile"></j-upload>
            </a-form-model-item>
          </a-col>
          <!-- <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col> -->
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'

export default {
  name: 'ScCockpitWorkOrderReplyForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: {
        workOrderId: '',
        replyFile: '',
        replyContent: ''
      },
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        replyContent: [{ required: true, message: '回复内容不能为空' }]
      },
      url: {
        add: '/workorder/scCockpitWorkOrderReply/add',
        edit: '/workorder/scCockpitWorkOrderReply/edit',
        queryById: '/workorder/scCockpitWorkOrderReply/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form = {
          workOrderId: this.model.id,
          replyContent: '',
          replyFile: ''
        }
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = this.url.add
          let method = 'post'
          console.log('表单提交数据', this.form)
          httpAction(httpurl, this.form, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
