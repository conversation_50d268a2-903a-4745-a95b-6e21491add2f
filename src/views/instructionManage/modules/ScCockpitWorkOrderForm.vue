<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model slot="detail" ref="form" :model="form">
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              pro="workOrderTitle"
              label="批示名称"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input v-model="form.workOrderTitle" placeholder="请输入批示名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              prop="workOrderContent"
              label="批示内容"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-textarea
                v-model="form.workOrderContent"
                placeholder="请输入批示内容"
                autoSize
              ></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              prop="workOrderFile"
              label="批示截图"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <img :src="form.workOrderFile" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'

export default {
  name: 'ScCockpitWorkOrderForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: {
        workOrderTitle: '',
        workOrderContent: '',
        workOrderFile: ''
      },
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/workorder/scCockpitWorkOrder/add',
        edit: '/workorder/scCockpitWorkOrder/edit',
        queryById: '/workorder/scCockpitWorkOrder/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form = pick(
          this.model,
          'workOrderTitle',
          'workOrderContent',
          'workOrderFile',
          'workOrderStatus'
        )
        console.log(this.form)
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'post'
          }
          console.log('表单提交数据', that.form)
          httpAction(httpurl, that.form, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
