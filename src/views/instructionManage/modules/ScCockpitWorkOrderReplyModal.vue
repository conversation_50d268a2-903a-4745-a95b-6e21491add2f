<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    cancelText="关闭"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <sc-cockpit-work-order-reply-form
      ref="realForm"
      :disabled="disableSubmit"
      @ok="submitCallback"
    ></sc-cockpit-work-order-reply-form>
  </j-modal>
</template>

<script>
import ScCockpitWorkOrderReplyForm from './ScCockpitWorkOrderReplyForm'
export default {
  name: 'ScCockpitWorkOrderReplyModal',
  components: {
    ScCockpitWorkOrderReplyForm
  },
  data() {
    return {
      title: '回复',
      width: 800,
      visible: false,
      disableSubmit: false
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
