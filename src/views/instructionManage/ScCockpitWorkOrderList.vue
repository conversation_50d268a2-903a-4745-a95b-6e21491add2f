<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24"> </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('领导驾驶舱-批示')"
        >导出</a-button
      >
      <a-upload
        name="file"
        :showUploadList="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        @change="handleImportExcel"
      >
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query
        ref="superQueryModal"
        :fieldList="superFieldList"
        @handleSuperQuery="handleSuperQuery"
      ></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else-if="!text.includes('base64')"
            :src="getImgView(text)"
            title="预览"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic; cursor: pointer"
          />
          <img
            v-else
            :src="text"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic; cursor: pointer"
            @click="() => (previewImg = text)"
          />
          <el-image-viewer
            v-if="!!previewImg"
            :on-close="() => (previewImg = '')"
            :url-list="[previewImg]"
          />
        </template>
        <template slot="workOrderStatus" slot-scope="text">
          <span :style="{ color: text == 1 ? 'red' : 'limegreen' }">{{
            text == 1 ? '未回复' : '已处理'
          }}</span>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="handleReply(record)">回复</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <sc-cockpit-work-order-modal ref="modalForm" @ok="modalFormOk"></sc-cockpit-work-order-modal>
    <ScCockpitWorkOrderReplyModal ref="replyForm" @ok="modalFormOk"></ScCockpitWorkOrderReplyModal>
  </a-card>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ScCockpitWorkOrderModal from './modules/ScCockpitWorkOrderModal'
import ScCockpitWorkOrderReplyModal from './modules/ScCockpitWorkOrderReplyModal'
export default {
  name: 'ScCockpitWorkOrderList',
  components: {
    ScCockpitWorkOrderModal,
    ScCockpitWorkOrderReplyModal,
    ElImageViewer
  },
  mixins: [JeecgListMixin, mixinDevice],
  data() {
    return {
      description: '领导驾驶舱-批示批示管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '批示名称',
          align: 'center',
          dataIndex: 'workOrderTitle'
        },
        {
          title: '批示内容',
          align: 'center',
          dataIndex: 'workOrderContent'
        },
        {
          title: '批示截图',
          align: 'center',
          dataIndex: 'workOrderFile',
          scopedSlots: { customRender: 'imgSlot' }
        },
        {
          title: '批示状态',
          align: 'center',
          dataIndex: 'workOrderStatus',
          scopedSlots: { customRender: 'workOrderStatus' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/workorder/scCockpitWorkOrder/list',
        delete: '/workorder/scCockpitWorkOrder/delete',
        deleteBatch: '/workorder/scCockpitWorkOrder/deleteBatch',
        exportXlsUrl: '/workorder/scCockpitWorkOrder/exportXls',
        importExcelUrl: 'workorder/scCockpitWorkOrder/importExcel'
      },
      dictOptions: {},
      superFieldList: [],
      previewImg: ''
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    this.getSuperFieldList()
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'workOrderTitle', text: '批示名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'workOrderContent', text: '批示内容', dictCode: '' })
      fieldList.push({ type: 'string', value: 'workOrderFile', text: '批示截图', dictCode: '' })
      fieldList.push({ type: 'string', value: 'workOrderStatus', text: '批示状态', dictCode: '' })
      this.superFieldList = fieldList
    },
    handleReply(record) {
      this.$refs.replyForm.visible = true
      this.$refs.replyForm.disableSubmit = false
      this.$refs.replyForm.edit(record)
    },
    handlePreview(text) {
      console.log(text)
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
