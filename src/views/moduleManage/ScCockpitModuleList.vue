<template xmlns:background-color="http://www.w3.org/1999/xhtml">
  <a-row :gutter="20">
    <a-col :md="6" :sm="24">
      <a-card :bordered="false">
        <!-- 按钮操作区域 -->
        <a-row style="margin-left: 14px">
          <a-button type="primary" @click="handleAddGroup(1)">添加分组</a-button>
          <a-button type="primary" @click="handleAddGroup(2)">添加下级分组</a-button>
          <a-button type="primary" @click="handleAddModule(1)">添加模块</a-button>
        </a-row>
        <div style="background: #fff; padding-left: 16px; height: 100%; margin-top: 5px">
          <a-alert type="info" :showIcon="true">
            <div slot="message">
              当前选择：<span v-if="currSelected && currSelected.groupName">{{
                getCurrSelectedTitle()
              }}</span>
              <a
                v-if="currSelected && currSelected.groupName"
                style="margin-left: 10px"
                @click="onClearSelected"
                >取消选择</a
              >
            </div>
          </a-alert>
          <a-input-search
            style="width: 100%; margin-top: 10px"
            placeholder="请输入分组或模块名称"
            @search="onSearch"
          />
          <!-- 树-->
          <a-col :md="8" :sm="24" class="department-tree">
            <template>
              <a-dropdown :trigger="[dropTrigger]" @visibleChange="dropStatus">
                <span style="user-select: none">
                  <a-spin
                    class="tree"
                    :style="{ overflow: loading ? 'hidden' : 'auto' }"
                    :spinning="loading"
                  ></a-spin>
                  <a-tree
                    show-icon
                    multiple
                    draggable
                    :replaceFields="replaceFields"
                    :selectedKeys="selectedKeys"
                    :treeData="treeData"
                    :checkStrictly="checkStrictly"
                    :expandedKeys="iExpandedKeys"
                    :autoExpandParent="autoExpandParent"
                    :icon="getIcon"
                    @drop="onDrop"
                    @select="onSelect"
                    @rightClick="rightHandle"
                    @expand="onExpand"
                  >
                    <template slot="title" slot-scope="{ groupName }">
                      <span v-if="groupName.indexOf(searchValue) > -1">
                        {{ groupName.substr(0, groupName.indexOf(searchValue)) }}
                        <span style="color: #f50">{{ searchValue }}</span>
                        {{ groupName.substr(groupName.indexOf(searchValue) + searchValue.length) }}
                      </span>
                      <span v-else>{{ groupName }}</span>
                    </template>
                  </a-tree>
                </span>
                <!--新增右键点击事件,和增加添加和删除功能-->
                <a-menu slot="overlay">
                  <a-menu-item key="1" @click="handleAddGroup(3)">添加下级分组</a-menu-item>
                  <a-menu-item key="2" @click="handleAddModule(2)">添加模块</a-menu-item>
                  <a-menu-item key="3" @click="handleDelete(rightClickSelectedRecord)"
                    >删除</a-menu-item
                  >
                  <a-menu-item key="4" @click="closeDrop">取消</a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
          </a-col>
        </div>
      </a-card>
      <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      <div class="drawer-bootom-button">
        <a-dropdown :trigger="['click']" placement="topCenter">
          <a-menu slot="overlay">
            <a-menu-item key="1" @click="switchCheckStrictly(1)">父子关联</a-menu-item>
            <a-menu-item key="2" @click="switchCheckStrictly(2)">取消关联</a-menu-item>
            <a-menu-item key="5" @click="expandAll">展开所有</a-menu-item>
            <a-menu-item key="6" @click="closeAll">合并所有</a-menu-item>
          </a-menu>
          <a-button>
            树操作
            <a-icon type="up" />
          </a-button>
        </a-dropdown>
      </div>
    </a-col>
    <a-col v-if="showRightForm" :md="18" :sm="24">
      <a-card>
        <ScCockpitModuleConfig
          ref="ScCockpitModuleConfig"
          :addModule="addModule"
          @ok="loadTree"
        ></ScCockpitModuleConfig>
      </a-card>
    </a-col>
    <a-col v-else :md="18" :sm="24">
      <a-card>
        <a-empty>
          <span slot="description"> 请先选择一个模块! </span>
        </a-empty>
      </a-card>
    </a-col>
    <ScCockpitModuleGroupManageModal
      ref="groupModalForm"
      groupType="index"
      @ok="modalFormOk"
    ></ScCockpitModuleGroupManageModal>
  </a-row>
</template>
<script>
import { getAction, postAction, deleteAction } from '@/api/manage'
import { traverseTree } from '@/api/api'
// import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ScCockpitModuleGroupManageModal from './modules/ScCockpitModuleGroupManageModal'
import ScCockpitModuleConfig from './modules/ScCockpitModuleConfig'
export default {
  name: 'ScCockpitModuleList',
  components: { ScCockpitModuleGroupManageModal, ScCockpitModuleConfig },
  // mixins: [JeecgListMixin],
  data() {
    return {
      currSelected: undefined,
      dropTrigger: '',
      loading: false,
      hiding: true,
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id'
      },
      selectReplaceFields: {
        children: 'children',
        title: 'groupName',
        key: 'id',
        value: 'id'
      },
      activeKey: '1',
      selectedKeys: [],
      treeData: [],
      treeList: [],
      searchValue: '',
      checkStrictly: true,
      iExpandedKeys: [],
      autoExpandParent: false,
      rightClickSelectedRecord: undefined,
      allTreeKeys: [],
      draggedNode: '',
      enterNode: '',
      showRightForm: false,
      addModule: false,
      url: {
        list: '/group/scCockpitGroup/queryByType?groupType=module',
        deleteGroup: '/group/scCockpitGroup/delete',
        deleteIndexSet: '/index/scCockpitIndexGroupConfig/delete',
        deleteBatch: '/group/scCockpitGroup/deleteBatch',
        queryModuleById: '/module/scCockpitModuleConfig/queryById',
        dragToEdit: '/group/scCockpitGroup/updateGroupSort'
      }
    }
  },
  created() {
    this.loadTree()
  },
  methods: {
    modalFormOk() {
      // 新增/修改 成功时，重载列表
      this.loadTree()
      // 清空列表选中
      this.onClearSelected()
    },
    loadTree() {
      this.loading = true
      this.addModule = false
      this.treeData = []
      getAction(this.url.list)
        .then((res) => {
          if (res.success) {
            // this.allTreeKeys = res.result.ids
            let treeDataInit = res.result
            treeDataInit.forEach((node) => {
              traverseTree(node, (node) => {
                if (
                  this.$router.currentRoute.query.id &&
                  node.id === this.$router.currentRoute.query.id
                ) {
                  this.onSelect(null, node)
                }
                if (node.data) {
                  // 将指标集data进行字段转换，并push到childList中，保留其type字段
                  node.data.forEach((data) => {
                    let obj = data
                    obj.groupName = data.name
                    delete obj.name
                    if (!node.childList) node.childList = [obj]
                    else node.childList.push(obj)
                  })
                  delete node.data
                }
              })
            })
            this.treeData = treeDataInit
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    setThisExpandedKeys(node) {},
    getAllKeys(node) {},
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      const record = node.node.dataRef
      this.rightClickSelectedRecord = record
      // this.topicType = record.topicType
    },
    onExpand(expandedKeys) {
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible === false) {
        this.dropTrigger = ''
      }
    }, // 右键点击下拉关闭下拉框
    closeDrop() {},
    onSearch(value) {
      this.treeList = []
      this.treeList = this.generateList(this.treeData)
      const expandedKeys = this.treeList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            return this.getParentKey(item.key, this.treeData)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        iExpandedKeys: expandedKeys,
        searchValue: value,
        autoExpandParent: true
      })
    },
    getParentKey(key, tree) {
      let parentKey
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.childList) {
          if (node.childList.some((item) => item.id === key)) {
            parentKey = node.id
          } else if (this.getParentKey(key, node.childList)) {
            parentKey = this.getParentKey(key, node.childList)
          }
        }
      }
      return parentKey
    },
    generateList(data) {
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        const key = node.id
        this.treeList.push({ key, title: node.groupName })
        if (node.childList) {
          this.generateList(node.childList)
        }
      }
      return this.treeList
    },
    hide() {
      this.visible = false
    },
    onSelect(selectedKeys, e) {
      const record = selectedKeys === null ? e : e.node.dataRef
      console.log('record', record)

      this.currSelected = Object.assign({}, record)
      this.selectedKeys = [record.id]
      this.showRightForm = !this.currSelected.groupType
      this.showRightForm &&
        this.$nextTick(() => {
          this.$refs.ScCockpitModuleConfig.unSave = false
          this.$refs.ScCockpitModuleConfig.configMode = 3
          this.$refs.ScCockpitModuleConfig.passGroupId(this.currSelected.id)
          getAction(this.url.queryModuleById, { id: this.currSelected.id }).then((res) => {
            if (res.success) {
              this.addModule = false
              this.$refs.ScCockpitModuleConfig.getModuleDataOnTreeSelect(res.result.moduleData)
            } else {
              this.showRightForm = false
            }
          })
        })
      this.$nextTick(() => {
        this.$refs.baseInfo && this.$refs.baseInfo.edit(record)
      })
    },
    // 触发onSelect事件时,为部门树右侧的form表单赋值
    setValuesToForm(record) {},
    getCurrSelectedTitle() {
      return this.currSelected.groupName
    },
    onClearSelected() {
      this.hiding = true
      this.currSelected = {}
      // this.form.resetFields()
      this.selectedKeys = []
      this.showRightForm = false
    },
    async submitCurrForm() {},
    emptyCurrForm() {},
    handleEdit(e, record) {
      if (record.groupType) this.handleEditGroup(record)
      else this.handleEditIndex(record)
    },
    handleEditGroup(record) {
      this.$refs.groupModalForm.disableSubmit = false
      this.$refs.groupModalForm.title = '编辑分组'
      this.$refs.groupModalForm.edit(record)
    },
    handleEditIndex(record) {
      this.$refs.indexModalForm.disableSubmit = false
      this.$refs.indexModalForm.title = '编辑指标集'
      this.$refs.indexModalForm.edit(record)
    },
    handleAddGroup(num) {
      if (
        (num === 2 && !this.currSelected.groupType) ||
        (num === 3 && !this.rightClickSelectedRecord.groupType)
      ) {
        this.$message.warning('只能在分组下添加分组')
        return
      }
      if (num === 2 && !this.selectedKeys.length) {
        this.$message.warning('请选择一个上级分组')
        return
      }
      this.$refs.groupModalForm.disableSubmit = false
      this.$refs.groupModalForm.title = num === 1 ? '新增分组' : '添加下级'
      this.$refs.groupModalForm.add(
        num,
        this.selectedKeys.length ? this.selectedKeys[0] : null,
        this.rightClickSelectedRecord ? this.rightClickSelectedRecord.id : null
      )
    },
    handleAddModule(num) {
      this.addModule = true
      if (num === 1 && !this.currSelected) {
        this.$message.warning('请选择一个上级分组')
        return
      } else if (
        (num === 1 && !this.currSelected.groupType) ||
        (num === 2 && !this.rightClickSelectedRecord.groupType)
      ) {
        this.$message.warning('只能在分组下添加模块')
        return
      }
      this.showRightForm = true
      this.$nextTick(() => {
        this.$refs.ScCockpitModuleConfig.passGroupId(
          num === 1 ? this.currSelected.id : this.rightClickSelectedRecord.id
        )
      })
      // this.$refs.indexModalForm.disableSubmit = false
      // this.$refs.indexModalForm.title = '新增模块'
      // this.$refs.indexModalForm.add(this.rightClickSelectedRecord.id)
    },
    handleDelete(node, showMsg = true) {
      if (node.childList && showMsg) {
        // 有下级分组不允许直接删除
        this.$message.warning(`该分组有${node.childList.length}个下级分组`)
        return
      }
      var that = this
      let url = ''
      if (node.groupType) url = this.url.deleteGroup // 删除分组
      else url = this.url.deleteIndexSet // 删除指标集
      if (showMsg) {
        this.$confirm({
          title: '确认删除',
          content: '确定要删除此节点吗?',
          onOk: async function () {
            const res = await getAction(url, { id: node.id })
            if (res.success) {
              that.$message.success(res.message)
              that.loadTree()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          }
        })
      } else {
        getAction(url, { id: node.id })
      }
    },
    onDrop(info) {
      // 执行树的拖拽逻辑
      let pid, sortOrder
      const dropPos = info.node.pos.split('-')
      // 拖入的节点
      const enterNodeKey = info.node.eventKey
      const isdropToGap = info.dropToGap
      // 被拖的节点
      const draggedNodeKey = info.dragNode.eventKey
      const draggedNodeData = info.dragNode.dataRef
      console.log('enterNodeKey', enterNodeKey)
      if (!isdropToGap) {
        // 移入到某个分组下
        if (!info.node.dataRef.groupType) {
          this.$message.warning('不能移入到模块中')
          return
        }
        let node
        for (let i = 1; i < dropPos.length; i++) {
          if (i === 1) node = this.treeData[+dropPos[i]]
          else node = node.childList[+dropPos[i]]
        }
        pid = node.id
        sortOrder = node.childList ? node.childList.length : 0
      } else {
        // 移入到某个间隙中
        let node = { id: '-1' }
        for (let i = 1; i < dropPos.length - 1; i++) {
          if (i === 1) node = this.treeData[+dropPos[i]]
          else node = node.childList[+dropPos[i]]
        }
        pid = node.id
        // 找到新位置前面的相邻节点
        const neighborNodePos = +dropPos[dropPos.length - 1] // 相邻节点在当前目录下的位置
        const relativePosition = info.dropPosition - neighborNodePos
        if (relativePosition === -1) {
          // 被拖节点在相邻节点之前，需要再往前找一个节点
          if (neighborNodePos) {
            // 前面还有节点
            const neighborNode =
              pid === '-1'
                ? this.treeData[neighborNodePos - 1]
                : node.childList[neighborNodePos - 1]
            sortOrder = this.getSortOrder(neighborNode, draggedNodeData, pid, node)
          } else {
            // 前无节点（拖到了当前目录的第一个位置），sortOrder直接取0
            sortOrder = 0
          }
        } else if (relativePosition === 1) {
          // 被拖节点在相邻节点之后，sortorder即为相邻节点sortOrder+1或groupSort+1
          const neighborNode =
            pid === '-1' ? this.treeData[neighborNodePos] : node.childList[neighborNodePos]
          sortOrder = this.getSortOrder(neighborNode, draggedNodeData, pid, node)
        }
      }
      let params
      if (draggedNodeData.groupType) {
        params = {
          pid,
          groupType: 'module',
          groupId: draggedNodeKey,
          sortOrder
        }
      } else {
        if (pid === '-1') {
          this.$message.warning('模块只能存在于分组内')
          return
        }
        params = {
          groupId: pid,
          groupType: 'module',
          relId: draggedNodeKey,
          sortOrder
        }
      }
      postAction(this.url.dragToEdit, params).then((res) => {
        if (res.success) {
          this.$message.success('操作成功')
          this.loadTree()
        } else {
          this.$message.warning('操作失败')
        }
      })
    },
    /**
     * 得到拖拽后的前一节点后，计算新的排序字段值
     *
     * @param neighborNode 前一节点
     * @param draggedNodeData 被拖节点
     * @param pid 父节点id，树节点pid=-1
     * @param node 父节点
     */
    getSortOrder(neighborNode, draggedNodeData, pid, node) {
      let sortOrder
      if (neighborNode.groupType) {
        // 前方节点是分组，被拖节点是分组则sort + 1，否则成为第一个指标集
        sortOrder = draggedNodeData.groupType ? neighborNode.groupSort + 1 : 0
      } else {
        // 前方节点是指标集，被拖节点是分组则找到当前目录下最后一个分组取sort+1，否则直接取sort+1
        if (draggedNodeData.groupType) {
          let lastGroup
          const currentLevelNodes = pid === '-1' ? this.treeData : node.childList
          for (let i = 0; i < currentLevelNodes.length && !!currentLevelNodes[i].groupType; i++) {
            lastGroup = currentLevelNodes[i]
          }
          sortOrder = lastGroup ? lastGroup.groupSort + 1 : 0
        } else {
          sortOrder = neighborNode.sortOrder + 1
        }
      }
      return sortOrder
    },
    expandAll() {
      this.iExpandedKeys = this.allTreeKeys
    },
    closeAll() {
      this.iExpandedKeys = []
    },
    getIcon(props) {
      if (!props.type) {
        return <a-icon type="folder-open" />
      } else if (props.type === 'module') {
        return <a-icon type="appstore" />
      }
    },
    cancelCheckALL() {},
    switchCheckStrictly(v) {}
  }
}
</script>
<style scoped>
.ant-card-body .table-operator {
  margin: 15px;
}

.anty-form-btn {
  width: 100%;
  text-align: center;
}

.anty-form-btn button {
  margin: 0 5px;
}

.anty-node-layout .ant-layout-header {
  padding-right: 0;
}

.header {
  padding: 0 8px;
}

.header button {
  margin: 0 3px;
}

.ant-modal-cust-warp {
  height: 100%;
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden;
}

#app .desktop {
  height: auto !important;
}

/** Button按钮间距 */
.ant-btn {
  margin-left: 10px;
}

.drawer-bootom-button {
  /*position: absolute;*/
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: left;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}

.department-tree {
  margin-top: 5px;
  width: 100%;
  height: 495px;
  overflow: auto;
}
</style>
