<template>
  <div style="position: relative">
    <div class="top-tab">
      <div v-for="(item, index) in tabItem" :key="item.name" class="tab-item" @click="handleClickTab(index)">
        <a-icon :type="item.icon" style="font-size: 25px" />
        <div>{{ item.name }}</div>
      </div>
    </div>
    <a-divider type="horizontal"></a-divider>
    <div
      class="tab-modal"
      :class="{ 'tab-modal-show': showModal }"
      :style="{
        marginLeft: `${(60 / 7) * currentSelectTabIndex}%`,
        width: `${currentSelectTabIndex == 0 ? 400 : currentSelectTabIndex == 3 ? 300 : 150}px`
      }"
    >
      <a-icon v-show="showModal" class="close-btn" type="close-square" @click="hideModal" />
      <div
        v-if="currentSelectTabIndex !== 0"
        class="grid-container"
        :style="{ gridTemplateColumns: `repeat(${currentSelectTabIndex === 3 ? 3 : 1}, 1fr)` }"
      >
        <div v-for="item in gridContent" :key="item.name" class="grid-item" @click="handleClickModalItem(item.name)">
          <div
            class="pic"
            style="
              height: 50px;
              width: 50px;
              border: 1px solid #505674;
              font-size: 10px;
              display: flex;
              align-items: center;
              justify-content: center;
            "
          >
            暂不可用
          </div>
          <div>
            {{ item.name }}
          </div>
        </div>
      </div>
      <div v-else class="chart-tab">
        <a-tabs v-model="currentSelectChartCatogary" tab-position="left" :style="{ height: '230px' }">
          <a-tab-pane v-for="item in chartItem" :key="item.catogary" class="grid-container" :tab="item.catogary">
            <div
              v-for="chart in item.content"
              :key="chart.name"
              class="grid-item"
              @click="handleClickModalItem(chart.name)"
            >
              <img class="pic" style="height: 50px; width: 60px" :src="chart.pic" />
              <div style="text-align: center">
                {{ chart.name }}
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import { tabItem, chartItem, searchComp, richText, media, tab, more } from '../constant/constant.js'
export default {
  name: '',
  data() {
    return {
      tabItem,
      chartItem,
      searchComp,
      richText,
      media,
      tab,
      more,
      showModal: false,
      currentSelectTabIndex: undefined,
      currentSelectChartCatogary: '折线图',
      gridContent: undefined
    }
  },
  methods: {
    handleClickTab(index) {
      this.showModal = !this.showModal ? true : this.currentSelectTabIndex !== index
      this.currentSelectTabIndex = index
      this.gridContent =
        index === 1
          ? searchComp
          : index === 2
          ? richText
          : index === 3
          ? media
          : index === 4
          ? tab
          : index === 5
          ? more
          : undefined
    },
    handleClickModalItem(name) {
      this.$emit('createComp', name)
      this.showModal = false
    },
    hideModal() {
      this.showModal = false
    }
  }
}
</script>
<style lang="less" scoped>
.top-tab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: auto;
  width: 60%;

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    cursor: pointer;
    transition: all 0.5s ease;
    padding: 0 20px;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    &:hover {
      background-color: #e6f7ff;
    }
    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(6) {
      border-right: 1px solid #e8e8e8;
    }
  }
}
.tab-modal {
  transition: margin-left 0.5s ease, opacity 0.5s ease;
  opacity: 0;
  background-color: #f0f2f5;
  max-width: 400px;
  position: absolute;
  z-index: 1;
  display: none;
  .close-btn {
    position: absolute;
    right: 10px;
    top: 0;
    font-size: 20px;
    z-index: 1;
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }
  &.tab-modal-show {
    opacity: 1;
    display: block;
  }
  .grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    row-gap: 20px;
    padding-top: 20px;
  }
  .chart-tab {
    ::v-deep .ant-tabs-tab {
      text-align: center;
    }
    ::v-deep .ant-tabs-content {
      padding: 10px;
      height: 100%;
      overflow-y: scroll;
    }
    .grid-container {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      padding: revert;
    }
  }
  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    cursor: pointer;
    &:hover {
      background-color: #e6f7ff;
    }
  }
}
::v-deep .ant-divider {
  margin-bottom: 0;
}
</style>
