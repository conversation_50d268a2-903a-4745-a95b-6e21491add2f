<template>
  <j-modal
    title="接口详情信息展示"
    :width="width"
    :visible="visible"
    dialogClass="interface-detail"
    @ok="handleCancel"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div class="detail-container">
      <div class="detail-box" v-show="type === '1'">
        <img class="img-item" src="../images/1.png" />
      </div>
      <div class="detail-box" v-show="type === '2'">
        <img class="img-item" src="../images/1.png" />
      </div>
    </div>
  </j-modal>
</template>

<script>

export default {
  name: 'InterfaceDetail',
  data() {
    return {
      width: 800,
      visible: false,
      type: ''
    }
  },
  methods: {

    /**
     * @description 打开介绍详情
     * @param {String} createType
     * @returns void
     */
    handleDetail(createType) {
      this.visible = true
      this.type = createType
    },

    /**
     * @description 关闭操作
     * @returns void
     */
    handleCancel() {
      this.visible = false
      this.type = ''
    }
  }
}
</script>

<style lang="less">
.detail-container {
  width: 100%;
  height: 590px;
  overflow: auto;
  .detail-box {
    width: 100%;
    overflow: auto;
  }
}

.interface-detail {
  .ant-modal-body {
    height: auto;
    padding: 10px 0px 15px 40px;
  }
}
</style>
