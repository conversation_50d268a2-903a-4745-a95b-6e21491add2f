<template>
  <a-drawer
    destroyOnClose
    title=""
    :width="540"
    :visible="visible"
    :body-style="{ paddingBottom: '80px' }"
    @close="onClose"
  >
    <a-tabs tab-position="top" @change="onTabChange">
      <a-tab-pane v-for="item in configTabs" :key="item.compName" :tab="item.name">
        <component
          :is="item.compName"
          :ref="item.compName"
          :componentType="currentSelectComp.componentType"
          @value="(value) => getFormData(value, item.compName)"
        ></component>
      </a-tab-pane>
    </a-tabs>
    <div class="bottom-btn" style="display: flex; justify-content: end; align-items: center">
      <a-button type="primary" @click="saveFormData">确定</a-button>
      <a-button style="margin-left: 20px" @click="onClose">取消</a-button>
    </div>
  </a-drawer>
</template>
<script>
import ScCockpitModuleConfigForm from './ScCockpitModuleConfigForm.vue'
import ScCockpitComponentConfigForm from './ScCockpitComponentConfigForm.vue'
import ScCockpitDataConfigForm from './ScCockpitDataConfigForm.vue'
import ScCockpitCustomCodeForm from './ScCockpitCustomCodeForm.vue'
export default {
  name: 'ScCockpitConfigDrawer',
  components: {
    ScCockpitModuleConfigForm,
    ScCockpitComponentConfigForm,
    ScCockpitDataConfigForm,
    ScCockpitCustomCodeForm
  },
  props: {
    configTabs: {
      type: Array,
      default: () => [
        {
          name: '模块配置',
          compName: 'ScCockpitModuleConfigForm'
        }
      ]
    },
    addModule: {
      type: Boolean,
      default: false
    },
    formDataProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: false,
      currentSelectConfigTab: '模块配置',
      currentSelectComp: {},
      formDataTmp: {},
      formData: {},
      groupId: ''
    }
  },

  methods: {
    show(comp) {
      this.visible = true
      this.currentSelectComp = {
        currentSelectCompIndex: comp ? comp.compIndex : null,
        currentSelectCompId: comp ? comp.compId : null,
        componentType: comp ? comp.compType : null,
        componentFormVal: comp ? comp.compFormVal : null,
        moduleFormVal: comp ? comp.moduleFormVal : null
      }
      this.currentSelectConfigTab = comp ? (comp.compType ? '组件配置' : '模块配置') : null
      this.$nextTick(() => {
        this.$refs.ScCockpitModuleConfigForm[0].getFormData(this.currentSelectComp.moduleFormVal)
      })
      this.formData = { ...this.formDataProps }
    },
    onClose() {
      this.visible = false
      this.currentSelectConfigTab = '模块配置'
      this.$emit('closeDrawer')
    },
    getFormData(value, compName) {
      // 接收各表单信息
      this.formDataTmp[compName] = value
    },
    onTabChange(tab) {
      if (tab !== 'ScCockpitModuleConfigForm') {
        // 传递组件表单信息
        this.$nextTick(() => {
          this.$refs[tab][0].getFormData(this.currentSelectComp.componentFormVal)
        })
      } else {
        // 传递模块表单信息
        this.$refs[tab][0].getFormData(this.currentSelectComp.moduleFormVal)
      }
    },
    saveFormData() {
      // 触发校验
      for (const tab of this.configTabs) {
        // 只校验填写过的表单
        if (this.$refs[tab.compName] && this.$refs[tab.compName].length) {
          const value = this.$refs[tab.compName][0].validateForm()
          if (this.formDataTmp[tab.compName]) {
            // 收集表单信息
            if (tab.name === '模块配置') {
              this.formData = Object.assign(this.formData, {
                groupId: this.groupId,
                moduleType: 'module',
                ...this.formDataTmp[tab.compName]
              })
            } else if (tab.name === '组件配置') {
              this.formDataTmp[tab.compName].tempId = this.currentSelectComp.currentSelectCompId
              if (!this.formData.componentList) this.formData.componentList = []
              this.formData.componentList[this.currentSelectComp.currentSelectCompIndex] = Object.assign(
                this.formData.componentList[this.currentSelectComp.currentSelectCompIndex] || {},
                this.formDataTmp[tab.compName]
              )
            } else if (tab.name === '数据配置' || tab.name === '自定义代码') {
              if (!this.formData.componentList) this.formData.componentList = []
              this.formData.componentList[this.currentSelectComp.currentSelectCompIndex] = Object.assign(
                this.formData.componentList[this.currentSelectComp.currentSelectCompIndex] || {},
                this.formDataTmp[tab.compName]
              )
            }
          }
        } else {
          continue
        }
      }
      this.formData.componentList &&
        this.formData.componentList.forEach((component) => {
          component.transNull = this.formData.transNull
        })
      this.$emit('saveFormData', this.formData)
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .ant-drawer-body {
  padding-bottom: 20px !important;
}
</style>
