<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form-model ref="form" slot="detail" :model="form" :rules="rules" :disabled="false">
        <div class="title">组件基础</div>
        <a-form-model-item style="display: flex; align-items: center" label="背景图片" porp="moduleBgPic">
          <!-- bgImage -->
          <j-image-upload v-model="form.moduleBgPic"></j-image-upload>
        </a-form-model-item>
        <a-row>
          <a-col :span="8">
            <a-form-model-item
              label="默认宽度"
              prop="componentWidth"
              :labelCol="labelColShort"
              :wrapperCol="wrapperColShort"
            >
              <a-input-number v-model="form.componentWidth" placeholder=""></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="最小宽度" prop="minWidth" :labelCol="labelColShort" :wrapperCol="wrapperColShort">
              <a-input-number v-model="form.minWidth"></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="最大宽度" prop="maxWidth" :labelCol="labelColShort" :wrapperCol="wrapperColShort">
              <a-input-number v-model="form.maxWidth"></a-input-number>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item
              label="默认长度"
              prop="componentLength"
              :labelCol="labelColShort"
              :wrapperCol="wrapperColShort"
            >
              <a-input-number v-model="form.componentLength"></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              label="最小长度"
              prop="minLength"
              :labelCol="labelColShort"
              :wrapperCol="wrapperColShort"
            >
              <a-input-number v-model="form.minLength"></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              label="最大长度"
              prop="maxLength"
              :labelCol="labelColShort"
              :wrapperCol="wrapperColShort"
            >
              <a-input-number v-model="form.maxLength"></a-input-number>
            </a-form-model-item>
          </a-col>
        </a-row>
        <div class="title">标题</div>
        <a-form-model-item label="标题名称" prop="titleName" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-model="form.titleName" placeholder="请输入标题名称"></a-input>
        </a-form-model-item>
        <a-form-model-item label="是否展示标题" prop="showTitle" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="form.showTitle">
            <a-radio :value="1"> 是 </a-radio>
            <a-radio :value="0"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item prop="titleImage" style="display: flex; align-items: center" label="标题背景图片">
          <j-image-upload v-model="form.titleImage"></j-image-upload>
        </a-form-model-item>
        <div class="title">点击事件</div>
        <a-form-model-item label="是否链接" prop="isLink" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="form.isLink">
            <a-radio :value="1"> 是 </a-radio>
            <a-radio :value="0"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="打开方式" prop="linkType" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select v-model="form.linkType" placeholder="请选择打开方式">
            <a-select-option value="0"> 内部弹窗 </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="链接地址" prop="linkUrl" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-model="form.linkUrl" placeholder="请输入链接地址"></a-input>
        </a-form-model-item>
        <a-form-model-item label="是否弹窗" prop="isPopup" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="form.isPopup">
            <a-radio :value="1"> 是 </a-radio>
            <a-radio :value="0"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="弹窗模块" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <!-- popupModuleId -->
        </a-form-model-item>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>
<script>
import { pick } from 'lodash-es'

export default {
  name: 'ScCockpitComponentConfigForm',
  data() {
    return {
      form: {
        moduleBgPic: undefined,
        componentWidth: undefined,
        minWidth: undefined,
        maxWidth: undefined,
        componentLength: undefined,
        minLength: undefined,
        maxLength: undefined,
        titleName: undefined,
        showTitle: 1,
        titleImage: undefined,
        isLink: 0,
        linkType: '0',
        linkUrl: undefined,
        isPopup: 0
      },
      rules: {
        componentWidth: [{ required: true, message: '请输入组件默认宽度', trigger: 'change' }],
        componentLength: [{ required: true, message: '请输入组件默认高度', trigger: 'change' }],
        titleName: [{ required: true, message: '请输入标题名称', trigger: 'change' }],
        showTitle: [{ required: true, message: '请选择是否展示标题', trigger: 'change' }],
        isLink: [{ required: true, message: '请输入是否链接', trigger: 'change' }],
        isPopup: [{ required: true, message: '请选择是否弹窗', trigger: 'change' }]
      },
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      labelColShort: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      wrapperColShort: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      moduleBgPic: [],
      titleImage: []
    }
  },
  created() {
    this.form = {
      moduleBgPic: undefined,
      componentWidth: undefined,
      minWidth: undefined,
      maxWidth: undefined,
      componentLength: undefined,
      minLength: undefined,
      maxLength: undefined,
      titleName: undefined,
      showTitle: undefined,
      titleImage: undefined,
      isLink: 0,
      linkType: '0',
      linkUrl: undefined,
      isPopup: 0
    }
  },
  methods: {
    getFormData(formData) {
      // 初始化表单
      this.form = Object.assign(this.form, pick(formData, 'titleName', 'isLink', 'isPopup', 'showTitle', 'titleImage'))
    },
    validateForm() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message.warning('组件配置表单有必填项未填写')
        } else {
          this.$emit('value', this.form)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.title {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 16px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    height: 16px;
    width: 4px;
    border-radius: 4px;
    background-color: #1890ff;
  }
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    height: 2px;
    width: 100%;
    background-color: #e8e8e8;
  }
}
::v-deep .ant-input-number {
  width: 80px;
}
</style>
