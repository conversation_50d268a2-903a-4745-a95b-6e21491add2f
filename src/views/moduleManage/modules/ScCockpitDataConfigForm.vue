<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form-model ref="form" slot="detail" :model="form" :rules="rules" :disabled="false">
        <div class="title">数据选择</div>
        <a-form-model-item label="指标集" prop="indexId" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            v-model="form.indexId"
            :tree-data="indexSetTree"
            :replaceFields="replaceFields2"
            style="width: 100%"
            placeholder="请选择指标集"
            allow-clear
            tree-default-expand-all
            @change="handleIndexSetChange"
          >
          </a-tree-select>
        </a-form-model-item>

        <div class="col-container">
          <div class="col">
            <div class="col-title">维度：</div>
            <draggable
              v-model="dimenssionAll"
              :group="groupDimenssionAll"
              animation="300"
              dragClass="dragClass"
              ghostClass="ghostClass"
              chosenClass="chosenClass"
              @start="(e) => onStart(e, 1)"
            >
              <transition-group>
                <div v-for="item in dimenssionAll" :key="item.id" class="item">{{ item.indexName }}</div>
              </transition-group>
            </draggable>
          </div>
          <div class="col">
            <div class="col-title">指标：</div>
            <draggable
              v-model="indexAll"
              :group="groupIndexAll"
              animation="300"
              dragClass="dragClass"
              ghostClass="ghostClass"
              chosenClass="chosenClass"
              @start="(e) => onStart(e, 2)"
            >
              <transition-group>
                <div v-for="item in indexAll" :key="item.id" class="item">{{ item.indexName }}</div>
              </transition-group>
            </draggable>
          </div>
        </div>
        <div class="title">组件配置</div>
        <a-form-model-item label="组件类型" prop="componentType" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-dict-select-tag
            v-model="form.componentType"
            placeholder="请选择组件类型"
            dictCode="component_type"
            disabled
          ></j-dict-select-tag>
        </a-form-model-item>
        <div class="col" style="margin-left: 5%">
          <div class="col-title">展示维度：</div>
          <draggable
            v-model="dimenssionSelect"
            :group="groupDimenssionSelect"
            :scroll="true"
            :disabled="disableDrag.dimenssion"
            animation="300"
            dragClass="dragClass"
            ghostClass="ghostClass"
            chosenClass="chosenClass"
            @add="(e) => onAdd(e, 1)"
            @sort="(e) => onSort(e, 1)"
          >
            <transition-group :style="style">
              <div v-for="item in dimenssionSelect" :key="item.id" class="item">
                {{ item.indexName }}
                <a-icon type="close-circle" class="close-btn" @click="deleteIndex(item.id, 1)"></a-icon>
              </div>
            </transition-group>
          </draggable>
        </div>
        <div class="col">
          <div class="col-title">别名：</div>
          <a-input
            v-for="item in dimenssionNickNameList"
            :key="item.id"
            v-model="item.value"
            class="item"
            style="cursor: revert; width: revert; height: 35px; text-align: left"
            placeholder="请输入左侧维度别名"
          ></a-input>
        </div>
        <div class="col" style="margin-left: 5%; margin-top: 20px; margin-bottom: 20px">
          <div class="col-title">展示指标：</div>
          <draggable
            v-model="indexSelect"
            :group="groupIndexSelect"
            :scroll="true"
            :disabled="disableDrag.index"
            animation="300"
            dragClass="dragClass"
            ghostClass="ghostClass"
            chosenClass="chosenClass"
            @add="(e) => onAdd(e, 2)"
            @sort="(e) => onSort(e, 2)"
          >
            <transition-group :style="style">
              <div v-for="item in indexSelect" :key="item.id" class="item">
                {{ item.indexName }}
                <a-icon type="close-circle" class="close-btn" @click="deleteIndex(item.id, 2)"></a-icon>
              </div>
            </transition-group>
          </draggable>
        </div>
        <div class="col" style="margin-top: 20px">
          <div class="col-title">别名：</div>
          <div
            v-for="item in indexNickNameList"
            :key="item.id"
            style="display: flex; justify-content: center; align-items: center"
          >
            <a-input
              v-model="item.value"
              class="item"
              :style="{
                cursor: 'revert',
                height: '35px',
                textAlign: 'left',
                width: componentType.includes('柱线') ? '' : 'revert'
              }"
              placeholder="请输入左侧指标别名"
            ></a-input>
            <a-radio-group
              v-if="componentType.includes('柱线')"
              v-model="item.type"
              name="radioGroup"
              style="display: flex; padding-bottom: 10px"
            >
              <a-radio :value="1"> 柱 </a-radio>
              <a-radio :value="2"> 线 </a-radio>
            </a-radio-group>
          </div>
        </div>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>
<script>
import { getAction } from '../../../api/manage'
import { traverseTree, getDictItemsFromCache } from '@/api/api'
import { pick } from 'lodash-es'
import draggable from 'vuedraggable'
export default {
  name: 'ScCockpitDataConfigForm',
  components: {
    draggable
  },
  props: {
    componentType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        indexId: undefined,
        componentType: undefined
      },
      rules: {
        indexId: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        componentType: [{ required: true, message: '请选择组件类型', trigger: 'change' }]
      },
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      labelColShort: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      wrapperColShort: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      indexSetTree: [],
      indexAll: [],
      indexSelect: [],
      indexNickNameList: [],
      histogramLineArr: [],
      dimenssionAll: [],
      dimenssionSelect: [],
      dimenssionNickNameList: [],
      componentList: [],
      currentSelectIndexSet: undefined,
      compTypeList: [],
      draggedIndexType: undefined,
      indexDimenssionTree: [
        { children: [], indexName: '指标', id: '1' },
        { children: [], indexName: '维度', id: '2' }
      ],
      groupIndexAll: {
        name: 'site',
        pull: 'clone',
        put: false
      },
      groupDimenssionAll: {
        name: 'site',
        pull: 'clone',
        put: false
      },
      groupIndexSelect: {
        name: 'site',
        pull: false,
        put: true
      },
      groupDimenssionSelect: {
        name: 'site',
        pull: false,
        put: true
      },
      style: 'min-height:100px;display: block;',
      draggedGroup: undefined,
      disableDrag: {
        dimenssion: false,
        index: false
      },
      replaceFields: {
        children: 'children',
        title: 'indexName',
        key: 'id',
        value: 'id'
      },
      replaceFields2: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      },
      url: {
        queryindexSet: '/group/scCockpitGroup/queryByType?groupType=index',
        queryIndexSetInfo: '/index/scCockpitIndexGroupConfig/queryById'
      }
    }
  },
  created() {
    this.compTypeList = getDictItemsFromCache('component_type')
    this.form = {
      indexId: undefined,
      componentType: this.compTypeList.filter((item) => {
        return item.text === this.componentType
      })[0].value
    }
    this.queryindexSet()
  },
  methods: {
    async getFormData(formData) {
      // 初始化表单
      if (!formData || !formData.dimensionColumn) return
      this.form.indexId = formData.indexId
      const dimensionColumn = JSON.parse(formData.dimensionColumn)
      const indexColumn = JSON.parse(formData.indexColumn)
      const dimenssionIds = Object.keys(dimensionColumn)
      const indexIds = Object.keys(indexColumn).filter((item) => {
        return item !== 'barIndex'
      })
      const dimenssionNickName = Object.values(dimensionColumn)
      const indexNickName = Object.values(indexColumn).filter((item) => {
        return typeof item !== 'object'
      })
      const barIndex = indexColumn.barIndex || null
      await this.handleIndexSetChange(formData.indexId)
      this.dimenssionSelect = dimenssionIds.map((id) => {
        return this.dimenssionAll.filter((item) => {
          return id === item.sourceColumnId
        })[0]
      })
      this.dimenssionNickNameList = dimenssionNickName.map((item, index) => {
        return {
          id: this.dimenssionSelect[index].id,
          value: item
        }
      })
      this.indexSelect = indexIds.map((id) => {
        return this.indexAll.filter((item) => {
          return id === item.sourceColumnId
        })[0]
      })
      this.indexNickNameList = indexNickName.map((item, index) => {
        return {
          id: this.indexSelect[index].id,
          value: item,
          type: barIndex ? (barIndex.includes(this.indexSelect[index].id) ? 1 : 2) : null // 1柱2线
        }
      })
    },
    queryindexSet() {
      getAction(this.url.queryindexSet).then((res) => {
        if (res.success) {
          res.result.forEach((node) => {
            traverseTree(node, (node) => {
              if (node.data) {
                // 将指标集data进行字段转换，并push到childList中，保留其type字段
                node.data.forEach((data) => {
                  let obj = data
                  obj.groupName = data.name
                  delete obj.name
                  if (!node.childList) node.childList = [obj]
                  else node.childList.push(obj)
                })
                delete node.data
              }
              node.key = node.id
              node.title = node.groupName
              node.children = node.childList
              node.value = node.id
              node.disabled = !!node.groupType
            })
          })
          console.log('res.result', res.result)

          this.indexSetTree = res.result
        }
      })
    },
    async handleIndexSetChange(id) {
      const res = await getAction(this.url.queryIndexSetInfo, { id })
      if (res.success) {
        this.currentSelectIndexSet = res.result
        this.indexAll = this.currentSelectIndexSet.groupColumnList.filter((column) => {
          return column.indexType === 'index'
        })
        this.dimenssionAll = this.currentSelectIndexSet.groupColumnList.filter((column) => {
          return column.indexType === 'dimenssion'
        })
        this.indexSelect = []
        this.dimenssionSelect = []
        this.indexNickNameList = []
        this.dimenssionNickNameList = []
      } else if (id) {
        this.$message.warning('指标集信息获取失败')
      }
    },
    async handleQueryCompIndexSetChange(id) {
      const res = await getAction(this.url.queryIndexSetInfo, { id })
      if (res.success) {
        const currentSelectIndexSet = res.result
        const indexAll = currentSelectIndexSet.groupColumnList.filter((column) => {
          return column.indexType === 'index'
        })
        const dimenssionAll = currentSelectIndexSet.groupColumnList.filter((column) => {
          return column.indexType === 'dimenssion'
        })
        this.indexDimenssionTree[0].children = indexAll
        this.indexDimenssionTree[1].children = dimenssionAll
      } else if (id) {
        this.$message.warning('指标集信息获取失败')
      }
    },
    deleteIndex(id, type) {
      if (type === 1) {
        this.dimenssionSelect = this.dimenssionSelect.filter((index) => {
          return index.id !== id
        })
        this.dimenssionNickNameList = this.dimenssionNickNameList.filter((item) => {
          return item.id !== id
        })
      } else {
        this.indexSelect = this.indexSelect.filter((index) => {
          return index.id !== id
        })
        this.indexNickNameList = this.indexNickNameList.filter((item) => {
          return item.id !== id
        })
      }
    },
    onStart(e, type) {
      this.disableDrag = {
        dimenssion: true,
        index: true
      }
      if (type === 1) {
        this.draggedIndexType = 'dimenssion'
        const draggedNode = this.dimenssionAll[e.oldIndex]
        this.disableDrag = {
          dimenssion: this.dimenssionSelect.some((index) => {
            return index.id === draggedNode.id
          }),
          index: true
        }
      } else {
        this.draggedIndexType = 'index'
        const draggedNode = this.indexAll[e.oldIndex]
        this.disableDrag = {
          dimenssion: true,
          index: this.indexSelect.some((index) => {
            return index.id === draggedNode.id
          })
        }
      }
    },
    onAdd(e, type) {
      if (type === 1) {
        const draggedNode = this.dimenssionAll[e.oldIndex]
        this.dimenssionNickNameList.splice(e.newIndex, 0, {
          id: draggedNode.id,
          value: ''
        })
      } else {
        const draggedNode = this.indexAll[e.oldIndex]
        this.indexNickNameList.splice(e.newIndex, 0, {
          id: draggedNode.id,
          value: ''
        })
      }
    },
    onSort(e, type) {
      let arrTmp = []
      if (type === 1) {
        this.dimenssionSelect.forEach((index) => {
          arrTmp.push(
            this.dimenssionNickNameList.filter((item) => {
              return item.id === index.id
            })[0]
          )
        })
        this.dimenssionNickNameList = arrTmp
      } else {
        this.indexSelect.forEach((index) => {
          arrTmp.push(
            this.indexNickNameList.filter((item) => {
              return item.id === index.id
            })[0]
          )
        })
        this.indexNickNameList = arrTmp
      }
    },
    validateForm() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message.warning('数据配置表单有必填项未填写')
        } else if (this.indexSelect.length === 0 || this.dimenssionSelect.length === 0) {
          this.$message.warning('数据配置表单必须设置至少一个维度和指标')
        } else {
          this.form.tableName = this.currentSelectIndexSet.tableName
          this.form.indexColumn = {}
          this.form.dimensionColumn = {}
          if (this.form.componentType.includes('柱线')) {
            if (
              this.indexNickNameList.some((item) => {
                return !item.type
              })
            ) {
              this.$message.warning('请在数据配置表单设置指标的柱线类型')
              return
            }
            this.form.indexColumn.barIndex = []
          }
          this.indexSelect.forEach((index, i) => {
            this.form.indexColumn[index.sourceColumnId] = this.indexNickNameList[i].value
            if (this.form.indexColumn.barIndex && this.indexNickNameList[i].type === 1) {
              this.form.indexColumn.barIndex.push(this.indexNickNameList[i].id)
            }
          })
          this.dimenssionSelect.forEach((index, i) => {
            this.form.dimensionColumn[index.sourceColumnId] = this.dimenssionNickNameList[i].value
          })
          this.form.indexColumn = JSON.stringify(this.form.indexColumn)
          this.form.dimensionColumn = JSON.stringify(this.form.dimensionColumn)
          this.form.sourceId = this.currentSelectIndexSet.sourceId
          this.$emit('value', this.form)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.title {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 16px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    height: 16px;
    width: 4px;
    border-radius: 4px;
    background-color: #1890ff;
  }
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    height: 2px;
    width: 100%;
    background-color: #e8e8e8;
  }
}
::v-deep .ant-input-number {
  width: 80px;
}
.ghostClass {
  background-color: blue !important;
}
.chosenClass {
  background-color: #e6f7ff !important;
  opacity: 1 !important;
}
.dragClass {
  background-color: #e6f7ff !important;
  opacity: 1 !important;
  box-shadow: none !important;
  outline: none !important;
  background-image: none !important;
}
.itxst {
  margin: 10px;
}
.col-container {
  display: flex;
  justify-content: center;
  align-items: start;
  margin-bottom: 20px;
}
.col-title {
  padding: 6px 12px;
}
.col,
.col-null {
  width: 45%;
  border: solid 1px #eee;
  border-radius: 5px;
  float: left;
  padding-bottom: 20px;
  height: 200px;
  overflow-y: scroll;
}

.item {
  padding: 6px 12px;
  margin: 0px 10px 0px 10px;
  border: solid 1px #1890ff;
  border-radius: 10px;
  background-color: #fff;
  text-align: center;
  cursor: pointer;
  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  .close-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }
}
</style>
