<template>
  <div class="moduleConfig-wrapper">
    <!-- <div class="config-mode">
      <a-radio-group v-model="configMode">
        <a-radio :value="1" :disabled="configMode === 3"> 手机端 </a-radio>
        <a-radio :value="2" :disabled="configMode === 3"> pad端 </a-radio>
        <a-radio style="display: block" :value="3" :disabled="configMode !== 3"> 大屏端 </a-radio>
      </a-radio-group>
    </div> -->
    <a-button class="clear-btn" @click="clearSelectComp">取消选择</a-button>
    <a-button type="primary" class="preview-btn" @click="previewModule">预览</a-button>
    <a-button type="primary" class="save-btn" @click="submitFormData">保存</a-button>
    <ScCockpitModuleConfigTab @createComp="handleCreateComp"></ScCockpitModuleConfigTab>
    <div class="config-preview-container">
      <a-alert
        v-show="formData"
        class="alert"
        :message="unSave ? '有修改未保存' : '所有修改已保存'"
        :type="unSave ? 'warning' : 'success'"
        show-icon
      />
      <ScCockpitComponentsContainer
        ref="ScCockpitComponentsContainer"
        class="comp-container"
        :moduleData="formData || null"
        @compId="handleCompId"
        @compLayout="handleCompLayout"
        @deleteComp="handleDeleteComp"
        @addComp="handleAddComp"
      ></ScCockpitComponentsContainer>
    </div>
    <ScCockpitConfigDrawer
      ref="ScCockpitConfigDrawer"
      :configTabs="configTabs"
      :addModule="addModule"
      :formDataProps="formData"
      @closeDrawer="handleCloseDrawer"
      @saveFormData="saveFormData"
    >
    </ScCockpitConfigDrawer>
    <a-icon
      type="left-square"
      class="drawer-switch"
      :class="{ 'drawer-switch-show': showDrawer }"
      :title="showDrawer ? '关闭配置表单' : '打开配置表单'"
      @click="showDrawerFunc"
    />
    <ScCockpitQueryCompoentModal
      ref="ScCockpitQueryCompoentModal"
      :moduleList="formData"
    ></ScCockpitQueryCompoentModal>
  </div>
</template>
<script>
import ScCockpitModuleConfigTab from './ScCockpitModuleConfigTab.vue'
import ScCockpitComponentsContainer from './ScCockpitComponentsContainer.vue'
import ScCockpitConfigDrawer from './ScCockpitConfigDrawer.vue'
import ScCockpitQueryCompoentModal from './ScCockpitQueryCompoentModal.vue'

import { httpAction } from '../../../api/manage'

export default {
  name: 'ScCockpitModuleConfig',
  components: {
    ScCockpitModuleConfigTab,
    ScCockpitComponentsContainer,
    ScCockpitConfigDrawer,
    ScCockpitQueryCompoentModal
  },
  props: {
    addModule: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tabItem: [
        {
          name: '图表',
          icon: 'pie-chart'
        },
        {
          name: '查询组件',
          icon: 'filter'
        },
        {
          name: '富文本',
          icon: 'font-size'
        },
        {
          name: '媒体',
          icon: 'picture'
        },
        {
          name: 'tab',
          icon: 'layout'
        },
        {
          name: '更多',
          icon: 'appstore'
        },
        {
          name: '复用',
          icon: 'copy'
        }
      ],
      configTabs: [
        {
          name: '模块配置',
          compName: 'ScCockpitModuleConfigForm'
        }
      ],
      url: {
        add: '/module/scCockpitModuleConfig/add',
        edit: '/module/scCockpitModuleConfig/edit',
        preview: '/module/scCockpitModuleConfig/previewModule'
      },
      currentSelectConfigTab: '模块配置',
      showDrawer: false,
      moduleData: null,
      formData: null,
      unSave: false,
      configMode: 3
    }
  },
  methods: {
    getModuleDataOnTreeSelect(moduleData) {
      // 点击树节点时获取模块信息
      this.moduleData = JSON.parse(moduleData)
      this.formData = this.moduleData
      console.log('formdata', this.formData)
    },
    handleCreateComp(name) {
      if (name === '查询组件') {
        this.$refs.ScCockpitQueryCompoentModal.visible = true
      } else {
        this.configTabs = [
          {
            name: '模块配置',
            compName: 'ScCockpitModuleConfigForm'
          }
        ]
        this.$refs.ScCockpitComponentsContainer.addComp(name)
      }
    },
    handleCompId(obj) {
      this.currentSelectComp = obj
      this.configTabs =
        obj && obj.id
          ? [
              {
                name: '模块配置',
                compName: 'ScCockpitModuleConfigForm'
              },
              {
                name: '组件配置',
                compName: 'ScCockpitComponentConfigForm'
              },
              {
                name: '数据配置',
                compName: 'ScCockpitDataConfigForm'
              },
              {
                name: '自定义代码',
                compName: 'ScCockpitCustomCodeForm'
              }
            ]
          : [
              {
                name: '模块配置',
                compName: 'ScCockpitModuleConfigForm'
              }
            ]
      if (obj && obj.id) {
        this.showDrawer = true
        this.$refs.ScCockpitConfigDrawer.show({
          compIndex: obj.index,
          compId: obj.id,
          compType: obj.name,
          compFormVal: obj.componentData,
          moduleFormVal: obj.moduleData
        })
      }
    },
    handleCompLayout(layout) {
      this.unSave = true
      const tmp = this.formData.componentList[layout.oldIndex]
      this.formData.componentList.splice(layout.oldIndex, 1)
      this.formData.componentList.splice(layout.newIndex, 0, tmp)
      // this.previewModule()
    },
    showDrawerFunc() {
      this.showDrawer = !this.showDrawer
      this.showDrawer
        ? this.$refs.ScCockpitConfigDrawer.show({
            compIndex: this.currentSelectComp ? this.currentSelectComp.index : null,
            compId: this.currentSelectComp ? this.currentSelectComp.id : null,
            compType: this.currentSelectComp ? this.currentSelectComp.name : null,
            compFormVal: this.currentSelectComp ? this.currentSelectComp.componentData : null,
            moduleFormVal: this.moduleData || null
          })
        : this.$refs.ScCockpitConfigDrawer.onClose()
    },
    handleCloseDrawer() {
      this.showDrawer = false
    },
    passGroupId(groupId) {
      this.$refs.ScCockpitConfigDrawer.groupId = groupId
    },
    saveFormData(formData) {
      const leftLastIndex = this.$refs.ScCockpitComponentsContainer.leftCharts.length
      // 在左列最后一个元素后插入标志位
      if (
        formData.componentList &&
        (!formData.componentList[leftLastIndex] ||
          formData.componentList[leftLastIndex].sourceId !== 'separatorFlag')
      ) {
        const separatorComp = { ...formData.componentList[0], sourceId: 'separatorFlag' }
        delete separatorComp.isInitChart
        formData.componentList.splice(leftLastIndex, 0, separatorComp)
      }
      this.unSave = true
      this.showDrawerFunc()
      this.$message.success('暂存成功')
      this.formData = formData
      console.log('formData', this.formData)
    },
    handleDeleteComp(index) {
      this.unSave = true
      this.formData.componentList.splice(index, 1)
    },
    handleAddComp(chart) {
      if (!this.formData) this.formData = { componentList: [] }
      this.formData.componentList.splice(
        this.$refs.ScCockpitComponentsContainer.leftCharts.length - 1,
        0,
        {
          componentType: chart.componentType
        }
      )
    },
    previewModule() {
      let formData = { ...this.formData }
      formData.componentList = formData.componentList.filter((comp) => {
        return comp.sourceId !== 'separatorFlag' && comp.titleName
      })
      if (!formData.componentList.length) {
        this.$message.warning('图表无数据')
        return
      }
      httpAction(this.url.preview, formData, 'post').then((previewRes) => {
        const res = previewRes.result
        this.formData.componentList.forEach((comp, index) => {
          if (!comp.titleName) {
            res.componentList.splice(index, 0, {
              dataList: { records: [] },
              dimensionList: [],
              indexList: []
            })
          }
        })
        res && this.$refs.ScCockpitComponentsContainer.renderChart(res.componentList)
      })
    },
    clearSelectComp() {
      this.$refs.ScCockpitComponentsContainer.currentSelectChart = null
      this.handleCompId(null)
    },
    submitFormData() {
      const url = this.addModule ? this.url.add : this.url.edit
      const method = this.addModule ? 'post' : 'post'
      console.log('formData', this.formData)
      httpAction(url, this.formData, method).then((res) => {
        if (res.success) {
          this.$message.success('操作成功')
          this.$emit('ok')
          this.unSave = false
        } else {
          this.$message.warning('操作失败')
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.moduleConfig-wrapper {
  position: relative;
  .save-btn {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
  }
  .preview-btn {
    position: absolute;
    top: 0;
    right: 80px;
    z-index: 1;
  }
  .clear-btn {
    position: absolute;
    top: 0;
    right: 160px;
    z-index: 1;
  }
  .unselect-btn {
    position: absolute;
    top: 0;
    right: 240px;
    z-index: 1;
  }
  .config-mode {
    position: absolute;
    top: 0;
    right: 260px;
    z-index: 1;
  }
}
.config-preview-container {
  display: flex;
  justify-content: start;
  align-items: start;
  position: relative;
  padding-top: 30px;
  .alert {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }
}
.comp-container {
  min-height: 200px;
  width: 100%;
  margin-top: 20px;
}

.drawer-switch {
  position: fixed;
  top: 50%;
  right: 0;
  transition: right 0.3s cubic-bezier(0.7, 0.3, 0.1, 1);
  font-size: 30px;
  z-index: 1001;
  cursor: pointer;
  &:hover {
    color: #1890ff;
  }
  &.drawer-switch-show {
    right: 540px;
    transform: rotate(180deg);
    color: #fff;
    &:hover {
      color: #1890ff;
    }
  }
}
</style>
