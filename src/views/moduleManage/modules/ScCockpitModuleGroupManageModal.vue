<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    cancelText="关闭"
    destroyOnClose
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form slot="detail" :form="form">
          <a-row>
            <a-col :span="24">
              <a-form-item label="分组名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  v-decorator="[
                    'groupName',
                    { rules: [{ required: true, message: '请输入分组名称' }] }
                  ]"
                  placeholder="请输入分组名称"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="分组排序" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input-number
                  v-decorator="[
                    'groupSort',
                    { rules: [{ required: true, message: '请输入分组排序' }] }
                  ]"
                  :min="0"
                ></a-input-number>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { pick } from 'lodash-es'
export default {
  name: 'ScCockpitModuleGroupManageModal',
  components: {},
  props: {
    groupType: {
      type: String,
      default: ''
    },
    currentSelectKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
      form: this.$form.createForm(this),
      addNum: 0,
      model: {},
      selectedKey: [],
      rightClickSelectedKey: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/group/scCockpitGroup/add',
        edit: '/group/scCockpitGroup/edit'
        // queryById: '/group/scCockpitGroup/queryById',
      }
    }
  },
  methods: {
    add(num, selectedKey, rightClickSelectedKey) {
      this.addNum = num
      this.selectedKey = selectedKey
      this.rightClickSelectedKey = rightClickSelectedKey
      this.edit({})
    },
    edit(record) {
      console.log('record', record)
      this.visible = true
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'groupName', 'groupSort'))
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.submitForm()
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'post'
          }
          let formData = Object.assign(that.model, values)
          that.handleFormData(formData)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.visible = false
                that.addNum = 0
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    handleFormData(formData) {
      formData.hasChild = this.addNum ? '0' : '1'
      if (this.addNum)
        formData.pid =
          this.addNum === 1
            ? '-1'
            : this.addNum === 2
            ? this.selectedKey
            : this.rightClickSelectedKey
      formData.groupType = this.groupType
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
