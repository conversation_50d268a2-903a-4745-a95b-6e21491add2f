<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form-model :model="form" ref="form" :rules="rules" slot="detail" :disabled="false">
        <div class="title">模块基础</div>
        <a-form-model-item label="模块名称" prop="moduleName" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-model="form.moduleName" placeholder="请输入模块名称"></a-input>
        </a-form-model-item>
        <a-form-model-item label="排序" prop="sortOrder" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input-number v-model="form.sortOrder"></a-input-number>
        </a-form-model-item>
        <a-form-model-item label="数据源" prop="sourceId" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            v-model="form.sourceId"
            :tree-data="dataSourceList"
            style="width: 100%"
            placeholder="请选择数据源"
            allow-clear
            tree-default-expand-all
          >
          </a-tree-select>
        </a-form-model-item>
        <a-form-model-item prop="bgImage" style="display: flex; align-items: center" label="模块背景图片">
          <!-- bgImage -->
          <j-image-upload v-model="form.bgImage"></j-image-upload>
        </a-form-model-item>
        <a-row>
          <a-col :span="8">
            <a-form-model-item
              label="默认宽度"
              prop="moduleWidth"
              :labelCol="labelColShort"
              :wrapperCol="wrapperColShort"
            >
              <a-input-number v-model="form.moduleWidth" placeholder=""></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="最小宽度" prop="minWidth" :labelCol="labelColShort" :wrapperCol="wrapperColShort">
              <a-input-number v-model="form.minWidth"></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="最大宽度" prop="maxWidth" :labelCol="labelColShort" :wrapperCol="wrapperColShort">
              <a-input-number v-model="form.maxWidth" placeholder=""></a-input-number>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item
              label="默认长度"
              prop="moduleLength"
              :labelCol="labelColShort"
              :wrapperCol="wrapperColShort"
            >
              <a-input-number v-model="form.moduleLength" placeholder=""></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              label="最小长度"
              prop="minLength"
              :labelCol="labelColShort"
              :wrapperCol="wrapperColShort"
            >
              <a-input-number v-model="form.minLength" placeholder=""></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              label="最大长度"
              prop="maxLength"
              :labelCol="labelColShort"
              :wrapperCol="wrapperColShort"
            >
              <a-input-number v-model="form.maxLength" placeholder=""></a-input-number>
            </a-form-model-item>
          </a-col>
        </a-row>
        <div class="title">标题</div>
        <a-form-model-item label="标题名称" prop="titleName" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-model="form.titleName" placeholder="请输入标题名称"></a-input>
        </a-form-model-item>
        <a-form-model-item label="是否展示标题" prop="showTitle" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="form.showTitle">
            <a-radio :value="1"> 是 </a-radio>
            <a-radio :value="0"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item prop="titleBgPic" style="display: flex; align-items: center" label="标题背景图片">
          <j-image-upload v-model="form.titleBgPic"></j-image-upload>
        </a-form-model-item>
        <div class="title">点击事件</div>
        <a-form-model-item label="是否链接" prop="isLink" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="form.isLink">
            <a-radio :value="1"> 是 </a-radio>
            <a-radio :value="0"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="打开方式" prop="linkType" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select v-model="form.linkType" placeholder="请选择打开方式">
            <a-select-option value="0"> 内部弹窗 </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="链接地址" prop="linkUrl" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-model="form.linkUrl" placeholder="请输入链接地址"></a-input>
        </a-form-model-item>
        <a-form-model-item label="是否弹窗" prop="isPopup" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="form.isPopup">
            <a-radio :value="1"> 是 </a-radio>
            <a-radio :value="0"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="弹窗模块" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <!-- popupModuleId -->
        </a-form-model-item>
        <div class="title">组件呈现</div>
        <a-form-model-item label="是否转换空值" prop="transNull" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="form.transNull">
            <a-radio :value="1"> 是 </a-radio>
            <a-radio :value="0"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="空值样式" prop="nullValue" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-dict-select-tag
            v-model="form.nullValue"
            :triggerChange="true"
            placeholder="请选择空值样式"
            dictCode="null_type"
          >
          </j-dict-select-tag>
        </a-form-model-item>
        <a-form-model-item label="数据自动刷新" prop="isRefresh" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="form.isRefresh">
            <a-radio :value="1"> 是 </a-radio>
            <a-radio :value="0"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="刷新时间" prop="refreshTime" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input-number v-model="form.refreshTime" placeholder=""></a-input-number>
        </a-form-model-item>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>
<script>
import { getAction } from '@/api/manage'
import { traverseTree } from '@/api/api'
import { pick } from 'lodash-es'
export default {
  name: 'ScCockpitModuleConfigForm',
  data() {
    return {
      form: {
        moduleName: undefined,
        sortOrder: 0,
        sourceId: undefined,
        bgImage: undefined,
        moduleWidth: undefined,
        minWidth: undefined,
        maxWidth: undefined,
        moduleLength: undefined,
        minLength: undefined,
        maxLength: undefined,
        titleName: undefined,
        showTitle: 1,
        titleBgPic: undefined,
        isLink: 0,
        linkType: undefined,
        linkUrl: undefined,
        isPopup: 0,
        transNull: 0,
        nullValue: undefined,
        isRefresh: 1,
        refreshTime: undefined
      },
      rules: {
        moduleName: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        sortOrder: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        sourceId: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        moduleWidth: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        moduleLength: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        titleName: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        showTitle: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        isLink: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        isPopup: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        transNull: [{ required: true, message: '请选择指标集', trigger: 'change' }],
        isRefresh: [{ required: true, message: '请选择指标集', trigger: 'change' }]
      },
      confirmLoading: false,
      dataSourceList: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      labelColShort: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      wrapperColShort: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      titleBgPic: [],
      url: {
        queryindexSet: '/group/scCockpitGroup/queryByType?groupType=index'
      }
    }
  },
  created() {
    this.form = {
      moduleName: undefined,
      sortOrder: 0,
      sourceId: undefined,
      bgImage: undefined,
      moduleWidth: undefined,
      minWidth: undefined,
      maxWidth: undefined,
      moduleLength: undefined,
      minLength: undefined,
      maxLength: undefined,
      titleName: undefined,
      showTitle: 1,
      titleBgPic: undefined,
      isLink: 0,
      linkType: undefined,
      linkUrl: undefined,
      isPopup: 0,
      transNull: 0,
      nullValue: undefined,
      isRefresh: 1,
      refreshTime: undefined
    }
    this.queryindexSet()
  },
  methods: {
    getFormData(formData) {
      this.form = Object.assign(
        this.form,
        pick(
          formData,
          'isLink',
          'isPopup',
          'isRefresh',
          'moduleName',
          'moduleLength',
          'moduleWidth',
          'bgImage',
          'showTitle',
          'sortOrder',
          'sourceId',
          'titleName',
          'transNull'
        )
      )
    },
    queryindexSet() {
      getAction(this.url.queryindexSet).then((res) => {
        if (res.success) {
          res.result.forEach((node) => {
            traverseTree(node, (node) => {
              if (node.data) {
                // 将指标集data进行字段转换，并push到childList中，保留其type字段
                node.data.forEach((data) => {
                  let obj = data
                  obj.groupName = data.name
                  delete obj.name
                  if (!node.childList) node.childList = [obj]
                  else node.childList.push(obj)
                })
                delete node.data
              }
              node.key = node.id
              node.title = node.groupName
              node.children = node.childList
              node.value = node.id
              node.disabled = !!node.groupType
            })
          })
          this.dataSourceList = res.result
        }
      })
    },
    validateForm() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message.warning('模块配置表单有必填项未填写')
        } else {
          this.$emit('value', this.form)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.title {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 16px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    height: 16px;
    width: 4px;
    border-radius: 4px;
    background-color: #1890ff;
  }
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    height: 2px;
    width: 100%;
    background-color: #e8e8e8;
  }
}
::v-deep .ant-input-number {
  width: 80px;
}
</style>
