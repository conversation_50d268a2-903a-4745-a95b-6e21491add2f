<template>
  <div>
    <div class="jsEditorContainer">
      <div class="title">JS代码</div>
      <editor
        ref="jsCodeEditor"
        v-model="jsData"
        width="100%"
        height="400px"
        :options="editorOptions"
        @init="editorInit"
      ></editor>
    </div>
    <div class="cssEditorContainer">
      <div class="title">CSS代码</div>
      <editor
        ref="cssCodeEditor"
        v-model="cssData"
        width="100%"
        height="400px"
        :options="editorOptions"
        @init="editorInit"
      ></editor>
    </div>
  </div>
</template>
<script>
import { chartItem } from '../constant/constant.js'
export default {
  name: 'ScCockpitCustomCodeForm',
  components: { editor: require('vue2-ace-editor') },
  props: {
    componentType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      chartItem,
      jsData: 'option = {}',
      cssData: '',
      editorOptions: {
        enableBasicAutocompletion: true,
        enableLiveAutocompletion: true,
        enableSnippets: true,
        enableLiveAutocompletion: true,
        tabSize: 6,
        fontSize: 14,
        showPrintMargin: false, //去除编辑器里的竖线
      },
    }
  },
  mounted() {
    this.changeTheme()
    this.changeLanguage()
    this.editorInit()
    this.$refs.jsCodeEditor.editor.resize()
  },
  methods: {
    getFormData() {
      for (const item of this.chartItem) {
        const chartProp = item.content.filter((element) => {
          return element.name === this.componentType
        })[0]
        if (chartProp) {
          this.jsData = JSON.stringify(chartProp.option)
            .replace(/,/g, ',\n')
            .replace(/{(?!})/g, '{\n')
            .replace(/(?<!{)}/g, '\n$&')
          return
        }
      }
    },
    changeLanguage() {
      this.$refs.jsCodeEditor.editor.getSession().setMode('ace/mode/javascript')
      this.$refs.cssCodeEditor.editor.getSession().setMode('ace/mode/css')
    },
    changeTheme() {
      this.$refs.jsCodeEditor.editor.setTheme('ace/theme/monokai')
      this.$refs.cssCodeEditor.editor.setTheme('ace/theme/monokai')
    },
    editorInit() {
      require('brace/ext/language_tools')
      // language
      require('brace/mode/javascript')
      // theme
      require('brace/theme/monokai')
    },
    validateForm() {
      if (!this.jsData) {
        this.$message.warning('自定义代码表单-js代码不能为空')
      } else {
        this.$emit('value', {
          jsData: this.jsData.substring(this.jsData.indexOf('{'), this.jsData.lastIndexOf('}') + 1),
          cssData: this.cssData,
        })
      }
    },
  },
}
</script>
<style lang="less" scoped>
.jsEditorContainer {
  margin-bottom: 20px;
}
.cssEditorContainer {
  margin-bottom: 20px;
}
.title {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 16px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    height: 16px;
    width: 4px;
    border-radius: 4px;
    background-color: #1890ff;
  }
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    height: 2px;
    width: 100%;
    background-color: #e8e8e8;
  }
}
</style>
