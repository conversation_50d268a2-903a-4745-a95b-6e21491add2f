<template>
  <div class="charts-container">
    <div class="col">
      <div class="col-title">左列</div>
      <draggable
        v-model="leftCharts"
        :group="groupA"
        animation="300"
        dragClass="dragClass"
        ghostClass="ghostClass"
        chosenClass="chosenClass"
        @start="onStart"
        @end="onEnd"
      >
        <transition-group :style="style">
          <div
            v-for="(item, index) in leftCharts"
            :id="item.id"
            :key="item.id"
            class="chart"
            :class="{ 'chart-select': currentSelectChart === item.id }"
            @click="(e) => handleChartClick(e, index, item.id, item.name, item.componentData, item.moduleData, 'left')"
          >
            <div :ref="`chart-${index}`" class="chart-item"></div>
            <a-popconfirm
              title="确定要删除此组件吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="confirmDeleteComp(item.id, index, 'left')"
            >
              <a-icon type="close-square" class="close-btn" />
            </a-popconfirm>
            <div class="compName">{{ item.componentData.titleName || item.name }}</div>
          </div>
        </transition-group>
      </draggable>
    </div>
    <div class="col">
      <div class="col-title">右列</div>
      <draggable
        v-model="rightCharts"
        :group="groupB"
        animation="300"
        dragClass="dragClass"
        ghostClass="ghostClass"
        chosenClass="chosenClass"
        @start="onStart"
        @end="onEnd"
      >
        <transition-group :style="style">
          <div
            v-for="(item, index) in rightCharts"
            :id="item.id"
            :key="item.id"
            class="chart"
            :class="{ 'chart-select': currentSelectChart === item.id }"
            @click="(e) => handleChartClick(e, index, item.id, item.name, item.componentData, item.moduleData, 'right')"
          >
            <div :ref="`chart-${index + leftCharts.length}`" class="chart-item"></div>
            <a-popconfirm
              title="确定要删除此组件吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="confirmDeleteComp(item.id, index, 'right')"
            >
              <a-icon type="close-square" class="close-btn" />
            </a-popconfirm>
            <div class="compName">{{ item.componentData.titleName || item.name }}</div>
          </div>
        </transition-group>
      </draggable>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import draggable from 'vuedraggable'
import { getDictItemsFromCache } from '@/api/api'

export default {
  name: 'ScCockpitComponentsContainer',
  components: { draggable },
  props: {
    moduleData: {
      // 点击树节点加载的现有组件列表
      type: Object,
      default: () => []
    }
  },
  data() {
    return {
      currentSelectChart: null,
      chartInstanceList: [],
      leftCharts: [],
      rightCharts: [],
      groupA: {
        name: 'site',
        pull: true, // 可以拖入
        put: true // 可以拖出
      },
      groupB: {
        name: 'site',
        pull: true,
        put: true
      },
      style: 'min-height:250px;display: block;',
      drag: false,
      isPad: false,
      leftOffsetLeft: 0,
      rightOffsetLeft: 0,
      leftLastIndex: 0
    }
  },
  watch: {
    moduleData: {
      handler(val) {
        this.loadCompExist(val)
      },
      deep: true
    }
  },
  mounted() {
    const dom = document.querySelectorAll('.col')
    this.leftOffsetLeft = dom[0].offsetLeft + 1
    this.rightOffsetLeft = dom[1].offsetLeft + 1
  },
  methods: {
    loadCompExist(val) {
      console.log('val', val)

      if (!val) {
        this.leftCharts = []
        this.rightCharts = []
        return
      }
      const compList = getDictItemsFromCache('component_type')
      this.leftLastIndex = val.componentList.findIndex((comp) => {
        return comp.sourceId === 'separatorFlag'
      }) // 标志位索引
      const leftComponent =
        this.leftLastIndex === -1 ? val.componentList : val.componentList.slice(0, this.leftLastIndex)
      const rightComponent = this.leftLastIndex === -1 ? [] : val.componentList.slice(this.leftLastIndex + 1)
      this.leftCharts = leftComponent.map((comp) => {
        return {
          name: compList.filter((item) => {
            return item.value === comp.componentType
          })[0].text,
          id: `chart-${this.generateRandomString(4)}`,
          componentData: comp,
          moduleData: val,
          titleImg: comp.titleImg
        }
      })
      this.rightCharts = rightComponent.map((comp) => {
        return {
          name: compList.filter((item) => {
            return item.value === comp.componentType
          })[0].text,
          id: `chart-${this.generateRandomString(4)}`,
          componentData: comp,
          moduleData: val,
          titleImg: comp.titleImg
        }
      })
      console.log('left', this.leftCharts)

      this.$nextTick(() => {
        const wrapperDom = document.querySelector('.charts-container')
        const leftModuleDom = document.querySelectorAll('.col')[0]
        const rightModuleDom = document.querySelectorAll('.col')[1]
        const titleDomArr = document.querySelectorAll('.compName')
        wrapperDom.style.background = 'revert'
        leftModuleDom.style.background = 'revert'
        rightModuleDom.style.background = 'revert'

        if (this.leftCharts.length && this.rightCharts.length && val.bgImage) {
          wrapperDom.style.background = `url(${window._CONFIG['staticDomainURL']}/${val.bgImage}) center center / 100% 100%`
        } else if (this.leftCharts.length && val.bgImage) {
          leftModuleDom.style.background = `url(${window._CONFIG['staticDomainURL']}/${val.bgImage}) center center / 100% 100%`
        } else if (this.rightCharts.length && val.bgImage) {
          rightModuleDom.style.background = `url(${window._CONFIG['staticDomainURL']}/${val.bgImage}) center center / 100% 100%`
        }
        titleDomArr.forEach((titleDom) => {
          // console.log('titleDom', titleDom)
        })
      })
    },
    addComp(name) {
      const compList = getDictItemsFromCache('component_type')
      const chart = {
        name,
        id: `chart-${this.generateRandomString(4)}`,
        componentData: null,
        componentType: compList.filter((item) => {
          return item.text === name
        })[0].value,
        moduleData: this.moduleData
      }
      this.leftCharts.push(chart)
      this.$emit('addComp', chart)
    },
    generateRandomString(length) {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      let result = ''
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        result += characters[randomIndex]
      }
      return result
    },
    handleChartClick(e, index, id, name, componentData, moduleData, position) {
      e.target.nodeName !== 'svg' &&
        e.target.nodeName !== 'path' &&
        this.$emit('compId', {
          index: position === 'left' ? index : index + this.leftCharts.length + 1,
          id,
          name,
          componentData,
          moduleData
        })
      this.currentSelectChart = id
    },
    renderChart(componentList) {
      // 所有图表的数据列表
      const dataList = componentList.map((item) => {
        return item.dataList.records
      })
      if (
        dataList.some((data) => {
          return data.length === 0
        })
      ) {
        this.$message.warning('图表无数据')
      }
      // 所有图表的维度列表
      const dimenssionColumnList = componentList.map((item) => {
        return item.dimensionList
      })
      // 所有图表的指标列表
      const indexColumnList = componentList.map((item) => {
        return item.indexList
      })
      this.chartInstanceList = new Array(dataList.length).fill(null)
      for (let i = 0; i < dataList.length; i++) {
        const component = componentList[i]
        if (!component.jsData || !dataList[i].length) continue
        const option = JSON.parse(component.jsData)
        const indexColumn = JSON.parse(component.indexColumn)
        echarts.dispose(this.$refs[`chart-${i}`][0])
        this.chartInstanceList[i] = echarts.init(this.$refs[`chart-${i}`][0])
        const dimensionColumnListItem = dimenssionColumnList[i]
        // 当前图表的维度列表
        const dimenssionName = dimensionColumnListItem[0].sourceColumn
        if (component.componentType.includes('BarChart')) {
          option.yAxis.data = dataList[i].map((data) => {
            return data[dimenssionName]
          })
          option.yAxis.name = dimenssionName
        } else {
          option.xAxis.data = dataList[i].map((data) => {
            return data[dimenssionName]
          })
          option.xAxis.name = dimenssionName
        }
        const indexColumnListItem = indexColumnList[i]
        // 当前图表的指标列表
        indexColumnListItem.forEach((index, ii) => {
          const indexName = index.sourceColumn
          if (!option.series[ii]) {
            option.series[ii] = Object.assign(option.series[0], { data: [] })
          }
          option.series[ii].name = indexName
          option.series[ii].data = dataList[ii].map((data) => {
            return data[indexName]
          })
          if (component.componentType.includes('BarLineChart')) {
            option.series[ii].type = indexColumn.barIndex.includes(index.sourceColumnId) ? 'bar' : 'line'
          }
          if (component.componentType.includes('BarChart')) {
            option.xAxis.name = indexName
          } else if (component.componentType.includes('BarLineChart')) {
            option.yAxis[ii].name = indexName
          } else {
            option.yAxis.name = indexName
          }
        })
        this.chartInstanceList[i].setOption(option)
        const that = this
        window.addEventListener('resize', function () {
          that.chartInstanceList[i].resize()
        })
      }
    },
    confirmDeleteComp(id, index, position) {
      position === 'left'
        ? (this.leftCharts = this.leftCharts.filter((chart) => {
            return chart.id !== id
          }))
        : (this.rightCharts = this.rightCharts.filter((chart) => {
            return chart.id !== id
          }))
      this.currentSelectChart = null
      this.$emit('deleteComp', position === 'left' ? index : index + this.leftCharts.length + 1)
    },
    // 开始拖拽事件
    onStart(e) {
      this.drag = true
    },
    // 拖拽结束事件
    onEnd(e) {
      this.drag = false
      let newIndex = e.newIndex
      let oldIndex = e.oldIndex
      const fromOffsetLeft = e.from.offsetLeft
      const toOffsetLeft = e.to.offsetLeft
      // 拖动前后元素在componentList中的索引值，需要考虑占位的特殊元素
      if (fromOffsetLeft === this.rightOffsetLeft && toOffsetLeft === this.leftOffsetLeft) {
        // 右到左
        oldIndex += this.leftCharts.length
      } else if (toOffsetLeft === this.rightOffsetLeft && fromOffsetLeft === this.leftOffsetLeft) {
        // 左到右
        newIndex += this.leftCharts.length + 1
      } else if (toOffsetLeft === this.rightOffsetLeft && fromOffsetLeft === this.rightOffsetLeft) {
        // 右到右
        newIndex += this.leftCharts.length + 1
        oldIndex += this.leftCharts.length + 1
      }
      // this.$emit('compLayout', { newIndex, oldIndex })
    }
  }
}
</script>
<style lang="less" scoped>
.chart {
  height: 250px;
  width: 100%;
  background: #f0f2f5;
  padding: 10px;
  border: 1px solid #fff;
  position: relative;
  cursor: pointer;
  &:hover {
    border: 1px solid #1890ff;
  }
  &.chart-select {
    border: 1px solid #1890ff;
  }
  .chart-item {
    height: 100%;
    cursor: pointer;
  }
  .compName {
    position: absolute;
    top: 10px;
    left: 10px;
  }
  .close-btn {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 20px;
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }
}

.ghostClass {
  background-color: blue !important;
}
.chosenClass {
  background-color: #e6f7ff !important;
  opacity: 1 !important;
}
.dragClass {
  background-color: #e6f7ff !important;
  opacity: 1 !important;
  box-shadow: none !important;
  outline: none !important;
  background-image: none !important;
}
.itxst {
  margin: 10px;
}
.col-title {
  padding: 6px 12px;
}
.col,
.col-null {
  width: 50%;
  border: solid 1px #eee;
  border-radius: 5px;
  float: left;
  padding-bottom: 20px;
}

.item {
  padding: 6px 12px;
  margin: 0px 10px 0px 10px;
  border: solid 1px #eee;
  background-color: #f1f1f1;
}
</style>
