<template>
  <a-modal
    destroyOnClose
    title="查询组件设置"
    :width="540"
    :visible="visible"
    :body-style="{ paddingBottom: '80px' }"
    @cancel="onClose"
    @ok="handleOk"
  >
    <div>
      <a-form slot="detail" :form="form" :disabled="false">
        <a-form-item label="组件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-dict-select-tag
            v-decorator="['queryComponentType', { rules: [{ required: true, message: '请选择查询组件类型' }] }]"
            dictCode="query_component_type"
            placeholder="请选择查询组件类型"
            :trigger-change="true"
          ></j-dict-select-tag>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>
<script>
export default {
  name: 'ScCockpitQueryCompoentModal',
  data() {
    return {
      form: this.$form.createForm(this),
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
  },
  methods: {
    onClose() {
      this.visible = false
      this.$emit('close')
    },
    handleOk() {
      this.visible = false
      this.$emit('ok')
    }
  }
}
</script>
