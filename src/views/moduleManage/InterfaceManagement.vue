<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <!-- 搜索区域 -->
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item
              label="接口名称"
              :labelCol="{ span: 5 }"
              :wrapperCol="{ span: 18, offset: 1 }"
            >
              <j-input v-model="queryParam.serviceName" placeholder="请输入接口名称"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item
              label="数据表名称"
              :labelCol="{ span: 5 }"
              :wrapperCol="{ span: 18, offset: 1 }"
            >
              <j-input v-model="queryParam.tableName" placeholder="请输入数据表名称"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item
              label="接口服务名称"
              :labelCol="{ span: 5 }"
              :wrapperCol="{ span: 18, offset: 1 }"
            >
              <j-input v-model="queryParam.serviceCode" placeholder="请输入接口服务名称"></j-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :md="6" :sm="8">
              <a-form-item
                label="接口描述"
                :labelCol="{ span: 5 }"
                :wrapperCol="{ span: 18, offset: 1 }"
              >
                <j-input v-model="queryParam.serviceDesc" placeholder="请输入接口描述"></j-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" icon="search" @click="searchQuery">查询</a-button>
              <a-button type="primary" icon="reload" style="margin-left: 8px" @click="searchReset"
                >重置</a-button
              >
              <a style="margin-left: 8px" @click="handleToggleSearch">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
      <a-button type="primary" icon="delete" @click="batchDel">批量删除</a-button>
      <j-super-query
        ref="superQueryModal"
        :fieldList="superQueryFieldList"
        @handleSuperQuery="handleSuperQuery"
      />
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i>
        已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>
        项
        <a style="margin-left: 24px" @click="onClearSelected">取消选择</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :scroll="{ x: true }"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
          getCheckboxProps: (record) => ({
            props: {
              disabled: record.createType === '2'
            }
          })
        }"
        @change="handleTableChange"
      >
        <span slot="serviceCode" slot-scope="text, record">
          <span>{{ record.serviceCode }}</span>
          <a-icon
            type="exclamation-circle"
            theme="twoTone"
            style="margin-left: 15px; cursor: pointer"
            @click="handleInterfaceDetail(record.createType)"
          />
        </span>

        <span slot="status" slot-scope="status">
          <span :style="{ color: status === '1' ? 'green' : 'red' }">{{
            status === '1' ? '启用' : '停止'
          }}</span>
        </span>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a :disabled="record.createType === '3'" @click="handleViewData(record)">查看数据</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDeleteCustom(record)">
            <a :disabled="record.createType === '2'">删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <InterfaceModal ref="modalForm" @ok="modalFormOk" />
    <InterfaceDetail ref="modalDetail" />
    <DataEdit ref="dataEdit" />
  </a-card>
</template>

<script>
import JSuperQuery from '@/components/jeecg/JSuperQuery'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import InterfaceModal from './modules/InterfaceModal'
import InterfaceDetail from './modules/InterfaceDetail'
import JInput from '@/components/jeecg/JInput.vue'
import DataEdit from '@/views/data-edit/DataEdit'

export default {
  name: 'InterfaceManagement',
  components: {
    JSuperQuery,
    InterfaceModal,
    InterfaceDetail,
    JInput,
    DataEdit
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      // 表头
      // fuzzyParam: ['serviceName', 'tableName', 'serviceCode', 'serviceDesc'],
      columns: [
        {
          title: '接口名称',
          align: 'center',
          dataIndex: 'serviceName'
        },
        {
          title: '接口描述',
          align: 'center',
          dataIndex: 'serviceDesc',
          customRender(text) {
            return text || '-'
          }
        },
        {
          title: '创建方式',
          align: 'center',
          dataIndex: 'createType_dictText',
          customRender(text) {
            return text || '-'
          }
        },
        {
          title: '数据表名称',
          align: 'center',
          dataIndex: 'tableName',
          customRender(text) {
            return text || '-'
          }
        },
        {
          title: '接口服务名称',
          align: 'center',
          dataIndex: 'serviceCode',
          scopedSlots: { customRender: 'serviceCode' }
        },
        {
          title: '接口状态',
          align: 'center',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '编辑人',
          align: 'center',
          dataIndex: 'createBy',
          customRender(text) {
            return text || '-'
          }
        },
        {
          title: '编辑时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 200,
          scopedSlots: { customRender: 'action' }
        }
      ],
      superQueryFieldList: [
        { type: 'select', value: 'createType', text: '创建方式', dictCode: 'service_create_type' },
        { type: 'input', value: 'tableName', text: '数据表名称' },
        { type: 'select', value: 'status', text: '接口状态', dictCode: 'status' },
        { type: 'input', value: 'createBy', text: '编辑人' },
        { type: 'datetime', value: 'createTime', text: '编辑时间' }
      ],
      url: {
        list: '/index/scSituationIndexTopicServ/list',
        delete: '/index/scSituationIndexTopicServ/delete',
        deleteBatch: '/index/scSituationIndexTopicServ/deleteBatch'
      },
      addFlag: false,
      currentKey: ''
    }
  },
  created() {},
  methods: {
    /**
     * @description 打开接口信息说明
     * @param {String} createType
     * @returns void
     */
    handleInterfaceDetail(createType) {
      this.$refs.modalDetail.handleDetail(createType)
    },

    /**
     * @description 查看数据
     * @param {Object} record
     * @returns void
     */
    handleViewData(record) {
      if (record.createType === '3') return
      this.$refs.dataEdit.handleOpenData(record, {
        dataSource: record.dataSourceCode,
        serviceCode: record.serviceCode,
        tableName: record.tableName
      })
    },

    /**
     * @description 自定义删除方法
     * @param {Object} record
     * @returns void
     */
    handleDeleteCustom(record) {
      if (record.createType === '2') return
      this.handleDelete(record.id)
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
<style lang="less"></style>
