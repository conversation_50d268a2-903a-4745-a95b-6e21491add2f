<template>
  <a-drawer
    destroyOnClose
    title=""
    :width="540"
    :visible="visible"
    :body-style="{ paddingBottom: '80px' }"
    @close="onClose"
  >
    <div>
      <div class="footer">
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="submitForm">确定</a-button>
      </div>
      <a-tabs type="card" @change="handleTabChange">
        <a-tab-pane key="1" tab="组件配置">
          <div>
            <a-form-model ref="form" slot="detail" :model="form" :rules="rules" :disabled="false">
              <div class="title">组件基础</div>
              <!-- <a-form-item style="display: flex; align-items: center" label="背景图片">
          <j-image-upload v-model="moduleBgPic"></j-image-upload>
        </a-form-item> -->
              <a-row>
                <a-col :span="24">
                  <a-form-model-item
                    label="组件类型"
                    prop="componentType"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-tree-select
                      v-model="form.componentType"
                      :tree-data="componentTypeTreeData"
                      placeholder="请选择组件类型"
                      treeDefaultExpandAll
                      :dropdownStyle="{ maxHeight: '300px' }"
                      @change="handleComponentTypeChange"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item
                    label="默认宽度"
                    prop="defWidth"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input-number v-model="form.defWidth" placeholder=""></a-input-number>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="24">
                  <a-form-model-item
                    label="默认高度"
                    prop="defLength"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input-number v-model="form.defLength" placeholder=""></a-input-number>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row
                v-if="
                  form.componentType === 'text-container-group' ||
                  form.componentType === 'customTextGroup'
                "
              >
                <a-col :span="24">
                  <a-form-model-item label="背景图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <j-image-upload v-model="form.bgImage" placeholder=""></j-image-upload>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <div class="title">标题</div>
              <a-form-model-item
                label="标题名称"
                prop="titleName"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.titleName" placeholder="请输入标题名称"></a-input>
              </a-form-model-item>
              <!-- <a-form-model-item
                label="是否展示标题"
                prop="showTitle"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-radio-group v-model="form.showTitle">
                  <a-radio :value="1"> 是 </a-radio>
                  <a-radio :value="0"> 否 </a-radio>
                </a-radio-group>
              </a-form-model-item> -->
            </a-form-model>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="自定义代码">
          <div class="jsEditorContainer">
            <div class="title">JS代码</div>
            <a-alert
              style="margin-bottom: 20px"
              message="Echarts图表series数组长度需与指标个数一致，且xAxis与yAxis的type字段必需"
              type="info"
              show-icon
            />
            <editor
              ref="jsCodeEditor"
              v-model="form.jsData"
              width="100%"
              height="400px"
              :options="editorOptions"
              @init="editorInit"
            ></editor>
          </div>
          <!-- <div class="cssEditorContainer">
            <div class="title">CSS代码</div>
            <editor
              ref="cssCodeEditor"
              v-model="form.cssData"
              width="100%"
              height="400px"
              :options="editorOptions"
              @init="editorInit"
            ></editor>
          </div> -->
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-drawer>
</template>
<script>
import { getAction, httpAction, postAction } from '@/api/manage.js'
import { getDictItemsFromCache } from '@/api/api'
import { pick } from 'lodash-es'
import JImageUpload from '@/components/jeecg/JImageUpload.vue'
export default {
  name: 'ScCockpitModuleBindingDrawer',
  components: { JImageUpload },
  props: {
    baseParams: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        componentType: '',
        defWidth: null,
        defLength: null,
        titleName: '',
        showTitle: 0,
        isLink: 0,
        isPopup: 0,
        jsData: '',
        cssData: '',
        bgImage: ''
      },
      rules: {
        componentType: [{ required: true, message: '请选择组件类型', trigger: 'change' }],
        defWidth: [{ required: true, message: '请输入组件默认宽度', trigger: 'change' }],
        defLength: [{ required: true, message: '请输入组件默认高度', trigger: 'change' }],
        titleName: [{ required: true, message: '请输入标题内容', trigger: 'change' }],
        showTitle: [{ required: true, message: '请选择是否展示标题', trigger: 'change' }],
        isLink: [{ required: true, message: '请选择是否链接', trigger: 'change' }],
        isPopup: [{ required: true, message: '请选择是否弹窗', trigger: 'change' }]
      },
      componentTypeTreeData: [],
      componentClass: [],
      visible: false,
      moduleBgPic: '',
      bgImage: '',
      model: {},
      currentSelectComponent: null,
      editorOptions: {
        enableBasicAutocompletion: true,
        enableLiveAutocompletion: true,
        enableSnippets: true,
        tabSize: 6,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      labelColShort: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      wrapperColShort: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      url: {
        listComponent: '/component/scCockpitIndexComponent/list',
        addComponent: '/module/scCockpitModuleConfig/add',
        editComponent: '/module/scCockpitModuleConfig/edit'
      }
    }
  },
  methods: {
    edit(record) {
      console.log('record', record)
      this.visible = true
      this.resetForm()
      if (record) {
        this.model = Object.assign({}, record)
        this.form = Object.assign(
          this.form,
          pick(record.componentList[record.componentList.length - 1], ...Object.keys(this.form))
        )
        const cssData = JSON.parse(record.componentList[record.componentList.length - 1].cssData)
        this.form.defLength = cssData.height
        this.form.defWidth = cssData.width
        this.handleOptionSeries()
      }
      this.initComponentTypeTree()
    },
    onClose() {
      this.visible = false
      this.$emit('closeDrawer')
    },

    changeLanguage() {
      this.$refs.jsCodeEditor.editor.getSession().setMode('ace/mode/javascript')
      // this.$refs.cssCodeEditor.editor.getSession().setMode('ace/mode/javascript')
    },
    changeTheme() {
      this.$refs.jsCodeEditor.editor.setTheme('ace/theme/chrome')
      // this.$refs.cssCodeEditor.editor.setTheme('ace/theme/chrome')
    },
    editorInit() {
      // language
      require('brace/mode/javascript')
      // theme
      require('brace/theme/chrome')
    },
    handleComponentTypeChange(type) {
      this.resetForm()
      this.form.componentType = type
      if (!type) return
      getAction(this.url.listComponent, { componentCode: type }).then((res) => {
        if (res.success && res.result.records.length) {
          this.currentSelectComponent = res.result.records[0]
          this.form.jsData = this.currentSelectComponent.jsData || ''
          this.form.cssData = this.currentSelectComponent.cssData || ''
          this.handleOptionSeries()
        }
      })
    },
    handleOptionSeries() {
      if (!this.form.jsData) return
      let option = {}
      const indexList = this.baseParams.indexColumnInfo
      try {
        option = new Function('return ' + this.form.jsData)() // 字符串转对象，并去掉了'option = ''
      } catch (error) {
        console.error('解析字符串代码时出错:', error)
      }
      while (indexList.length > option.series?.length) {
        option.series.push(option.series[0])
      }
      this.form.jsData = `option = ${JSON.stringify(option, null, 2)}`
        .replace(/"([^"]+)":/g, '$1:') // 移除键的双引号（如 "name": → name:）
        .replace(/:\s*"([^"]*)"/g, ": '$1'")
    },
    initComponentTypeTree() {
      getAction(this.url.listComponent, { pageNo: 1, pageSize: 100, componentClass: 'index' }).then(
        (res) => {
          this.componentTypeTreeData = []
          for (const component of res.result.records) {
            if (
              !this.componentTypeTreeData.some((root) => {
                return root.title === component.componentType_dictText
              })
            ) {
              this.componentTypeTreeData.push({
                key: component.componentType + '-type',
                value: component.componentType + '-type',
                title: component.componentType_dictText,
                disabled: true,
                children: []
              })
              this.componentTypeTreeData[this.componentTypeTreeData.length - 1].children.push({
                key: component.componentCode,
                pId: component.componentType + '-type',
                value: component.componentCode,
                title: component.componentName,
                isLeaf: true
              })
            } else {
              const index = this.componentTypeTreeData.findIndex((item) => {
                return item.title === component.componentType_dictText
              })
              this.componentTypeTreeData[index].children.push({
                key: component.componentCode,
                pId: component.componentType,
                value: component.componentCode,
                title: component.componentName,
                isLeaf: true
              })
            }
          }
        }
      )
    },
    handleTabChange(key) {
      if (key === '2') {
        this.$nextTick(() => {
          this.changeTheme()
          this.changeLanguage()
          this.editorInit()
          this.$refs.jsCodeEditor.editor.resize()
        })
      }
    },
    submitForm() {
      let indexColumn = {},
        dimensionColumn = {}
      for (const column of this.baseParams.indexColumnInfo) {
        indexColumn[column.sourceColumnId] = column.sourceColumn
      }
      for (const column of this.baseParams.dimensionColumnInfo) {
        dimensionColumn[column.sourceColumnId] = column.sourceColumn
      }
      let formData = Object.assign({}, this.model)
      this.form.cssData = JSON.stringify({
        height: this.form.defLength,
        width: this.form.defWidth
      })
      formData = Object.assign(formData, {
        // 填充必填字段
        moduleName: this.form.titleName,
        titleName: this.form.titleName,
        showTitle: this.form.showTitle,
        moduleType: 'index',
        sourceId: this.baseParams.sourceId,
        moduleLength: 0,
        moduleWidth: 0,
        isRefresh: 0,
        isPopup: 0,
        groupId: this.baseParams.groupId,
        sortOrder: 1,
        transNull: 0,
        componentList: [
          {
            ...this.form,
            transNull: 0,
            tableName: this.baseParams.tableName,
            indexId: this.baseParams.indexId,
            indexColumn: JSON.stringify(indexColumn),
            dimensionColumn: JSON.stringify(dimensionColumn)
          }
        ]
      })
      this.$refs.form.validate((valid) => {
        if (valid) {
          const url = this.model.id ? this.url.editComponent : this.url.addComponent
          const method = this.model.id ? 'post' : 'post'
          if (this.model.id && formData.moduleData) delete formData.moduleData
          console.log('formData', formData)
          httpAction(url, formData, method).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.$emit('ok', {
                componentType: this.form.componentType,
                bgImage: this.form.bgImage,
                jsData: this.form.jsData,
                cssData: this.form.cssData,
                tableName: this.baseParams.tableName,
                indexId: this.baseParams.indexId,
                indexColumnInfo: this.baseParams.indexColumnInfo,
                dimensionColumnInfo: this.baseParams.dimensionColumnInfo
              })
              this.visible = false
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      })
    },
    resetForm() {
      this.form = {
        componentType: '',
        defWidth: null,
        defLength: null,
        titleName: '',
        showTitle: 0,
        isLink: 0,
        isPopup: 0,
        jsData: '',
        cssData: '',
        bgImage: ''
      }
    }
  }
}
</script>
<style lang="less" scoped>
.title {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 16px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    height: 16px;
    width: 4px;
    border-radius: 4px;
    background-color: #1890ff;
  }
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    height: 2px;
    width: 100%;
    background-color: #e8e8e8;
  }
}
::v-deep .ant-input-number {
  width: 80px;
}
.jsEditorContainer {
  margin-bottom: 20px;
}
::v-deep .ant-drawer-body {
  position: relative;
}
.footer {
  position: absolute;
  top: 10px;
  right: 30px;
  z-index: 1;
  > button {
    margin-right: 20px;
  }
}
</style>
