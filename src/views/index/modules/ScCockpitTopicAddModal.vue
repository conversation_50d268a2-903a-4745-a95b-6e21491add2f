<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    cancelText="关闭"
    destroyOnClose
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form-model ref="form" slot="detail" :model="form" :rules="rules">
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="专题名称"
                prop="topicName"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.topicName" placeholder="请输入专题名称"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="专题编码"
                prop="topicCode"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.topicCode" placeholder="请输入专题编码"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="专题描述"
                prop="topicDescription"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.topicDescription" placeholder="请输入专题描述"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="专题图标"
                prop="topicIcon"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <j-image-upload v-model="form.topicIcon"></j-image-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="专题排序"
                prop="topicOrder"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input-number v-model="form.topicOrder" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="链接类型"
                prop="linkType"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-radio-group v-model="form.linkType">
                  <a-radio :value="0"> 无链接 </a-radio>
                  <a-radio :value="1"> 内部链接 </a-radio>
                  <a-radio :value="2"> 外部链接 </a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="专题地址"
                prop="topicUrl"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.topicUrl" placeholder="请输入专题地址"> </a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="专题类型"
                prop="topicType"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-radio-group v-model="form.topicType">
                  <a-radio value="topic"> 专题画布 </a-radio>
                  <a-radio value="group"> 专题分组 </a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { pick } from 'lodash-es'
export default {
  name: 'ScCockpitTopicAddModal',
  components: {},
  props: {
    groupType: {
      type: String,
      default: ''
    },
    currentSelectKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
      form: {
        topicName: '',
        topicType: 'topic',
        topicDescription: '',
        topicCode: '',
        pid: '',
        relWorkbench: '',
        status: 1,
        topicIcon: '',
        topicOrder: 0,
        linkType: 0,
        topicUrl: ''
      },
      rules: {
        topicName: [{ required: true, message: '请输入专题名称', trigger: 'change' }],
        topicCode: [{ required: true, message: '请输入专题编码', trigger: 'change' }],
        topicDescription: [{ required: true, message: '请输入专题描述', trigger: 'change' }]
      },
      model: {},
      selectedKey: [],
      rightClickSelectedKey: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/topic/scCockpitTopicInfo/add'
      }
    }
  },
  methods: {
    add(record) {
      this.visible = true
      this.form = {
        topicName: '',
        topicType: 'topic',
        topicDescription: '',
        topicCode: '',
        status: 1,
        pid: record.pid,
        relWorkbench: record.relWorkbench,
        topicIcon: '',
        topicOrder: 0,
        linkType: 0,
        topicUrl: ''
      }
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.submitForm()
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let formData = Object.assign(that.model, this.form)
          formData.topicJson = console.log('表单提交数据', formData)
          httpAction(that.url.add, formData, 'post')
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.visible = false
                that.addNum = 0
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
