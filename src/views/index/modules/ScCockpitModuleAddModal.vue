<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    cancelText="关闭"
    destroyOnClose
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form-model ref="form" slot="detail" :model="form" :rules="rules">
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="模块名称"
                prop="moduleName"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.moduleName" placeholder="请输入模块名称"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="标题名称"
                prop="titleName"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.titleName" placeholder="请输入标题名称"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="模块默认高度"
                prop="moduleLength"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input-number
                  v-model="form.moduleLength"
                  placeholder="请输入模块默认高度"
                ></a-input-number>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="模块默认宽度"
                prop="moduleWidth"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input-number
                  v-model="form.moduleWidth"
                  placeholder="请输入模块默认宽度"
                ></a-input-number>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="模块描述"
                prop="moduleDes"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-textarea v-model="form.moduleDes" placeholder="请输入模块描述"></a-textarea>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="是否弹窗"
                prop="isPopup"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-radio-group v-model="form.isPopup">
                  <a-radio :value="1"> 是 </a-radio>
                  <a-radio :value="0"> 否 </a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { pick } from 'lodash-es'
export default {
  name: 'ScCockpitModuleAddModal',
  components: {},
  props: {
    groupType: {
      type: String,
      default: ''
    },
    currentSelectKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
      form: {
        moduleName: '',
        titleName: '',
        moduleLength: 0,
        moduleWidth: 0,
        moduleDes: '',
        moduleType: 'module',
        topicId: ''
      },
      rules: {
        moduleName: [{ required: true, message: '请输入模块名称', trigger: 'change' }],
        titleName: [{ required: true, message: '请选择标题名称', trigger: 'change' }],
        moduleLength: [{ required: true, message: '请输入模块高度', trigger: 'change' }],
        moduleWidth: [{ required: true, message: '请输入模块宽度', trigger: 'change' }],
        moduleDes: [{ required: true, message: '请输入模块描述', trigger: 'change' }]
      },
      selectedKey: [],
      rightClickSelectedKey: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/module/scCockpitModuleConfig/add'
      }
    }
  },
  methods: {
    add(record) {
      this.visible = true
      this.form = {
        moduleName: '',
        titleName: '',
        moduleLength: 0,
        moduleWidth: 0,
        moduleDes: '',
        moduleType: 'module',
        topicId: record.topicId
      }
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.submitForm()
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let formData = this.form
          console.log('表单提交数据', formData)
          httpAction(that.url.add, formData, 'post')
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.visible = false
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
