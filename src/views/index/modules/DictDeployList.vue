<template>
  <a-card :bordered="false">
    <!-- 抽屉 -->
    <a-drawer title="字典配置列表" :width="screenWidth" :visible="visible" @close="onClose">
      <!-- 抽屉内容的border -->
      <div
        :style="{
          padding: '10px',
          border: '1px solid #e9e9e9',
          background: '#fff',
        }"
      >
        <div class="table-page-search-wrapper">
          <a-form layout="inline" :form="form" @keyup.enter.native="searchQuery">
            <a-row :gutter="10">
              <a-col :md="8" :sm="12">
                <a-form-item label="名称">
                  <j-input v-model="queryParam.itemText" style="width: 120px" placeholder="请输入名称"></j-input>
                </a-form-item>
              </a-col>
              <a-col :md="9" :sm="24">
                <a-form-item label="状态" style="width: 170px" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select v-model="queryParam.status" placeholder="请选择状态">
                    <a-select-option value="1">启用</a-select-option>
                    <a-select-option value="0">停用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="7" :sm="24">
                <span style="float: left" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery">搜索</a-button>
                  <a-button type="primary" style="margin-left: 8px" @click="searchReset">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div>
          <a-table
            ref="table"
            rowKey="id"
            size="middle"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowClassName="getRowClassname"
            @change="handleTableChange"
          >
            <!-- 状态渲染模板 -->
            <template slot="customRenderStatus" slot-scope="status">
              <a-tag v-if="status == 0" color="red">停用</a-tag>
              <a-tag v-if="status == 1" color="green">启用</a-tag>
            </template>
          </a-table>
        </div>
      </div>
    </a-drawer>
  </a-card>
</template>

<script>
import { filterObj } from '@/utils/util'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'DictItemList',
  mixins: [JeecgListMixin],
  data() {
    return {
      columns: [
        {
          title: '名称',
          align: 'center',
          width: 100,
          dataIndex: 'itemText',
        },
        {
          title: '数据值',
          align: 'center',
          width: 130,
          dataIndex: 'itemValue',
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status',
          scopedSlots: { customRender: 'customRenderStatus' },
          filterMultiple: false,
        },
      ],
      queryParam: {
        dictId: '',
        dictName: '',
        itemText: '',
        delFlag: '1',
        status: [],
      },
      title: '操作',
      visible: false,
      screenWidth: 800,
      model: {},
      dictId: '',
      status: 1,
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 12 },
      },
      form: this.$form.createForm(this),
      validatorRules: {
        itemText: { rules: [{ required: true, message: '请输入名称!' }] },
        itemValue: { rules: [{ required: true, message: '请输入数据值!' }] },
      },
      url: {
        list: '/sys/dictItem/list',
        delete: '/sys/dictItem/delete',
        deleteBatch: '/sys/dictItem/deleteBatch',
      },
    }
  },
  created() {
    // 当页面初始化时,根据屏幕大小来给抽屉设置宽度
    this.resetScreenSize()
  },
  methods: {
    add(dictId) {
      this.dictId = dictId
      this.edit({})
    },
    edit(record) {
      if (record.id) {
        this.dictId = record.id
      }
      this.queryParam = {}
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.model.dictId = this.dictId
      this.model.status = this.status
      this.visible = true
      this.loadData()
    },
    getQueryParams() {
      this.ipagination.total = 0
      var param = Object.assign({}, this.queryParam)
      param.dictId = this.dictId
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      if (this.superQueryParams) {
        param['superQueryParams'] = encodeURI(this.superQueryParams)
        param['superQueryMatchType'] = this.superQueryMatchType
      }
      return filterObj(param)
    },
    showDrawer() {
      this.visible = true
    },
    onClose() {
      this.visible = false
      this.form.resetFields()
      this.dataSource = []
    },
    // 抽屉的宽度随着屏幕大小来改变
    resetScreenSize() {
      const screenWidth = document.body.clientWidth
      if (screenWidth < 600) {
        this.screenWidth = screenWidth
      } else {
        this.screenWidth = 600
      }
    },
    // 增加样式方法返回值
    getRowClassname(record) {
      if (record.status == 0) {
        return 'data-rule-invalid'
      }
    },
  },
}
</script>

<style lang="less" scoped>
/deep/ .data-rule-invalid {
  background: #f4f4f4;
  color: #bababa;
}
</style>
