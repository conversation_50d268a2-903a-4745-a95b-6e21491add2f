<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    cancelText="关闭"
    destroyOnClose
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <sc-cockpit-index-set-config-form
      ref="realForm"
      :disabled="disableSubmit"
      @ok="submitCallback"
    ></sc-cockpit-index-set-config-form>
  </j-modal>
</template>

<script>
import ScCockpitIndexSetConfigForm from './ScCockpitIndexSetConfigForm'
export default {
  name: 'ScCockpitIndexSetConfigModal',
  components: {
    ScCockpitIndexSetConfigForm,
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add(groupId) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add(groupId)
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  max-height: 70vh;
  overflow-y: scroll;
}
</style>
