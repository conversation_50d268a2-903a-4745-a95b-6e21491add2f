<!-- 数据预览 -->
<template>
  <div>
    <data-edit ref="dataEdit" :dataSourceCode="baseParams.tableSourceCode"></data-edit>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import DataEdit from './DataEditNew.vue'
export default {
  name: 'ScCockpitIndexPreview',
  components: { DataEdit },

  data() {
    return {
      disableMixinLoad: true,
      baseParams: {},
      queryInfo: [],
      superQueryFieldList: [
        // { type: 'select', value: 'createType', text: '创建方式', dictCode: 'service_create_type' },
        // { type: 'input', value: 'tableName', text: '数据表名称' },
        // { type: 'select', value: 'status', text: '接口状态', dictCode: 'status' },
        // { type: 'input', value: 'createBy', text: '编辑人' },
        // { type: 'datetime', value: 'createTime', text: '编辑时间' }
      ],
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        }
      ],
      url: {
        queryIndexSetInfoById: '/index/scCockpitIndexGroupConfig/queryById',
        queryDataSourceById: '/data/scCockpitDataSource/queryById'
      }
    }
  },
  methods: {
    async init(record) {
      this.$refs.dataEdit.handleOpenData(record, {
        tableName: this.baseParams.tableName,
        dataSourceCode: this.baseParams.tableSourceCode,
        columns: this.baseParams.sourceColumn,
        isSql: this.baseParams.isSql,
        selectSql: this.baseParams.selectSql,
        sourceId: this.baseParams.sourceId
      })
    },
    changePreviewData(value) {
      this.baseParams = value
    },
    changeQueryInfo(value) {
      this.queryInfo = value
    }
  }
}
</script>
