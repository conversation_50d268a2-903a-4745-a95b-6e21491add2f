<template>
  <div style="position: relative">
    <a-button type="primary" @click="showBindingDrawer">组件配置</a-button>
    <div v-if="!!chartInfo" class="switch-container">
      <div :class="checked ? 'tip' : 'tip-hidden'">深色模式</div>
      <a-switch v-model="checked" class="switch" />
    </div>
    <div
      v-if="!!chartInfo"
      class="componentContainer"
      :style="{ backgroundColor: checked ? '#1b6cbc' : '#fff' }"
    >
      <div v-if="isEcharts" ref="chart"></div>
      <component :is="chartInfo.componentType" v-else ref="extraChart"> </component>
    </div>
    <ScCockpitModuleBindingDrawer
      ref="drawerForm"
      :baseParams="baseParams"
      @ok="(values) => handleComponentInfo(values)"
    ></ScCockpitModuleBindingDrawer>
  </div>
</template>
<script>
import ScCockpitModuleBindingDrawer from './ScCockpitModuleBindingDrawer.vue'
import * as echarts from 'echarts'
import textContainerGroup from '../components/textContainerGroup.vue'
import customTextGroup from '../components/customTextGroup.vue'
import tableComp from '@/views/index/components/tableComp.vue'
import { pick } from 'lodash-es'
import { getAction, postAction } from '@/api/manage'
import { nonEchartsComp } from '../constant/nonEchartsComp'
export default {
  name: 'ScCockpitModuleBinding',
  components: { ScCockpitModuleBindingDrawer, textContainerGroup, customTextGroup, tableComp },
  data() {
    return {
      chartInstance: null,
      chartInfo: null,
      baseParams: {},
      tableSourceCode: '',
      bindingModuleData: null,
      specialChart: ['pie', 'radar', 'ring', 'rose'],
      nonEchartsComp,
      checked: false,
      where: [],
      url: {
        list: '/index/scCockpitIndexGroupConfig/previewDataWithOutSave',
        listWithSql: '/topic/config/testSQL',
        queryBindingComp: '/module/scCockpitModuleConfig/queryByIndexId'
      }
    }
  },
  computed: {
    isEcharts() {
      return !nonEchartsComp.some((item) => {
        return item.value === this.chartInfo.componentType
      })
    }
  },
  mounted() {
    this.$bus.$on('where', (where) => {
      this.where = where
    })
  },
  methods: {
    init(record, bindingModuleData) {
      this.bindingModuleData = bindingModuleData
      this.baseParams.indexId = record.id
      this.baseParams.groupId = record.groupId
      this.checked = false
      if (bindingModuleData) {
        const componentData = JSON.parse(bindingModuleData.moduleData).componentList[0]

        const values = {
          bgImage: componentData.bgImage,
          componentType: componentData.componentType,
          jsData: componentData.jsData,
          cssData: componentData.cssData,
          tableName: componentData.tableName,
          indexColumnInfo: componentData.indexList.map((index) => {
            return {
              sourceColumn: index.sourceColumn,
              sourceColumnId: index.sourceColumnId,
              indexName: index.indexName,
              unit: index.unit
            }
          }),
          dimensionColumnInfo: componentData.dimensionList.map((dimension) => {
            return {
              sourceColumn: dimension.sourceColumn,
              sourceColumnId: dimension.sourceColumnId,
              indexName: dimension.indexName
            }
          })
        }
        this.handleComponentInfo(values, 1)
      } else {
        // 组件信息查询接口报错
        this.chartInfo = null
        this.$refs.chart && echarts.dispose(this.$refs.chart)
        this.chartInstance = null
      }
    },
    changeComponentData(data) {
      this.tableSourceCode = data.tableSourceCode
      this.baseParams = Object.assign(
        this.baseParams,
        pick(
          data,
          'tableName',
          'sourceColumn',
          'indexColumnInfo',
          'dimensionColumnInfo',
          'isSql',
          'selectSql',
          'indexId'
        )
      )
    },
    showBindingDrawer() {
      this.$refs.drawerForm.edit(this.bindingModuleData)
    },
    async handleComponentInfo(values, flag = 0) {
      !flag &&
        (await getAction(this.url.queryBindingComp, { indexId: this.baseParams.indexId }).then(
          (res) => {
            if (res.success) {
              this.bindingModuleData = res.result
              JSON.parse(this.bindingModuleData.moduleData).componentList[0].indexList.map(
                (index, i) => {
                  if (values.indexColumnInfo[i]) values.indexColumnInfo[i].unit = index.unit
                }
              )
            }
          }
        ))
      this.chartInfo = values
      this.$nextTick(async () => {
        const indexColumn = values.indexColumnInfo.map((item) => {
          return item.sourceColumn
        })
        const dimensionColumn = values.dimensionColumnInfo.map((item) => {
          return item.sourceColumn
        })
        const params = {
          columns: [...indexColumn, ...dimensionColumn],
          connType: 'And',
          dataSourceCode: this.tableSourceCode,
          pageNo: 1,
          pageSize: 1000,
          tableName: values.tableName,
          where: this.where
        }
        const res = this.baseParams.isSql
          ? await postAction(this.url.listWithSql, {
              sql: this.baseParams.selectSql,
              parameter: {}
            })
          : await postAction(this.url.list, params)
        let data = []
        if (res.success) {
          data = res.result.records
          this.$refs.extraChart &&
            this.$refs.extraChart.setData(
              data,
              values,
              this.bindingModuleData.showTitle ? this.bindingModuleData.titleName : ''
            )
          if (this.isEcharts) {
            const jsData = values.jsData.split('=')[1].trim()
            let option = {}
            try {
              option = new Function('return ' + jsData)()
              this.renderChart(option, data, values.indexColumnInfo, values.dimensionColumnInfo)
            } catch (error) {
              console.error('解析字符串代码时出错:', error)
              return
            }
          }
        }
      })
    },
    renderChart(option, data, indexColumnInfo, dimensionColumnInfo) {
      echarts.dispose(this.$refs.chart)
      this.chartInstance = null
      this.chartInstance = echarts.init(this.$refs.chart)
      // 获取维度数据
      let dimenssionData = [],
        indexData = []
      for (const dimenssion of dimensionColumnInfo) {
        dimenssionData.push({
          name: dimenssion.indexName,
          data: data.map((item) => {
            return item[dimenssion.sourceColumn]
          })
        })
      }
      for (const index of indexColumnInfo) {
        indexData.push({
          name: index.indexName,
          data: data.map((item) => {
            return item[index.sourceColumn]
          }),
          unit: index.unit
        })
      }
      if (option && !this.specialChart.includes(option.series[0].type)) {
        option.title = {
          text: this.bindingModuleData.showTitle ? this.bindingModuleData.titleName : '',
          left: 'center'
        }
        if (option.yAxis.type === 'value') {
          // 指标为纵轴
          option.xAxis.data = dimenssionData[0].data
          option.xAxis.name = dimenssionData[0].name
          if (option.yAxis[0])
            option.yAxis.map((axis, i) => {
              axis.name = '单位: ' + indexData[i].unit
            })
          else option.yAxis.name = '单位: ' + indexData[0].unit
          for (const index in indexData) {
            if (!option.series[index]) break
            option.series[index].data = indexData[index].data
            option.series[index].name = indexData[index].name
          }
        } else if (option.xAxis.type === 'value') {
          // 指标为横轴
          option.yAxis.data = dimenssionData[0].data
          option.yAxis.name = dimenssionData[0].name
          if (option.xAxis[0])
            option.yAxis.map((axis, i) => {
              axis.name = '单位: ' + indexData[i].unit
            })
          else option.xAxis.name = '单位: ' + indexData[0].unit
          for (const index in indexData) {
            if (!option.series[index]) break
            option.series[index].data = indexData[index].data
            option.series[index].name = indexData[index].name
          }
        }
        if (option.legend) {
          option.legend.data = indexData.map((item) => {
            return item.name
          })
        }
      } else if (
        option.series[0].type === 'pie' ||
        option.series[0].type === 'ring' ||
        option.series[0].type === 'rose'
      ) {
        if (!dimenssionData.length) {
          option.series[0].data = indexData.map((item, index) => {
            return {
              value: item.data[0],
              name: item.name
            }
          })
          option.legend.data = indexData.map((item) => item.name)
          option.series = option.series.filter((item) => item.data.length)
        } else {
          option.series[0].data = dimenssionData[0].data.map((item, index) => {
            return {
              value: indexData[0].data[index].trim(),
              name: item
            }
          })
          if (option.legend) {
            option.legend.data = dimenssionData[0].data
          }
        }
      } else if (option.series[0].type === 'radar') {
        for (const i in dimenssionData[0].data) {
          option.radar.indicator[i] = {
            name: dimenssionData[0].data[i],
            max: this.findMax(i, indexData)
          }
        }
        indexData.forEach((data, i) => {
          option.series[0].data[i] = {
            value: data.data,
            name: data.name
          }
          option.legend.data[i] = data.name
        })
      }
      this.chartInstance.setOption(option)
      const that = this
      window.addEventListener('resize', function () {
        that.chartInstance.resize()
      })
    },
    findMax(i, indexData) {
      let max = 0
      indexData.forEach((item) => {
        max = Math.max(item.data[i], max)
      })
      return max * 1.1
    }
  }
}
</script>
<style lang="less" scoped>
.componentContainer {
  margin-top: 20px;
  height: 500px;
  width: 100%;
  overflow: scroll;
  display: flex;
  justify-content: center;
  > div {
    height: 100%;
    width: 100%;
  }
}
.switch-container {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  display: flex;
  justify-content: end;
  .tip {
    margin-right: 10px;
    opacity: 1;
    transition: opacity 0.5s linear;
    font-weight: bold;
  }
  .tip-hidden {
    margin-right: 10px;
    opacity: 0;
  }
}
</style>
