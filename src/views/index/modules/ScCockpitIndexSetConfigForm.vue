<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" slot="detail" :model="form" :rules="rules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              label="指标集名称"
              prop="indexGroupName"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input v-model="form.indexGroupName" placeholder="请输入指标集名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="数据源"
              prop="tableSourceCode"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-model="form.tableSourceCode"
                showSearch
                optionFilterProp="label"
                placeholder="请选择数据源"
                @change="handleChangeTableSourceCode"
              >
                <a-select-option
                  v-for="item in tableSourceList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.code"
                >{{ item.name }}</a-select-option
                >
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item
              label="是否自定义查询"
              prop="isSql"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-radio-group v-model="form.isSql" name="isSqlGroup" @change="handleIsSqlChange">
                <a-radio :value="1"> 是 </a-radio>
                <a-radio :value="0"> 否 </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col v-if="!form.isSql" :span="24">
            <a-form-model-item
              label="数据集"
              prop="sourceId"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-model="form.sourceId"
                showSearch
                optionFilterProp="label"
                placeholder="请选择数据集"
                @change="handleChangeDataSource"
              >
                <a-select-option
                  v-for="item in dataSourceList"
                  :key="item.id"
                  :label="item.tableName"
                  :value="item.id"
                >{{ item.tableName }}（{{ item.tableComment }}）</a-select-option
                >
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="指标集类型"
              prop="indexGroupType"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <j-dict-select-tag
                v-model="form.indexGroupType"
                disabled
                dictCode="index_group_type"
                placeholder="请选择指标集类型"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="排序"
              prop="sortOrder"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input-number v-model="form.sortOrder" :min="0"></a-input-number>
            </a-form-model-item>
          </a-col>

          <!-- <a-col :span="24">
            <a-form-item label="sql" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                v-decorator="['sortOrder', { rules: [{ required: true, message: '请输入排序' }] }]"
                :min="0"
              ></a-input-number>
            </a-form-item>
          </a-col> -->
          <a-col v-if="form.isSql" :span="24">
            <a-form-model-item prop="selectSql">
              <div class="sql-editor-container">
                <SQLEditor
                  ref="SQLEditor"
                  v-model="form.selectSql"
                  :isShowSqlFormatButton="false"
                  :sqlString="model.selectSql"
                  @input="getSqlContent"
                />
              </div>
              <div class="sql-operate-button-container">
                <a-button @click="formatSqlContent">格式化SQL</a-button>
                <a-button style="margin-left: 10px" @click="testSqlContent">数据验证</a-button>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="使用字段"
              prop="sourceColumn"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-model="form.sourceColumn"
                mode="multiple"
                optionFilterProp="label"
                placeholder="请选择字段"
                @select="handleColumnsSelect"
                @deselect="handleColumnsDeSelect"
              >
                <a-select-option
                  v-for="item in tableColumns"
                  :key="item.columnName"
                  :label="item.columnName"
                  :value="item.columnName"
                >{{ item.columnName
                }}{{
                  item.columnComment ? '（' + item.columnComment + '）' : ''
                }}</a-select-option
                >
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="指标集编码"
              prop="indexGroupCode"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input v-model="form.indexGroupCode" placeholder="请输入指标集编码"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col v-if="form.isSql" :span="24">
            <a-form-model-item
              label="周期库表"
              prop="relUpdateTable"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-model="form.relUpdateTable"
                showSearch
                optionFilterProp="label"
                placeholder="请选择周期库表"
                allowClear
              >
                <a-select-option
                  v-for="item in dataSourceList"
                  :key="item.tableName"
                  :label="item.tableName"
                  :value="item.tableName"
                >{{ item.tableName }}（{{ item.tableComment }}）</a-select-option
                >
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
    <div>
      <div class="title">指标</div>
      <j-editable-table
        ref="table1"
        class="j-table"
        :dataSource="currentSelectTableColumnsIndex"
        :columns="columnsIndex"
      >
        <template #action="props">
          <a-icon type="down-square" class="transfer" @click="transferIndexType(props)" />
        </template>
        <template #tableName="props">
          <j-ellipsis :value="props.value" :length="20"></j-ellipsis>
        </template>
      </j-editable-table>
    </div>
    <div>
      <div class="title">维度</div>
      <j-editable-table
        ref="table2"
        class="j-table"
        :dataSource="currentSelectTableColumnsDimenssion"
        :columns="columnsDimenssion"
      >
        <template #action="props">
          <a-icon type="up-square" class="transfer" @click="transferIndexType(props)" />
        </template>
        <template #tableName="props">
          <j-ellipsis :value="props.value" :length="20"></j-ellipsis>
        </template>
        <template #dictCode="props">
          <a-button @click="handleCheckDict(props.index, 'dimenssion')">{{
            props.value || '选择字典表'
          }}</a-button>
        </template>
      </j-editable-table>
    </div>
    <div>
      <div class="title">查询字段</div>
      <a-button type="primary" style="margin-bottom: 20px" @click="showQueryConfigModal(null)"
      >添加查询字段</a-button
      >
      <j-editable-table
        v-for="(item, index) in queryCompTableList"
        :ref="`table${3 + index}`"
        :key="item.id"
        class="j-table"
        style="text-align: center"
        :dataSource="item.data"
        :columns="item.columns"
      >
        <template #action="props">
          <a @click="showQueryConfigModal(props)">编辑</a>
          <a-divider type="vertical"></a-divider>
          <a-popconfirm title="确定删除吗?" @confirm="() => deleteQueryComp(index)">
            <a>删除</a>
          </a-popconfirm>
        </template>
        <template #isShow="props">
          <div>{{ props.value ? '是' : '否' }}</div>
        </template>
      </j-editable-table>
    </div>
    <div v-if="baseInfoSubmitBtn" class="baseinfo-btn">
      <a-button icon="edit" type="primary" @click="submitForm">保存</a-button>
      <a-button icon="reload" style="margin-left: 8px" @click="resetForm">重置</a-button>
    </div>
    <InterfaceBySQLTestModal ref="sqlTestModal" @ok="testSqlSuccess" />
    <!-- 查看字典表弹窗 -->
    <view-dictionary-table
      ref="ViewDictionarylist"
      @reciveData="handleReciveData"
    ></view-dictionary-table>
    <ScCockpitIndexSetQueryCompConfigModal
      ref="ScCockpitIndexSetQueryCompConfigModal"
      @queryCompInfo="handleQueryCompInfo"
    />
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { pick } from 'lodash-es'
import { FormTypes, validateTables, VALIDATE_NO_PASSED } from '@/utils/JEditableTableUtil'

import SQLEditor from '@/components/SQLEditor'
import InterfaceBySQLTestModal from '@/views/dataSource/modules/interfaceBySQLTestModal'
import ViewDictionaryTable from './ViewDictionaryTable'
import ScCockpitIndexSetQueryCompConfigModal from './ScCockpitIndexSetQueryCompConfigModal'
export default {
  name: 'ScCockpitIndexSetConfigForm',
  components: {
    SQLEditor,
    InterfaceBySQLTestModal,
    ViewDictionaryTable,
    ScCockpitIndexSetQueryCompConfigModal
  },
  props: {
    // 流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false
    },
    // 表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    baseInfoSubmitBtn: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: {
        indexGroupName: undefined,
        sourceId: undefined,
        indexGroupType: 'table',
        sortOrder: 0,
        isSql: 0,
        sourceColumn: [],
        queryColumn: [],
        tableSourceCode: undefined,
        indexGroupCode: '',
        selectSql: '',
        servId: '',
        relUpdateTable: undefined
      },
      rules: {
        indexGroupName: [{ required: true, message: '请输入指标集名称', trigger: 'change' }],
        sourceId: [{ required: true, message: '请选择数据集', trigger: 'change' }],
        indexGroupType: [{ required: true, message: '请选择指标集类型', trigger: 'change' }],
        sortOrder: [{ required: true, message: '请输入排序', trigger: 'change' }],
        isSql: [{ required: true, message: '请选择是否自定义查询', trigger: 'change' }],
        sourceColumn: [{ required: true, message: '请选择字段', trigger: 'change' }],
        tableSourceCode: [{ required: true, message: '请选择数据源', trigger: 'change' }],
        indexGroupCode: [{ required: true, message: '请选择指标集编码', trigger: 'change' }]
      },
      model: {},
      dataSourceList: [],
      tableColumns: [],
      currentSelectTableColumns: [],
      currentSelectTableColumnsIndex: [],
      currentSelectTableColumnsDimenssion: [],
      currentSelectTableColumnsQuery: [],
      queryCompList: [],
      queryCompTableList: [],
      tableSourceList: [],
      currentSelectTable: undefined,
      initSQLEditor: false,
      testSqlSuccessFlag: false,
      initSqlDataInEditMode: false,
      radioStyle: {
        padding: '0 5px'
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      pid: undefined,
      url: {
        add: '/index/scCockpitIndexGroupConfig/add',
        edit: '/index/scCockpitIndexGroupConfig/edit',
        queryById: '/index/scCockpitIndexGroupConfig/queryById',
        queryDataSourceList: '/index/scCockpitIndexGroupConfig/listDataSource',
        getTableSource: '/sys/dataSource/list',
        getTableColumns: '/data/scCockpitDataSource/queryById',
        getQueryCompList: '/component/scCockpitIndexComponent/list'
      },
      columnsIndex: [
        {
          title: '转换为维度',
          key: 'action',
          width: '100px',
          type: FormTypes.slot,
          slotName: 'action'
        },
        {
          title: '字段id',
          key: 'sourceColumnId',
          type: FormTypes.hidden
          // defaultValue: '',
        },
        {
          title: '表名',
          key: 'tableName',
          type: FormTypes.slot,
          slotName: 'tableName'
        },
        {
          title: '字段名',
          key: 'sourceColumn'
        },
        {
          title: '指标名称',
          key: 'indexName',
          validateRules: [{ required: true, message: '${title}不能为空' }],
          type: FormTypes.input
        },
        {
          title: '数据类型',
          key: 'dbType'
        },
        {
          title: '字段长度',
          key: 'dbLength'
        },
        {
          title: '指标类型',
          key: 'indexType',
          type: FormTypes.hidden,
          defaultValue: 'index'
        },
        {
          title: '来源类型',
          key: 'sourceType'
        },
        {
          title: '单位',
          key: 'unit',
          type: FormTypes.input
        }
      ],
      columnsDimenssion: [
        {
          title: '转换为指标',
          key: 'action',
          width: '100px',
          type: FormTypes.slot,
          slotName: 'action'
        },
        {
          title: '字段id',
          key: 'sourceColumnId',
          type: FormTypes.hidden
          // defaultValue: '',
        },
        {
          title: '表名',
          key: 'tableName',
          type: FormTypes.slot,
          slotName: 'tableName'
        },
        {
          title: '字段名',
          key: 'sourceColumn'
        },
        {
          title: '维度名称',
          key: 'indexName',
          validateRules: [{ required: true, message: '${title}不能为空' }],
          type: FormTypes.input
        },
        {
          title: '数据类型',
          key: 'dbType'
        },
        {
          title: '字段长度',
          key: 'dbLength'
        },
        {
          title: '指标类型',
          key: 'indexType',
          type: FormTypes.hidden,
          defaultValue: 'dimenssion'
        },
        {
          title: '来源类型',
          key: 'sourceType'
        },
        {
          title: '单位',
          key: 'unit',
          type: FormTypes.input
        }
      ],
      columnsInput: [
        {
          title: '操作',
          key: 'action',
          width: '100px',
          type: FormTypes.slot,
          slotName: 'action'
        },
        {
          title: '字段id',
          key: 'sourceColumnId',
          type: FormTypes.hidden
        },
        {
          title: '表名',
          key: 'tableName'
        },
        {
          title: '字段名',
          key: 'sourceColumn'
        },
        // {
        //   title: '查询控件',
        //   key: 'queryType'
        // },
        {
          title: '查询名称',
          key: 'queryName'
        }
        // {
        //   title: '是否显示',
        //   key: 'isShow'
        // },
        // {
        //   title: '运算符',
        //   key: 'queryOperator'
        // },
        // {
        //   title: '默认值',
        //   key: 'dbDefaultVal'
        // },
        // {
        //   title: '数据类型',
        //   key: 'dbType'
        // },
        // {
        //   title: '字段长度',
        //   key: 'dbLength'
        // },
        // {
        //   title: '来源类型',
        //   key: 'sourceType'
        // },
        // {
        //   title: '单位',
        //   key: 'unit',
        //   type: FormTypes.input
        // }
      ],
      columnsSelect: [
        {
          title: '操作',
          key: 'action',
          width: '100px',
          type: FormTypes.slot,
          slotName: 'action'
        },
        {
          title: '字段id',
          key: 'sourceColumnId',
          type: FormTypes.hidden
        },
        {
          title: '表名',
          key: 'tableName'
        },
        {
          title: '字段名',
          key: 'sourceColumn'
        },
        {
          title: '查询控件',
          key: 'queryType'
        },
        {
          title: '查询名称',
          key: 'queryName'
        },
        {
          title: '是否显示',
          key: 'isShow'
        },
        {
          title: '字典编码',
          key: 'dictCode'
        },
        {
          title: '字典表名',
          key: 'dictTable'
        },
        {
          title: '默认值',
          key: 'dbDefaultVal'
        },
        {
          title: '数据类型',
          key: 'dbType'
        },
        {
          title: '字段长度',
          key: 'dbLength'
        },
        {
          title: '来源类型',
          key: 'sourceType'
        },
        {
          title: '单位',
          key: 'unit',
          type: FormTypes.input
        }
      ],
      columnsDatepicker: [
        {
          title: '操作',
          key: 'action',
          width: '100px',
          type: FormTypes.slot,
          slotName: 'action'
        },
        {
          title: '字段id',
          key: 'sourceColumnId',
          type: FormTypes.hidden
        },
        {
          title: '表名',
          key: 'tableName'
        },
        {
          title: '字段名',
          key: 'sourceColumn'
        },
        {
          title: '查询控件',
          key: 'queryType'
        },
        {
          title: '查询名称',
          key: 'queryName'
        },
        {
          title: '是否显示',
          key: 'isShow',
          type: FormTypes.slot,
          slotName: 'isShow'
        },
        {
          title: '开始时间',
          key: 'startTime'
        },
        {
          title: '结束时间',
          key: 'endTime'
        },
        {
          title: '日期格式',
          key: 'dateFormatStr'
        },
        {
          title: '数据类型',
          key: 'dbType'
        },
        {
          title: '字段长度',
          key: 'dbLength'
        },
        {
          title: '来源类型',
          key: 'sourceType'
        },
        {
          title: '单位',
          key: 'unit',
          type: FormTypes.input
        }
      ]
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  watch: {
    'form.sourceColumn': {
      handler(val) {
        this.changePreviewData()
      }
    },
    queryCompTableList: {
      handler(val) {
        this.$emit(
          'changeQueryInfo',
          val.map((item) => {
            return item.data[0]
          })
        )
      },
      deep: true
    }
  },
  created() {
    if (!this.dataSourceList.length) this.getDataSourceList()
  },
  methods: {
    add(groupId) {
      this.pid = groupId
      this.edit({})
    },
    async edit(record) {
      this.resetForm()
      this.model = Object.assign({}, record)
      this.visible = true
      await this.getTableSourceList()
      if (record.id) {
        const res = await getAction(this.url.queryById, { id: record.id })
        if (res.success) {
          this.$nextTick(async () => {
            this.form = {
              ...pick(
                res.result,
                'indexGroupName',
                'indexGroupType',
                'sourceId',
                'tableSourceCode',
                'indexGroupCode',
                'servId',
                'selectSql',
                'relUpdateTable'
              ),
              sourceColumn: res.result.groupColumnList.map((item) => {
                return item.sourceColumn
              }),
              isSql: res.result.selectSql ? 1 : 0,
              sortOrder: this.model.sortOrder
            }
            this.model = Object.assign(this.model, this.form)
            this.currentSelectTable = this.dataSourceList.filter((item) => {
              return item.id === res.result.sourceId
            })[0]
            this.currentSelectTableColumns = res.result.groupColumnList.map((item) => {
              return { ...item, sourceColumnId: item.id }
            })
            this.currentSelectTableColumnsIndex = this.currentSelectTableColumns.filter((item) => {
              return item.indexType === 'index'
            })
            this.currentSelectTableColumnsDimenssion = this.currentSelectTableColumns.filter(
              (item) => {
                return item.indexType === 'dimenssion'
              }
            )
            this.queryCompTableList = res.result.queryColumnList.map((item) => {
              return {
                data: [item],
                columns: this.columnsInput
                // item.queryType === 'input'
                //   ? this.columnsInput
                //   : item.queryType === 'select'
                //   ? this.columnsSelect
                //   : item.queryType === 'datepicker'
                //   ? this.columnsDatepicker
                //   : []
              }
            })

            const res1 = await getAction(this.url.getTableColumns, {
              id: res.result.sourceId
            })
            if (res1.success) {
              this.tableColumns = res1.result.dataColumnList.map((item) => ({
                ...item,
                sourceColumn: item.columnName
              }))
            }
          })
        }
      }
    },
    changePreviewData() {
      const indexColumnInfo = this.currentSelectTableColumnsIndex.map((column) => {
        return {
          sourceColumnId: column.sourceColumnId,
          sourceColumn: column.sourceColumn,
          indexName: column.indexName
        }
      })
      const dimensionColumnInfo = this.currentSelectTableColumnsDimenssion.map((column) => {
        return {
          sourceColumnId: column.sourceColumnId,
          sourceColumn: column.sourceColumn,
          indexName: column.indexName
        }
      })
      const tableName = this.form.sourceId
        ? this.dataSourceList.filter((item) => item.id === this.form.sourceId)[0]?.tableName
        : ''
      this.$emit('changePreviewData', {
        indexColumnInfo,
        dimensionColumnInfo,
        tableName,
        ...pick(this.form, 'sourceId', 'tableSourceCode', 'sourceColumn', 'isSql', 'selectSql')
      })
    },
    async handleChangeDataSource(tableId) {
      //sourceColumnId
      this.currentSelectTableColumns = []
      this.currentSelectTableColumnsIndex = []
      this.currentSelectTableColumnsDimenssion = []
      const tableName = this.dataSourceList.filter((item) => item.id === this.form.sourceId)[0]
        .tableName
      this.form.indexGroupCode = tableName
      this.$emit('changePreviewData', {
        indexColumn: {},
        dimensionColumn: {},
        tableName,
        ...pick(this.form, 'sourceId', 'tableSourceCode', 'sourceColumn', 'isSql', 'selectSql')
      })
      this.form.sourceColumn = undefined
      this.currentSelectTable = this.dataSourceList.filter((item) => {
        return item.id === tableId
      })[0]
      if (this.currentSelectTable && this.currentSelectTable.tableComment) {
        this.form.indexGroupName = this.currentSelectTable.tableComment
      }
      this.form.indexGroupType = 'table'
      const res = await getAction(this.url.getTableColumns, {
        id: tableId
      })
      if (res.success) {
        this.tableColumns = res.result.dataColumnList.map((item) => ({
          ...item,
          sourceColumn: item.columnName,
          tableName: res.result.tableName
        }))
      }
    },
    async handleChangeTableSourceCode(tableSourceCode) {
      const res = await getAction(this.url.queryDataSourceList, { tableSourceCode })
      if (res.success) this.dataSourceList = res.result
      this.currentSelectTableColumns = []
      this.currentSelectTableColumnsIndex = []
      this.currentSelectTableColumnsDimenssion = []
      this.queryCompTableList = []
      this.form = Object.assign(this.form, {
        sourceId: undefined,
        sourceColumn: [],
        queryColumn: []
      })
    },
    /**
     * @description 点击字典表回显
     * @returns void
     */
    handleCheckDict(index, indexType) {
      this.$refs.ViewDictionarylist.showDrawerFunc(index, indexType)
    },
    /**
     * @description 接受数据
     * @returns void
     */
    handleReciveData(value) {
      const [dictCode, index, indexType] = [value.value.dictCode, value.index, value.indexType]
      if (indexType === 'index') {
        this.currentSelectTableColumnsIndex.splice(
          index,
          1,
          Object.assign(this.currentSelectTableColumnsIndex[index], { dictCode })
        )
      } else {
        this.currentSelectTableColumnsDimenssion.splice(
          index,
          1,
          Object.assign(this.currentSelectTableColumnsDimenssion[index], { dictCode })
        )
      }
    },
    getDbType(props) {
      return props.target.dataSource[0] ? props.target.dataSource[0].dbType : null
    },
    handleColumnsSelect(value) {
      // 新增使用字段时，先默认加到指标表里
      const matchedDataSource = this.dataSourceList.find((item) => item.id === this.form.sourceId)
      const tableName = matchedDataSource?.tableName || ''

      const matchedColumn = this.tableColumns.find((column) => column.columnName === value)

      if (matchedColumn) {
        this.currentSelectTableColumnsIndex.push({
          ...matchedColumn,
          sourceColumnId: matchedColumn.id, // 复制属性而非修改原对象
          indexName: matchedColumn.columnComment,
          sourceType: this.form.isSql ? 'sql' : 'table',
          indexType: 'index',
          tableName
        })
      }
    },
    handleColumnsDeSelect(value) {
      this.currentSelectTableColumnsIndex = this.currentSelectTableColumnsIndex.filter((item) => {
        return item.sourceColumn !== value
      })
      this.currentSelectTableColumnsDimenssion = this.currentSelectTableColumnsDimenssion.filter(
        (item) => {
          return item.sourceColumn !== value
        }
      )
    },
    handleQueryColumnsSelect(value) {
      const matchedDataSource = this.dataSourceList.find((item) => item.id === this.form.sourceId)
      const tableName = matchedDataSource?.tableName

      const matchedColumn = this.tableColumns.find((column) => column.columnName === value)

      if (matchedColumn) {
        this.currentSelectTableColumnsQuery.push({
          ...matchedColumn,
          sourceColumnId: matchedColumn.id, // 从原对象复制而非修改
          sourceType: this.form.isSql ? 'sql' : 'table',
          indexType: 'query',
          tableName: tableName || '' // 确保有默认值
        })
      }
    },
    handleQueryColumnsDeSelect(value) {
      this.currentSelectTableColumnsQuery = this.currentSelectTableColumnsQuery.filter((item) => {
        return item.sourceColumn !== value
      })
    },

    handleIsSqlChange(e) {
      this.form = Object.assign(this.form, {
        indexGroupType: e.target.value ? 'sql' : 'table',
        isSql: e.target.value,
        sourceColumn: [],
        sourceId: ''
      })
      this.queryCompTableList = []
      this.currentSelectTableColumnsIndex = []
      this.currentSelectTableColumnsDimenssion = []
      this.initSQLEditor = !!this.form.isSql
      this.form.relUpdateTable = undefined
      if (!this.form.isSql) {
        // 重置form的SQL字段
        this.$nextTick(() => {
          this.form.selectSql = null
        })
      } else {
        this.form.selectSql = null // 重置form的SQL字段
        this.$nextTick(() => {
          this.$refs.SQLEditor.handleEmptySqlContent() // 置空sqlEditor
        })
      }
    },
    handleTestSql(rule, value, callback) {
      if (!value) {
        callback()
      } else {
        if (this.testSqlSuccessFlag) {
          callback()
        } else {
          const msg = '请进行数据验证!'
          callback(msg)
        }
      }
    },
    testSqlContent() {
      const sqlContent = this.form.selectSql // 获取sql输入值
      if (!sqlContent) {
        // 设置sql非空为前置验证条件
        this.$message.warning('请输入SQL!')
        return
      }
      this.$refs.sqlTestModal.sql = sqlContent // 传入sql验证组件sql输入值

      this.$refs.sqlTestModal.initData(this.form.tableSourceCode) // 初始化sql验证组件数据
    },
    /**
     * @description 获取sqlEditor中的输入内容
     * @returns void
     */
    getSqlContent(value) {
      const content = value !== '' ? value : null
      this.form.selectSql = content // 设置form的configSql字段
      if (!content && !this.initSQLEditor) {
        // sql内容为空且当前sqlEditor初始化标识为false(非通过接口创建方式切换进行的初始化)时
        this.$nextTick(() => {
          // this.form.validateField(['selectSql'], { force: true }) // 强制验证form的selectSql字段
        })
      }
      // 默认sql输入值变化时，重置相关标识参数
      this.initSQLEditor = false // 重置sqlEditor初始化标识为false
      if (this.initSqlDataInEditMode) {
        this.initSqlDataInEditMode = !this.initSqlDataInEditMode // 当前为编辑初始化sql情况时，重置initSqlDataInEditMode标识参数
      } else {
        this.testSqlSuccessFlag = false // 非编辑初始化sql情况时，重置sql验证成功标识为false
      }
      this.changePreviewData()
    },
    /**
     * @description 验证成功后的回调方法
     * @returns void
     */
    testSqlSuccess(columnsInfo) {
      this.testSqlSuccessFlag = true
      this.$nextTick(() => {
        this.$refs.form.validate(['selectSql'], { force: true }) // 强制验证form的configSql字段
        this.tableColumns = columnsInfo.columns.map((item) => {
          return {
            sourceColumn: item,
            columnName: item,
            indexName: '',
            unit: '',
            tableName: '',
            id: require('nanoid').nanoid(12)
          }
        })
        this.queryCompTableList = columnsInfo.query.map((item) => {
          return {
            columns: [
              {
                title: '字段名',
                key: 'columnName'
              },
              {
                title: '查询控件',
                key: 'queryType'
              },
              {
                title: '是否显示',
                key: 'isShow',
                customRender: function (t, r, index) {
                  return '123'
                }
              },
              {
                title: '默认值',
                key: 'dbDefaultVal'
              }
            ],
            data: [
              {
                columnName: item.paramName,
                queryType: 'input',
                isShow: 0,
                dbDefaultVal: item.queryValue
              }
            ]
          }
        })
      })
    },
    /**
     * @description 格式化sqlEditor中的输入内容
     * @returns void
     */
    formatSqlContent() {
      this.$refs.SQLEditor.handleFormatSql() // 调用sqlEditor组件内部方式实现格式化
    },
    transferIndexType(record) {
      // 实现穿梭效果
      const value = record.getValue()
      if (value.indexType === 'index') {
        // 转为维度
        this.currentSelectTableColumnsDimenssion.unshift(
          Object.assign(
            {},
            this.currentSelectTableColumnsIndex.filter((column) => {
              return column.sourceColumn === value.sourceColumn
            })[0],
            {
              sourceType: this.form.isSql ? 'sql' : 'table',
              indexType: 'dimenssion',
              indexName: value.indexName,
              unit: value.unit
            }
          )
        )
        const position = this.currentSelectTableColumnsIndex.findIndex((item) => {
          return item.sourceColumn === value.sourceColumn
        })
        this.currentSelectTableColumnsIndex.splice(position, 1)
      } else {
        // 转为指标
        this.currentSelectTableColumnsIndex.push(
          Object.assign(
            {},
            this.currentSelectTableColumnsDimenssion.filter((column) => {
              return column.sourceColumn === value.sourceColumn
            })[0],
            {
              sourceType: this.form.isSql ? 'sql' : 'table',
              indexType: 'index',
              indexName: value.indexName,
              unit: value.unit
            }
          )
        )
        const position = this.currentSelectTableColumnsDimenssion.findIndex((item) => {
          return item.sourceColumn === value.sourceColumn
        })
        this.currentSelectTableColumnsDimenssion.splice(position, 1)
      }
    },
    async getDataSourceList() {
      const res = await getAction(this.url.queryDataSourceList)
      if (res.success) {
        this.dataSourceList = res.result
      }
    },
    async getTableSourceList() {
      const res = await getAction(this.url.getTableSource, { pageSize: 100 })
      if (res.success) {
        this.tableSourceList = res.result.records
      } else {
        this.$message.warning('数据源信息查询失败')
      }
    },
    showQueryConfigModal(record) {
      this.$refs.ScCockpitIndexSetQueryCompConfigModal.edit(
        record?.getValue(),
        this.tableColumns,
        this.form.isSql
      )
    },

    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.validateTableFields().then((table) => {
            if (!table.table1.values.length) {
              this.$message.warning('需至少设置一个指标')
              return
            }
            that.confirmLoading = true
            let httpurl = ''
            let method = ''

            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'post'
            }
            let formData = Object.assign(this.model, this.form)
            this.handleFormData(
              formData,
              table.table1.values,
              table.table2.values,
              this.queryCompTableList.map((_, index) => {
                return table[`table${index + 3}`].values[0]
              })
            )
            console.log('表单提交数据', formData)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          })
        }
      })
    },
    resetForm() {
      this.form = {
        indexGroupName: undefined,
        sourceId: undefined,
        indexGroupType: 'table',
        sortOrder: 0,
        isSql: 0,
        sourceColumn: [],
        euqryColumn: [],
        tableSouceCode: '',
        indexGroupCode: '',
        relUpdateTable: undefined
      }
      this.currentSelectTableColumns = []
      this.currentSelectTableColumnsDimenssion = []
      this.currentSelectTableColumnsIndex = []
    },
    handleFormData(formData, table1Value, table2Value, queryTableValue) {
      const table = [...table1Value, ...table2Value]
      formData.groupColumnList = table.map((column) => {
        delete column.action
        !this.model.id && delete column.id
        return Object.assign({}, column)
      })
      const queryTable = [...queryTableValue]
      formData.queryColumnList = queryTable.map((column, index) => {
        delete column.action
        !this.model.id && delete column.id
        return Object.assign(this.queryCompTableList[index].data[0], {
          ...column,
          indexId: this.model.id
        })
      })
      if (this.pid) formData.groupId = this.pid
      formData.tableName = this.currentSelectTable?.tableName
      delete formData.sourceColumn
    },
    getAllTable() {
      const promiseList = [
        this.getRefPromise('table1'),
        this.getRefPromise('table2'),
        ...this.queryCompTableList.map((_, index) => {
          return this.getRefPromise(`table${index + 3}`)
        })
      ]
      return Promise.all(promiseList)
    },
    getRefPromise(name) {
      const _this = this
      return new Promise((resolve) => {
        (function next() {
          let ref = _this.$refs[name][0] || _this.$refs[name]
          if (ref) {
            resolve(ref)
          } else {
            setTimeout(() => {
              next()
            }, 10)
          }
        })()
      })
    },
    validateTableFields() {
      const _this = this
      return new Promise((resolve, reject) => {
        this.getAllTable()
          .then((tables) => {
            const cases = [
              _this.$refs.table1,
              _this.$refs.table2,
              ...this.queryCompTableList.map((_, index) => {
                return _this.$refs[`table${index + 3}`][0]
              })
            ]
            return validateTables(cases, true)
          })
          .then((all) => {
            const options = {}
            all.forEach((item, index) => {
              options[`table${index + 1}`] = item
            })
            resolve(options)
          })
          .catch((e = {}) => {
            // 判断表单验证是否未通过
            if (e.error === VALIDATE_NO_PASSED) {
              // 未通过就跳转到相应的tab选项卡
              _this.activeKey = (e.index + 1).toString()
            }
            reject(e)
          })
      })
    },
    handleQueryCompInfo(queryCompObj) {
      const data = queryCompObj.data
      const sourceColumnRow =
        this.tableColumns && this.tableColumns.length
          ? this.tableColumns.filter((column) => {
              return column.columnName === data.sourceColumn
            })[0]
          : {
              id: require('nanoid').nanoid(12),
              sourceColumn: data.sourceColumn,
              queryName: data.queryName
            }
      const { id, ...rest } = sourceColumnRow
      const tableName = this.dataSourceList.filter((item) => {
        return item.id === this.form.sourceId
      })[0]?.tableName
      const newRow = {
        data: [
          Object.assign(rest, {
            ...data,
            tableName,
            sourceType: this.form.isSql ? 'sql' : 'table'
          })
        ],
        columns: this.columnsInput
        // data.queryType === 'input'
        //   ? this.columnsInput
        //   : data.queryType === 'select'
        //   ? this.columnsSelect
        //   : data.queryType === 'datepicker'
        //   ? this.columnsDatepicker
        //   : []
      }
      if (queryCompObj.type === 'add') {
        this.queryCompTableList.push(newRow)
      } else {
        const index = this.queryCompTableList.findIndex((item) => {
          return item.data[0].id === data.id
        })
        this.queryCompTableList.splice(index, 1, newRow)
      }
    },
    deleteQueryComp(index) {
      this.queryCompTableList.splice(index, 1)
    }
  }
}
</script>
<style lang="less" scoped>
.ant-spin-container {
  .ant-table-content {
    .ant-select {
      min-width: 100px;
    }
  }
}
::v-deep .j-table {
  .td {
    text-align: center;
  }
  .tr {
    justify-content: space-around;
  }
}
.title {
  margin: 20px 0;
  font-size: 16px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 4px;
    height: 16px;
    width: 4px;
    border-radius: 4px;
    background-color: #1890ff;
  }
}
.transfer {
  cursor: pointer;
  font-size: 20px;

  &:hover {
    color: #1890ff;
  }
}
.sql-editor-container {
  width: 100%;
  height: 228px !important;
  overflow: auto;
  position: relative;
  line-height: 20px;
}
.sql-operate-button-container {
  display: flex;
  justify-content: end;
  margin-top: 10px;
}
.baseinfo-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
