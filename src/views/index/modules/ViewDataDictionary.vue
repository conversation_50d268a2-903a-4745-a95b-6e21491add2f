<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form-model layout="inline" :model="queryParam" @keyup.enter.native="searchQuery">
        <a-row :gutter="10">
          <a-col :md="8" :sm="12">
            <a-form-model-item label="名称" prop="dictName">
              <j-input v-model="queryParam.dictName" style="width: 120px" placeholder="请输入名称"></j-input>
            </a-form-model-item>
          </a-col>
          <a-col :md="8" :sm="12">
            <a-form-model-item label="编号" prop="dictCode">
              <j-input v-model="queryParam.dictCode" style="width: 120px" placeholder="请输入编号"></j-input>
            </a-form-model-item>
          </a-col>
          <a-col :md="7" :sm="24">
            <span style="float: left" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery">搜索</a-button>
              <a-button type="primary" style="margin-left: 8px" @click="searchReset">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <div>
      <h5 style="font-weight: bold">（双击选中数据）</h5>
      <a-table
        ref="table"
        rowKey="id"
        size="middle"
        bordered
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowClassName="getRowClassname"
        :customRow="handleCheck"
        @change="handleTableChange"
      >
        <span slot="action" slot-scope="text, record">
          <a @click="editDictItem(record)"><a-icon type="setting" /> 字典配置</a>
        </span>
      </a-table>
    </div>
    <!-- 字典配置 -->
    <dict-deploy-list ref="dictdeployList"></dict-deploy-list>
  </a-card>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, deleteAction } from '@/api/manage'
import DictDeployList from './DictDeployList'
export default {
  components: { DictDeployList },
  mixins: [JeecgListMixin],
  data() {
    return {
      description: '数据字典tab',
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '字典名称',
          align: 'center',
          width: 150,
          dataIndex: 'dictName'
        },
        {
          title: '字典编号',
          align: 'center',
          width: 150,
          dataIndex: 'dictCode'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {
        dictCode: '',
        dictName: ''
      },
      url: {
        list: '/sys/dict/list',
        delete: '/sys/dict/delete'
        // deleteBatch: '/sys/dictItem/deleteBatch'
      }
    }
  },
  created() {},
  methods: {
    // update--begin--autor:wangshuai-----date:20191204------for：系统管理 数据字典禁用和正常区别开，添加背景颜色 teambition JT-22------
    // 增加样式方法返回值
    getRowClassname(record) {
      if (record.status == 0) {
        return 'data-rule-invalid'
      }
    },
    // 编辑字典数据
    editDictItem(record) {
      this.$refs.dictdeployList.edit(record)
    },
    // update--end--autor:wangshuai-----date:20191204------for：系统管理 数据字典禁用和正常区别开，添加背景颜色 teambition JT-22------
    // 双击对字典code进行赋值
    handleCheck(record, index) {
      return {
        on: {
          dblclick: () => {
            this.$emit('dbClickDict', record)
          }
        }
      }
    }
  }
}
</script>
