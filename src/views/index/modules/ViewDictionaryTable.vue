<template>
  <a-card :bordered="false">
    <!-- 抽屉 -->
    <a-drawer title="字典列表" :width="screenWidth" :visible="visible" @close="onClose">
      <!-- 抽屉内容的border -->
      <div
        :style="{
          padding: '10px',
          border: '1px solid #e9e9e9',
          background: '#fff',
        }"
      >
        <View-Data-Dictionary ref="ViewDataDictionaryList" @dbClickDict="handleDbClick"> </View-Data-Dictionary>
      </div>
    </a-drawer>
  </a-card>
</template>

<script>
import ViewDataDictionary from './ViewDataDictionary'

export default {
  name: 'DictItemList',
  components: { ViewDataDictionary },
  data() {
    return {
      title: '操作',
      visible: false,
      screenWidth: 800,
      model: {},
      dictId: '',
      status: 1,
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 12 },
      },
      form: this.$form.createForm(this),
    }
  },
  created() {
    // 当页面初始化时,根据屏幕大小来给抽屉设置宽度
    this.resetScreenSize()
  },
  methods: {
    showDrawer(index, indexType) {
      this.visible = true
      ;(this.index = index), (this.indexType = indexType)
    },
    onClose() {
      this.visible = false
      this.form.resetFields()
      this.dataSource = []
    },
    // 抽屉的宽度随着屏幕大小来改变
    resetScreenSize() {
      const screenWidth = document.body.clientWidth
      if (screenWidth < 600) {
        this.screenWidth = screenWidth
      } else {
        this.screenWidth = 650
      }
    },
    /**
     * @description 双击事件
     */
    handleDbClick(value) {
      this.$emit('reciveData', { value: value, index: this.index, indexType: this.indexType })
      this.visible = false
    },
  },
}
</script>
<style lang="less" scoped>
/deep/ .data-rule-invalid {
  background: #f4f4f4;
  color: #bababa;
}
/deep/.ant-drawer-body {
  padding: 12px;
}
</style>
