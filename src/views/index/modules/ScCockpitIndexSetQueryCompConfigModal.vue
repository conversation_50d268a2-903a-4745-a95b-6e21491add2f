<template>
  <a-modal
    :title="title"
    :width="900"
    :visible="visible"
    destroyOnClose
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div>
      <a-form-model ref="form" slot="detail" :model="form" :rules="rules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              label="使用字段"
              prop="sourceColumn"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-if="!isSql"
                v-model="form.sourceColumn"
                optionFilterProp="label"
                placeholder="请选择字段"
              >
                <a-select-option
                  v-for="item in tableColumns"
                  :key="item.columnName"
                  :label="item.columnName"
                  :value="item.columnName"
                  >{{ item.columnName }}</a-select-option
                >
              </a-select>
              <a-input v-else v-model="form.sourceColumn" placeholder="请输入字段名"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="查询名称"
              prop="queryName"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input v-model="form.queryName" placeholder="请输入查询名称"></a-input>
            </a-form-model-item>
          </a-col>
          <!--<a-col :span="24">
            <a-form-model-item
              label="查询控件类型"
              prop="queryType"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <j-dict-select-tag
                v-model="form.queryType"
                dictCode="index_query_type"
                placeholder="请选择查询控件类型"
              ></j-dict-select-tag>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="是否显示"
              prop="isShow"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-radio-group v-model="form.isShow" name="isShow">
                <a-radio :value="1"> 是 </a-radio>
                <a-radio :value="0"> 否 </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <div v-if="form.queryType === 'input'">
            <a-col :span="24">
              <a-form-model-item
                label="运算符"
                prop="queryOperator"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  v-model="form.queryOperator"
                  optionFilterProp="label"
                  placeholder="请选择运算符"
                >
                  <a-select-option
                    v-for="item in optionRuleArray"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                    >{{ item.name }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="默认值"
                prop="dbDefaultVal"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                ><a-input v-model="form.dbDefaultVal" placeholder="请输入默认值"></a-input
              ></a-form-model-item>
            </a-col>
          </div>
          <div v-else-if="form.queryType === 'select'">
            <a-col :span="24">
              <a-form-model-item
                label="字典编码"
                prop="dictCode"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.dictCode" placeholder="请输入字典编码"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="字典表名"
                prop="dictTable"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.dictTable" placeholder="请输入字典表名"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="默认值"
                prop="dbDefaultVal"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                ><a-input v-model="form.dbDefaultVal" placeholder="请输入默认值"></a-input
              ></a-form-model-item>
            </a-col>
          </div>
          <div v-else-if="form.queryType === 'datepicker'">
            <a-col :span="24">
              <a-form-model-item
                label="开始时间"
                prop="startTime"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.startTime" placeholder="请输入开始时间"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="结束时间"
                prop="endTime"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.endTime" placeholder="请输入结束时间"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="日期格式"
                prop="dateFormatStr"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <j-dict-select-tag
                  v-model="form.dateFormatStr"
                  dict-code="date_format"
                  placeholder="请输入日期格式"
                ></j-dict-select-tag>
              </a-form-model-item>
            </a-col>
          </div> -->
        </a-row>
      </a-form-model>
    </div>
  </a-modal>
</template>
<script>
import { pick } from 'lodash-es'

export default {
  name: 'ScCockpitIndexSetQueryCompConfigModal',
  data() {
    return {
      title: '查询控件配置',
      visible: false,
      tableColumns: [],
      isSql: 0,
      model: {},
      form: {
        sourceColumn: undefined,
        // queryType: undefined,
        queryName: ''
        // queryOperator: undefined,
        // dbDefaultVal: '',
        // isShow: 0,
        // dictCode: undefined,
        // dictTable: 'sys_dict',
        // startTime: 0,
        // endTime: 0,
        // dateFormatStr: undefined
      },
      rules: {
        sourceColumn: [{ required: true, message: '请选择使用字段', trigger: 'change' }],
        // queryType: [{ required: true, message: '请选择查询控件类型', trigger: 'change' }],
        queryName: [{ required: true, message: '请输入查询名称', trigger: 'change' }]
        // queryOperator: [{ required: true, message: '请选择运算符', trigger: 'change' }],
        // dbDefaultVal: [{ required: false, message: '请输入默认值', trigger: 'change' }],
        // isShow: [{ required: true, message: '请选择是否显示', trigger: 'change' }],
        // dictCode: [{ required: true, message: '请输入字典编码', trigger: 'change' }],
        // dictTable: [{ required: true, message: '请输入字典表名', trigger: 'change' }],
        // startTime: [{ required: true, message: '请输入开始时间', trigger: 'change' }],
        // endTime: [{ required: true, message: '请输入结束时间', trigger: 'change' }],
        // dateFormatStr: [{ required: true, message: '请输入日期格式', trigger: 'change' }]
      },
      optionRuleArray: [
        { id: 'eq', name: '等于' },
        { id: 'like', name: '包含' },
        { id: 'right_like', name: '以..开始' },
        { id: 'left_like', name: '以..结尾' },
        { id: 'in', name: '在...中' },
        { id: 'ne', name: '不等于' },
        { id: 'gt', name: '大于' },
        { id: 'ge', name: '大于等于' },
        { id: 'lt', name: '小于' },
        { id: 'le', name: '小于等于' }
      ],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
  },
  methods: {
    edit(record, tableColumns, isSql) {
      this.isSql = isSql
      this.resetForm()
      if (record) {
        this.model = Object.assign({}, record)
        this.form = pick(record, ...Object.keys(this.form))
        this.form.isShow = +this.form.isShow
      }
      this.visible = true
      this.tableColumns = tableColumns
    },
    resetForm() {
      this.form = {
        sourceColumn: undefined,
        // queryType: undefined,
        queryName: ''
        // queryOperator: undefined,
        // dbDefaultVal: '',
        // isShow: 0,
        // dictCode: undefined,
        // dictTable: 'sys_dict',
        // startTime: 0,
        // endTime: 0,
        // dateFormatStr: 'YYYY-MM-DD HH:mm:ss'
      }
    },
    handleOk() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let emitData = {}
          if (this.form.queryType === 'input') {
            emitData = pick(
              this.form,
              'sourceColumn',
              // 'queryType',
              'queryName'
              // 'isShow',
              // 'queryOperator',
              // 'dbDefaultVal'
            )
          } else if (this.form.queryType === 'select') {
            emitData = pick(
              this.form,
              'sourceColumn',
              // 'queryType',
              'queryName'
              // 'isShow',
              // 'dictCode',
              // 'dictTable',
              // 'dbDefaultVal'
            )
          } else if (this.form.queryType === 'datepicker') {
            emitData = pick(
              this.form,
              'sourceColumn',
              // 'queryType',
              'queryName'
              // 'isShow',
              // 'startTime',
              // 'endTime',
              // 'dateFormatStr'
            )
          }
          emitData = pick(this.form, 'sourceColumn', 'queryName')
          this.$message.success('操作成功')
          this.$emit('queryCompInfo', {
            type: this.model.id ? 'edit' : 'add',
            data: { id: this.model.id, ...emitData }
          })
          this.visible = false
        }
      })
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
