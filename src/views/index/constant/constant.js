import commonAttribute from '@/views/designer/components/commonAttribute'
export const tabItem = [
  {
    name: '图表',
    icon: 'pie-chart'
  },
  {
    name: '查询组件',
    icon: 'filter'
  },
  {
    name: '富文本',
    icon: 'font-size'
  },
  {
    name: '媒体',
    icon: 'picture'
  },
  {
    name: 'tab',
    icon: 'layout'
  },
  {
    name: '更多',
    icon: 'appstore'
  },
  {
    name: '复用',
    icon: 'copy'
  }
]

export const chartItem = [
  {
    catogary: '线图',
    content: [
      {
        name: '基础折线图',
        pic: require('../../../assets/img/basicLineChart.png'),
        option: {
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '',
              data: [],
              type: 'line'
            }
          ]
        }
      },
      {
        name: '面积图',
        pic: require('../../../assets/img/areaChart.png'),
        option: {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '',
              data: [],
              type: 'line',
              areaStyle: {}
            }
          ]
        }
      },
      {
        name: '堆叠折线图',
        pic: require('../../../assets/img/stackedLineChart.png'),
        option: {
          title: {},
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: []
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '',
              type: 'line',
              stack: 'Total',
              data: []
            },
            {
              name: '',
              type: 'line',
              stack: 'Total',
              data: []
            }
          ]
        }
      },
      {
        name: '堆叠面积图',
        pic: require('../../../assets/img/stackedLineChart.png'),
        option: {
          title: {},
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
          },
          legend: {
            data: []
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: []
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              name: '',
              type: 'line',
              stack: 'Total',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: []
            },
            {
              name: '',
              type: 'line',
              stack: 'Total',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: []
            }
          ]
        }
      }
    ]
  },
  {
    catogary: '柱状图',
    content: [
      {
        name: 'basicHistogramChart',
        pic: require('../../../assets/img/basicHistogramChart.png'),
        option: {
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      },
      {
        name: '堆叠柱状图',
        pic: require('../../../assets/img/stackedHistogramChart.png'),
        option: {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {},
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: []
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              name: '',
              type: 'bar',
              stack: 'stack1',
              emphasis: {
                focus: 'series'
              },
              data: []
            },
            {
              name: '',
              type: 'bar',
              stack: 'stack1',
              emphasis: {
                focus: 'series'
              },
              data: []
            }
          ]
        }
      },
      {
        name: '分组柱状图',
        pic: require('../../../assets/img/groupedHistogramChart.png'),
        option: {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {},
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: []
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              name: '',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: []
            },
            {
              name: '',
              type: 'bar',
              data: [],
              emphasis: {
                focus: 'series'
              }
            }
          ]
        }
      },
      {
        name: '基础条形图',
        pic: require('../../../assets/img/basicBarChart.png'),
        option: {
          title: {},
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {},
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01]
          },
          yAxis: {
            type: 'category',
            data: []
          },
          series: [
            {
              name: '',
              type: 'bar',
              data: []
            }
          ]
        }
      },
      {
        name: '堆叠条形图',
        pic: require('../../../assets/img/stackedBarChart.png')
      },
      {
        name: '分组条形图',
        pic: require('../../../assets/img/groupedBarChart.png'),
        option: {
          title: {},
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {},
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01]
          },
          yAxis: {
            type: 'category',
            data: []
          },
          series: [
            {
              name: '',
              type: 'bar',
              data: []
            },
            {
              name: '',
              type: 'bar',
              data: []
            }
          ]
        }
      }
    ]
  },
  {
    catogary: '分布图',
    content: [
      {
        name: '饼图',
        pic: require('../../../assets/img/pieChart.png')
      },
      {
        name: '环形图',
        pic: require('../../../assets/img/ringChart.png')
      },
      {
        name: '玫瑰图',
        pic: require('../../../assets/img/roseChart.png')
      },
      {
        name: '雷达图',
        pic: require('../../../assets/img/radarChart.png')
      }
    ]
  },
  {
    catogary: '双轴图',
    content: [
      {
        name: '柱线组合图',
        pic: require('../../../assets/img/BarLineChart.png'),
        option: {
          tooltip: {
            trigger: 'axis'
          },
          legend: {},
          xAxis: {
            type: 'category',
            data: [],
            axisPointer: {
              type: 'shadow'
            }
          },
          yAxis: [
            {
              type: 'value',
              name: ''
            },
            {
              type: 'value',
              name: '',
              alignTicks: true
            }
          ],
          series: [
            {
              name: '',
              type: 'bar',
              data: []
            },
            {
              name: '',
              type: 'line',
              yAxisIndex: 1,
              data: []
            }
          ]
        }
      }
    ]
  }
]
export const searchComp = [
  {
    name: '查询组件',
    pic: ''
  }
]
export const richText = [
  {
    name: '富文本',
    pic: ''
  }
]
export const media = [
  {
    name: '图片',
    pic: ''
  },
  {
    name: '视频',
    pic: ''
  },
  {
    name: '流媒体',
    pic: ''
  },
  {
    name: '图片组',
    pic: ''
  }
]
export const tab = [
  {
    name: 'tab',
    pic: ''
  }
]
export const more = [
  {
    name: '网页',
    pic: ''
  }
]

export const chartOptions = [
  {
    name: '基础折线图'
  }
]
