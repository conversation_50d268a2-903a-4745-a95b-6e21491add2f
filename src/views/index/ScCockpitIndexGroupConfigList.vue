<template xmlns:background-color="http://www.w3.org/1999/xhtml">
  <a-row :gutter="20">
    <a-col :md="8" :sm="24">
      <a-card :bordered="false">
        <!-- 按钮操作区域 -->
        <a-row style="margin-left: 14px">
          <a-button type="primary" @click="handleAddGroup(1)">添加分组</a-button>
          <a-button type="primary" @click="handleAddGroup(2)">添加下级分组</a-button>
          <a-button type="primary" @click="handleAddIndexSet(1)">添加指标集</a-button>
        </a-row>
        <div style="background: #fff; padding-left: 16px; height: 100%; margin-top: 5px">
          <a-alert type="info" :showIcon="true">
            <div slot="message">
              当前选择：<span v-if="currSelected && currSelected.groupName">{{
                getCurrSelectedTitle()
              }}</span>
              <a
                v-if="currSelected && currSelected.groupName"
                style="margin-left: 10px"
                @click="onClearSelected"
              >取消选择</a
              >
            </div>
          </a-alert>
          <a-input-search
            style="width: 100%; margin-top: 10px"
            placeholder="请输入分组或指标集名称"
            @search="onSearch"
          />
          <!-- 树-->
          <a-col :md="10" :sm="24" class="department-tree">
            <template>
              <a-dropdown :trigger="[dropTrigger]" @visibleChange="dropStatus">
                <span style="user-select: none">
                  <a-spin
                    class="tree"
                    :style="{ overflow: loading ? 'hidden' : 'auto' }"
                    :spinning="loading"
                  ></a-spin>
                  <a-tree
                    show-icon
                    multiple
                    draggable
                    :replaceFields="replaceFields"
                    :selectedKeys="selectedKeys"
                    :treeData="treeData"
                    :checkStrictly="checkStrictly"
                    :expandedKeys="iExpandedKeys"
                    :autoExpandParent="autoExpandParent"
                    :icon="getIcon"
                    @drop="onDrop"
                    @select="onSelect"
                    @rightClick="rightHandle"
                    @expand="onExpand"
                  >
                    <template slot="title" slot-scope="{ groupName }">
                      <span v-if="groupName.indexOf(searchValue) > -1">
                        {{ groupName.substr(0, groupName.indexOf(searchValue)) }}
                        <span style="color: #f50">{{ searchValue }}</span>
                        {{ groupName.substr(groupName.indexOf(searchValue) + searchValue.length) }}
                      </span>
                      <span v-else>{{ groupName }}</span>
                    </template>
                  </a-tree>
                </span>
                <!--新增右键点击事件,和增加添加和删除功能-->
                <a-menu slot="overlay">
                  <a-menu-item key="0" @click="(e) => handleEdit(e, rightClickSelectedRecord)"
                  >编辑</a-menu-item
                  >
                  <a-menu-item key="1" @click="handleAddGroup(3)">添加下级分组</a-menu-item>
                  <a-menu-item key="2" @click="handleAddIndexSet(2)">添加指标集</a-menu-item>
                  <a-menu-item key="3" @click="handleDelete(rightClickSelectedRecord)"
                  >删除</a-menu-item
                  >
                  <a-menu-item key="4" @click="closeDrop">取消</a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
          </a-col>
        </div>
      </a-card>
      <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      <div class="drawer-bootom-button">
        <a-dropdown :trigger="['click']" placement="topCenter">
          <a-menu slot="overlay">
            <a-menu-item key="1" @click="switchCheckStrictly(1)">父子关联</a-menu-item>
            <a-menu-item key="2" @click="switchCheckStrictly(2)">取消关联</a-menu-item>
            <a-menu-item key="5" @click="expandAll">展开所有</a-menu-item>
            <a-menu-item key="6" @click="closeAll">合并所有</a-menu-item>
          </a-menu>
          <a-button>
            树操作
            <a-icon type="up" />
          </a-button>
        </a-dropdown>
      </div>
    </a-col>
    <a-col v-if="currSelected && currSelected.type" :md="16" :sm="24">
      <a-tabs v-model="activeKey" @change="handleActiveKeyChange">
        <a-tab-pane key="1" tab="基本信息" forceRender>
          <a-card
          ><ScCockpitIndexSetConfigForm
            ref="baseInfo"
            :baseInfoSubmitBtn="true"
            @ok="loadTree"
            @changePreviewData="changePreviewData"
            @changeQueryInfo="changeQueryInfo"
          ></ScCockpitIndexSetConfigForm
          ></a-card>
        </a-tab-pane>
        <a-tab-pane key="2" tab="数据预览" forceRender>
          <a-card>
            <ScCockpitIndexPreview ref="ScCockpitIndexPreview"></ScCockpitIndexPreview>
          </a-card>
        </a-tab-pane>
        <!-- <a-tab-pane key="3" tab="组件绑定" forceRender>
          <a-card>
            <ScCockpitModuleBinding ref="ScCockpitModuleBinding"></ScCockpitModuleBinding>
          </a-card>
        </a-tab-pane> -->
      </a-tabs>
    </a-col>
    <a-col v-show="!currSelected || currSelected.groupType" :md="16" :sm="24">
      <a-card>
        <a-empty>
          <span slot="description"> 请先选择一个指标集! </span>
        </a-empty>
      </a-card>
    </a-col>
    <ScCockpitGroupManageModal
      ref="groupModalForm"
      groupType="index"
      @ok="modalFormOk"
    ></ScCockpitGroupManageModal>
    <ScCockpitIndexSetConfigModal
      ref="indexModalForm"
      @ok="modalFormOk"
    ></ScCockpitIndexSetConfigModal>
  </a-row>
</template>
<script>
import { getAction, postAction, deleteAction } from '@/api/manage'
import { traverseTree } from '@/api/api'
// import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ScCockpitGroupManageModal from './modules/ScCockpitGroupManageModal'
import ScCockpitIndexSetConfigModal from './modules/ScCockpitIndexSetConfigModal'
import ScCockpitIndexSetConfigForm from './modules/ScCockpitIndexSetConfigForm'
import ScCockpitIndexPreview from './modules/ScCockpitIndexPreview'
import ScCockpitModuleBinding from './modules/ScCockpitModuleBinding.vue'
export default {
  name: 'ScCockpitIndexGroupConfigList',
  components: {
    ScCockpitGroupManageModal,
    ScCockpitIndexSetConfigModal,
    ScCockpitIndexSetConfigForm,
    ScCockpitIndexPreview,
    ScCockpitModuleBinding
  },
  // mixins: [JeecgListMixin],
  data() {
    return {
      currSelected: undefined,
      dropTrigger: '',
      loading: false,
      hiding: true,
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id'
      },
      selectReplaceFields: {
        children: 'children',
        title: 'groupName',
        key: 'id',
        value: 'id'
      },
      activeKey: '1',
      selectedKeys: [],
      treeData: [],
      treeList: [],
      searchValue: '',
      checkStrictly: true,
      iExpandedKeys: [],
      autoExpandParent: false,
      rightClickSelectedRecord: undefined,
      allTreeKeys: [],
      draggedNode: '',
      enterNode: '',
      url: {
        list: '/group/scCockpitGroup/queryByType?groupType=index',
        deleteGroup: '/group/scCockpitGroup/delete',
        deleteIndexSet: '/index/scCockpitIndexGroupConfig/delete',
        deleteBatch: '/group/scCockpitGroup/deleteBatch',
        queryIndexSetById: '/index/scCockpitIndexGroupConfig/queryById',
        dragToEdit: '/group/scCockpitGroup/updateGroupSort'
      }
    }
  },
  created() {
    this.loadTree()
  },
  methods: {
    modalFormOk() {
      // 新增/修改 成功时，重载列表
      this.loadTree()
      // 清空列表选中
      this.onClearSelected()
    },
    loadTree(val) {
      this.loading = true
      this.treeData = []
      var queryParam = {}
      if (val) {
        queryParam.topicName = val
      }
      getAction(this.url.list, queryParam)
        .then((res) => {
          if (res.success) {
            // this.allTreeKeys = res.result.ids
            let treeDataInit = res.result
            treeDataInit.forEach((node) => {
              traverseTree(node, (node) => {
                if (node.data) {
                  // 将指标集data进行字段转换，并push到childList中，保留其type字段
                  node.data.forEach((data) => {
                    let obj = data
                    obj.groupName = data.name
                    delete obj.name
                    if (!node.childList) node.childList = [obj]
                    else node.childList.push(obj)
                  })
                  delete node.data
                }
              })
            })
            this.treeData = treeDataInit
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    setThisExpandedKeys(node) {},
    getAllKeys(node) {},
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      const record = node.node.dataRef
      this.rightClickSelectedRecord = record
      console.log('rightClickSelectedRecord', this.rightClickSelectedRecord)
      // this.topicType = record.topicType
    },
    onExpand(expandedKeys) {
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    }, // 右键点击下拉关闭下拉框
    closeDrop() {},
    onSearch(value) {
      this.treeList = []
      this.treeList = this.generateList(this.treeData)
      const expandedKeys = this.treeList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            return this.getParentKey(item.key, this.treeData)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        iExpandedKeys: expandedKeys,
        searchValue: value,
        autoExpandParent: true
      })
    },
    getParentKey(key, tree) {
      let parentKey
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.childList) {
          if (node.childList.some((item) => item.id === key)) {
            parentKey = node.id
          } else if (this.getParentKey(key, node.childList)) {
            parentKey = this.getParentKey(key, node.childList)
          }
        }
      }
      return parentKey
    },
    generateList(data) {
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        const key = node.id
        this.treeList.push({ key, title: node.groupName })
        if (node.childList) {
          this.generateList(node.childList)
        }
      }
      return this.treeList
    },
    hide() {
      this.visible = false
    },
    handleActiveKeyChange(flag) {
      flag === '2' &&
        this.$nextTick(() => {
          this.$refs.ScCockpitIndexPreview &&
            this.$refs.ScCockpitIndexPreview.init(this.currSelected)
        })
      flag === '3' &&
        this.$nextTick(() => {
          getAction('/module/scCockpitModuleConfig/queryByIndexId', {
            indexId: this.currSelected.id
          }).then((res) => {
            !!this.$refs.ScCockpitModuleBinding &&
              this.$refs.ScCockpitModuleBinding.init(
                this.currSelected,
                res.success ? res.result : null
              )
          })
        })
    },
    changePreviewData(value) {
      this.$nextTick(() => {
        this.$refs.ScCockpitIndexPreview &&
          this.$refs.ScCockpitIndexPreview.changePreviewData({
            ...value
          })
        this.$refs.ScCockpitModuleBinding &&
          this.$refs.ScCockpitModuleBinding.changeComponentData({
            ...value
          })
      })
    },
    changeQueryInfo(value) {
      this.$nextTick(() => {
        this.$refs.ScCockpitIndexPreview && this.$refs.ScCockpitIndexPreview.changeQueryInfo(value)
      })
    },
    onSelect(selectedKeys, e) {
      const record = e.node.dataRef
      this.currSelected = Object.assign({}, record)
      this.selectedKeys = [record.id]
      this.activeKey = '1'
      this.$nextTick(() => {
        this.$refs.baseInfo && this.$refs.baseInfo.edit(record)
      })
    },
    // 触发onSelect事件时,为部门树右侧的form表单赋值
    setValuesToForm(record) {},
    getCurrSelectedTitle() {
      return this.currSelected.groupName
    },
    onClearSelected() {
      this.hiding = true
      this.currSelected = {}
      // this.form.resetFields()
      this.selectedKeys = []
    },
    async submitCurrForm() {},
    emptyCurrForm() {},
    handleEdit(e, record) {
      if (record.groupType) this.handleEditGroup(record)
      else this.handleEditIndex(record)
    },
    handleEditGroup(record) {
      this.$refs.groupModalForm.disableSubmit = false
      this.$refs.groupModalForm.title = '编辑分组'
      this.$refs.groupModalForm.edit(record)
    },
    handleEditIndex(record) {
      this.$refs.indexModalForm.disableSubmit = false
      this.$refs.indexModalForm.title = '编辑指标集'
      this.$refs.indexModalForm.edit(record)
    },
    handleAddGroup(num) {
      if (
        (num === 2 && !this.currSelected.groupType) ||
        (num === 3 && !this.rightClickSelectedRecord.groupType)
      ) {
        this.$message.warning('只能在分组下添加分组')
        return
      }
      if (num === 2 && !this.selectedKeys.length) {
        this.$message.warning('请选择一个上级分组')
        return
      }
      this.$refs.groupModalForm.disableSubmit = false
      this.$refs.groupModalForm.title = num === 1 ? '新增分组' : '添加下级'
      this.$refs.groupModalForm.add(
        num,
        this.selectedKeys.length ? this.selectedKeys[0] : null,
        this.rightClickSelectedRecord ? this.rightClickSelectedRecord.id : null
      )
    },
    handleAddIndexSet(flag) {
      if (
        (flag === 1 && !this.currSelected?.groupType) ||
        (flag === 2 && !this.rightClickSelectedRecord?.groupType)
      ) {
        this.$message.warning('只能在分组下添加指标集')
        return
      }
      this.$refs.indexModalForm.disableSubmit = false
      this.$refs.indexModalForm.title = '新增指标集'
      this.$refs.indexModalForm.add(
        flag === 1 ? this.currSelected.id : this.rightClickSelectedRecord.id
      )
    },
    handleDelete(node, showMsg = true) {
      if (node.childList && showMsg) {
        // 有下级分组不允许直接删除
        this.$message.warning(`该分组有${node.childList.length}个下级分组`)
        return
      }
      var that = this
      let url = ''
      if (node.groupType) url = this.url.deleteGroup // 删除分组
      else url = this.url.deleteIndexSet // 删除指标集
      if (showMsg) {
        this.$confirm({
          title: '确认删除',
          content: '确定要删除此节点吗?',
          onOk: async function () {
            const res = await getAction(url, { id: node.id })
            if (res.success) {
              that.$message.success(res.message)
              that.loadTree()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          }
        })
      } else {
        getAction(url, { id: node.id })
      }
    },
    onDrop(info) {
      // 执行树的拖拽逻辑
      let pid, sortOrder
      const dropPos = info.node.pos.split('-')
      // 拖入的节点
      const enterNodeKey = info.node.eventKey
      const isdropToGap = info.dropToGap
      // 被拖的节点
      const draggedNodeKey = info.dragNode.eventKey
      const draggedNodeData = info.dragNode.dataRef
      console.log('enterNodeKey', enterNodeKey)
      if (!isdropToGap) {
        // 移入到某个分组下
        if (!info.node.dataRef.groupType) {
          this.$message.warning('不能移入到指标集中')
          return
        }
        let node
        for (let i = 1; i < dropPos.length; i++) {
          if (i === 1) node = this.treeData[+dropPos[i]]
          else node = node.childList[+dropPos[i]]
        }
        pid = node.id
        sortOrder = node.childList ? node.childList.length : 0
      } else {
        // 移入到某个间隙中
        let node = { id: '-1' }
        for (let i = 1; i < dropPos.length - 1; i++) {
          if (i === 1) node = this.treeData[+dropPos[i]]
          else node = node.childList[+dropPos[i]]
        }
        pid = node.id
        // 找到新位置前面的相邻节点
        const neighborNodePos = +dropPos[dropPos.length - 1] // 相邻节点在当前目录下的位置
        console.log('neighborNodePos', neighborNodePos)
        const relativePosition = info.dropPosition - neighborNodePos
        console.log('relativePosition', relativePosition)
        console.log('node', node)
        if (relativePosition === -1) {
          // 被拖节点在相邻节点之前，需要再往前找一个节点
          if (neighborNodePos) {
            // 前面还有节点
            const neighborNode =
              pid === '-1'
                ? this.treeData[neighborNodePos - 1]
                : node.childList[neighborNodePos - 1]
            sortOrder = this.getSortOrder(neighborNode, draggedNodeData, pid, node)
          } else {
            // 前无节点（拖到了当前目录的第一个位置），sortOrder直接取0
            sortOrder = 0
          }
        } else if (relativePosition === 1) {
          // 被拖节点在相邻节点之后，sortorder即为相邻节点sortOrder+1或groupSort+1
          const neighborNode =
            pid === '-1' ? this.treeData[neighborNodePos] : node.childList[neighborNodePos]
          sortOrder = this.getSortOrder(neighborNode, draggedNodeData, pid, node)
        }
      }
      let params
      if (draggedNodeData.groupType) {
        params = {
          pid,
          groupType: 'index',
          groupId: draggedNodeKey,
          sortOrder
        }
      } else {
        if (pid === '-1') {
          this.$message.warning('数据集只能存在于分组内')
          return
        }
        params = {
          groupId: pid,
          groupType: 'index',
          relId: draggedNodeKey,
          sortOrder
        }
      }
      postAction(this.url.dragToEdit, params).then((res) => {
        if (res.success) {
          this.$message.success('操作成功')
          this.loadTree()
        } else {
          this.$message.warning('操作失败')
        }
      })
    },
    /**
     * 得到拖拽后的前一节点后，计算新的排序字段值
     *
     * @param neighborNode 前一节点
     * @param draggedNodeData 被拖节点
     * @param pid 父节点id，树节点pid=-1
     * @param node 父节点
     */
    getSortOrder(neighborNode, draggedNodeData, pid, node) {
      let sortOrder
      if (neighborNode.groupType) {
        // 前方节点是分组，被拖节点是分组则sort + 1，否则成为第一个指标集
        sortOrder = draggedNodeData.groupType ? neighborNode.groupSort + 1 : 0
      } else {
        // 前方节点是指标集，被拖节点是分组则找到当前目录下最后一个分组取sort+1，否则直接取sort+1
        if (draggedNodeData.groupType) {
          let lastGroup
          const currentLevelNodes = pid === '-1' ? this.treeData : node.childList
          for (let i = 0; i < currentLevelNodes.length && !!currentLevelNodes[i].groupType; i++) {
            lastGroup = currentLevelNodes[i]
          }
          sortOrder = lastGroup ? lastGroup.groupSort + 1 : 0
        } else {
          sortOrder = neighborNode.sortOrder + 1
        }
      }
      return sortOrder
    },
    expandAll() {
      this.iExpandedKeys = this.allTreeKeys
    },
    closeAll() {
      this.iExpandedKeys = []
    },
    getIcon(props) {
      if (!props.type) {
        return <i class="el-icon-folder-opened"></i>
      } else if (props.type === 'table') {
        return (
          <svg
            t="1749031923027"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="5331"
            width="14"
            height="14"
          >
            <path
              d="M896 64a128 128 0 0 1 128 128v640a128 128 0 0 1-128 128H128a128 128 0 0 1-128-128V192a128 128 0 0 1 128-128h768z m-416 608H64V832a64 64 0 0 0 56.512 63.552L128 896h352v-224z m480 0H544V896H896a64 64 0 0 0 63.552-56.512L960 832v-160zM480 384H64v224h416V384zM960 384H544v224H960V384z m-64-256H128a64 64 0 0 0-63.552 56.512L64 192v128h896V192a64 64 0 0 0-64-64zM192 192a32 32 0 0 1 0 64H128a32 32 0 0 1 0-64h64z m144 0a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64h64zM480 192a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64h64z"
              fill="#999b99"
              p-id="5332"
            ></path>
          </svg>
        )
      } else if (props.type === 'sql') {
        return (
          <svg
            t="1749031819802"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="4249"
            width="20"
            height="20"
          >
            <path
              d="M271.1 327.5 208.6 382.7c-22-30.6-44.3-45.8-67.1-45.8-11.1 0-20.1 3-27.2 8.9-7 5.9-10.6 12.6-10.6 20.1 0 7.4 2.5 14.5 7.6 21.1 6.8 8.9 27.5 27.9 61.9 57 32.2 26.9 51.8 43.9 58.6 51 17.1 17.3 29.3 33.8 36.4 49.6 7.1 15.8 10.7 33 10.7 51.7 0 36.4-12.6 66.5-37.7 90.2-25.2 23.7-58 35.6-98.4 35.6-31.6 0-59.1-7.7-82.6-23.2-23.4-15.5-43.5-39.8-60.2-73l71-42.8c21.3 39.2 45.9 58.8 73.7 58.8 14.5 0 26.7-4.2 36.6-12.7 9.9-8.4 14.8-18.2 14.8-29.3 0-10.1-3.7-20.1-11.2-30.2-7.5-10.1-23.9-25.4-49.2-46.1-48.3-39.4-79.6-69.8-93.7-91.2-14.1-21.4-21.1-42.8-21.1-64.1 0-30.8 11.7-57.2 35.2-79.2 23.4-22 52.4-33 86.8-33 22.1 0 43.2 5.1 63.3 15.4C226.1 281.6 247.8 300.3 271.1 327.5z"
              p-id="4250"
              fill="#999b99"
            ></path>
            <path
              d="M709.2 646l77.2 99.8-100 0-39.2-50.5c-32.4 17.8-68.6 26.6-108.4 26.6-66.6 0-122-23-166.1-68.9-44.1-45.9-66.1-100.7-66.1-164.2 0-42.4 10.3-81.4 30.8-116.9 20.5-35.5 48.7-63.7 84.7-84.6 35.9-20.9 74.5-31.4 115.7-31.4 63 0 117 22.7 162.2 68.2 45.2 45.4 67.8 100.8 67.8 166.2C767.8 550.5 748.2 602.3 709.2 646zM656.5 577.8c17.9-26.5 26.8-55.9 26.8-88.1 0-42-14.2-77.7-42.6-107.1-28.4-29.3-62.7-44-103-44-41.5 0-76.2 14.3-104.2 42.8-28 28.6-42 64.8-42 108.9 0 49.1 17.6 87.9 52.9 116.4 27.6 22.3 58.9 33.5 94 33.5 20.1 0 39.1-3.9 56.8-11.8l-79.4-102.2 100.7 0L656.5 577.8z"
              p-id="4251"
              fill="#999b99"
            ></path>
            <path
              d="M816.5 267.1l84.4 0 0 363.1L1024 630.2l0 80.5L816.5 710.7 816.5 267.1z"
              p-id="4252"
              fill="#999b99"
            ></path>
          </svg>
        )
      }
    },
    cancelCheckALL() {},
    switchCheckStrictly(v) {}
  }
}
</script>
<style scoped>
.ant-card-body .table-operator {
  margin: 15px;
}

.anty-form-btn {
  width: 100%;
  text-align: center;
}

.anty-form-btn button {
  margin: 0 5px;
}

.anty-node-layout .ant-layout-header {
  padding-right: 0;
}

.header {
  padding: 0 8px;
}

.header button {
  margin: 0 3px;
}

.ant-modal-cust-warp {
  height: 100%;
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden;
}

#app .desktop {
  height: auto !important;
}

/** Button按钮间距 */
.ant-btn {
  margin-left: 10px;
}

.drawer-bootom-button {
  /*position: absolute;*/
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: left;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}

.department-tree {
  margin-top: 5px;
  width: 100%;
  height: 495px;
  overflow: auto;
}
</style>
