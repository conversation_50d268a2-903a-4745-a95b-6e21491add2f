<template>
  <div class="table-wrapper" :style="pageHeight ? 'max-height: unset' : ''">
    <header class="table__header">
      <div class="table__row">
        <span
          v-for="(key, index) in chartData.column"
          :key="index"
          :title="chartData.unitList[key]"
          style="flex: 1; overflow: hidden; white-space: nowrap; text-overflow: ellipsis"
          @click="filterData(key)"
        >
          {{ chartData.columnName[key]
          }}{{ chartData.unitList[key] ? '（' + chartData.unitList[key] + '）' : '' }}
        </span>
      </div>
    </header>
    <main :class="{ table__body: true, scroll: scroll }">
      <div v-for="(item, index) in chartData.data" :key="index" class="table__row">
        <div
          v-for="(key, i) in chartData.column"
          :key="i"
          style="flex: 1; overflow: hidden; white-space: nowrap; text-overflow: ellipsis"
          :title="item[key]"
        >
          {{ item[key] }}
        </div>
      </div>
    </main>
  </div>
</template>

<script>
export default {
  name: 'TableComp',
  data() {
    return {
      pageHeight: true,
      chartData: {
        columnName: {},
        column: [],
        data: [],
        unitList: {}
      },
      scroll: true
    }
  },
  watch: {
    chartData(newVal) {
      newVal && this.setData() // newVal存在的话执行dataChild函数
    }
  },
  created() {
    this.setData()
  },
  methods: {
    filterData(e) {
      // this.$emit('filterData', e)
    },
    setData(data, values, title) {
      if (!values) return
      this.chartData.columnName = {}
      const columns = values.dimensionColumnInfo.concat(values.indexColumnInfo)
      this.chartData.column = columns.map((value) => value.sourceColumn)
      this.chartData.data = data
      for (const column of columns) {
        this.chartData.columnName[column.sourceColumn] = column.indexName
        this.chartData.unitList[column.sourceColumn] = column.unit
      }
    }
  }
}
</script>

<style lang="less">
.table-wrapper {
  position: relative;
  width: 100%;
  max-height: 1260px;
  overflow: auto;

  .table__header {
    position: sticky;
    top: 0;
    left: 0;
    width: 100%;

    .table__row {
      font-weight: 600;
      background-color: hsla(208, 100%, 50%, 1);
      color: hsla(0, 0%, 100%, 1);
    }
  }
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .table__body {
    margin-top: 14px;

    .img1 {
      width: 78px;
      height: 78px;
    }

    .img2 {
      width: 40px;
      height: 52px;
    }

    .img3 {
      width: 34px;
      height: 46px;
    }
  }

  .circleNum {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    div {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: #0089ff;
      color: #fff;
      line-height: 36px;
      text-align: center;
    }
  }

  .table__row {
    width: 100%;
    min-height: 60px;
    background-color: hsla(213, 100%, 98%, 1);
    color: hsla(208, 100%, 37%, 1);
    border-radius: 5000px;
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 36px;
    padding: 0 16px;
    display: flex;
    > * {
      // min-width: 100px;
      text-align: center;
      line-height: 1.3;
      overflow: hidden;
      // text-overflow: ellipsis;
      // /* white-space: nowrap; */
      // display: -webkit-box;
      // -webkit-box-orient: vertical;
      // -webkit-line-clamp: 2;

      &:not(:last-of-type) {
        margin-right: 4px;
      }
    }

    > .order-class {
      width: 100px !important;
      // flex: 0;
    }

    &:not(:last-of-type) {
      margin-bottom: 14px;
    }
  }
  .scroll {
    overflow-y: auto;
  }
}
</style>
