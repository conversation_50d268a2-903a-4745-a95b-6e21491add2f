<!-- 暂用普通文本栅格的样式，待修改 -->
<template>
  <div style="display: flex; justify-content: start; align-items: center; flex-direction: column">
    <div style="font-size: 18px; font-weight: bold; color: #525252; line-height: 50px">
      {{ title }}
    </div>
    <div class="text-container-group">
      <div
        v-for="(item, index) in data"
        :key="index"
        class="text-container"
        :style="{
          background: bgImage ? 'url(' + bgImage + ') no-repeat center center / 100% 100%' : 'none'
        }"
      >
        <div>{{ item.dimensionData }}</div>
        <div>
          {{ item.indexData }}<span>{{ unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getFileAccessHttpUrl } from '@/api/manage'
export default {
  name: 'CustomTextGroup',
  data() {
    return {
      data: [],
      bgImage: '',
      unit: '',
      title: ''
    }
  },
  methods: {
    setData(data, values, title) {
      this.bgImage = this.getFileAccessHttpUrl(values.bgImage)
      this.title = title

      this.unit = values.indexColumnInfo[0].unit
      const indexKeys = {
        name: values.indexColumnInfo[0].indexName,
        key: values.indexColumnInfo[0].sourceColumn
      }
      const dimensionKeys = {
        name: values.dimensionColumnInfo[0].indexName,
        key: values.dimensionColumnInfo[0].sourceColumn
      }
      const indexData = data.map((item) => {
        if (typeof item[indexKeys.key] === 'string') return item[indexKeys.key].trim()
        return item[indexKeys.key]
      })

      const dimensionData = data.map((item) => {
        return item[dimensionKeys.key]
      })
      this.data = dimensionData.map((item, index) => {
        return {
          dimensionKey: dimensionKeys,
          indexKey: indexKeys,
          dimensionData: item,
          indexData: indexData[index]
        }
      })
      this.$nextTick(() => {
        const style = JSON.parse(values.cssData)
        const dom = document.querySelector('.text-container-group')
        dom.style.width = style.width + 'px'
      })
    },
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    }
  }
}
</script>
<style lang="less" scoped>
.text-container-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  column-gap: 20px;
  .text-container {
    text-align: center;
  }
}
</style>
