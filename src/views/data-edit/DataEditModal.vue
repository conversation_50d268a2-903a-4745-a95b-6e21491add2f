<template>
  <a-modal
    :title="title"
    :width="600"
    :visible="showFlag"
    :confirmLoading="confirmLoading"
    dialogClass="new-top"
    destroyOnClose
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="data-edit-box">
      <div v-for="(item, index) in operateArray" :key="index" class="data-edit-item">
        <span class="item-left">{{ item.dataIndex }}:</span>
        <div class="item-right">
          <a-input
            v-if="item.columnType === 'string'"
            v-model="item.value"
            allowClear
            placeholder="请输入字段值"
            style="width: 270px"
            :disabled="item.disabled"
          />
          <a-input-number
            v-else-if="item.columnType === 'number'"
            v-model="item.value"
            placeholder="请输入数值"
            style="width: 270px"
            :disabled="item.disabled"
          />
          <a-date-picker
            v-else-if="item.columnType === 'date'"
            v-model="item.value"
            allowClear
            show-time
            placeholder="请选择日期"
            :format="item.dateFormat"
            style="width: 270px"
            :disabled="item.disabled"
          />
          <a-input
            v-else
            v-model="item.value"
            allowClear
            placeholder="请输入字段值"
            style="width: 270px"
            :disabled="item.disabled"
          />
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { postParamAction } from '@/api/manage'
import moment from 'moment'

export default {
  name: 'DataEditModal',
  props: {
    showFlag: {
      type: Boolean,
      default: false
    },
    actionFlag: {
      type: String,
      default: 'Add'
    },
    baseParam: {
      type: Object,
      default: {}
    },
    addColumns: {
      type: Array,
      default: []
    },
    editRecord: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      title: '新增数据',
      operateArray: [],
      confirmLoading: false,
      url: {
        edit: '/dataSource/tableMaintain/edit',
        add: '/dataSource/tableMaintain/add'
      }
    }
  },
  created() {
    this.actionFlag === 'Add' ? this.title = '新增数据' : this.title = '编辑数据'
    // 修改数据
    if (this.addColumns && this.addColumns.length > 0) {
      this.addColumns.map((item, index) => {
        const obj = {
          columnType: item.columnType,
          dateFormat: item.dateFormat,
          dataIndex: item.dataIndex,
          title: item.title,
          value: this.actionFlag === 'Update' ? this.editRecord[item.dataIndex] : null,
          disabled: this.actionFlag === 'Update' && item.isPk === 1 ? true : false
        }
        this.operateArray.push(obj)
      })
    }
  },
  mounted() {
  },
  methods: {

    /**
     * @description 添加操作
     * @returns void
     */
    handleOk() {
      // 进行数据的封装
      const obj = {}
      this.operateArray.map((item, index) => {
        // 区分日期类型和非日期类型
        if (item.columnType === 'date') {
          // 需要对日期类型进行格式化
          if (item.value === null || item.value === '' || item.value === undefined) {
            obj[item.dataIndex] = null
          } else {
            const format = item.dateFormat === null ? 'YYYY-MM-DD HH:mm:ss' : item.dateFormat
            obj[item.dataIndex] = moment(item.value).format(format)
          }
        } else {
          obj[item.dataIndex] = item.value === '' || item.value === undefined ? null : item.value
        }
      })
      this.confirmLoading = true
      const url = this.actionFlag === 'Add' ? this.url.add : this.url.edit
      // 编辑时需要追加id
      if (this.actionFlag !== 'Add') {
        obj.id = this.editRecord.id
      }
      postParamAction(url, this.baseParam, obj).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.$emit('ok')
          this.handleCancel()
        } else {
          this.$message.warning(res.message)
        }
        this.confirmLoading = false
      }).catch(() => {
        this.confirmLoading = false
      })
    },

    /**
     * @description 取消操作
     * @returns void
     */
    handleCancel() {
      this.$emit('update:showFlag', false)
    }

  }
}
</script>

<style lang="less">
.new-top {
  top: 160px;
}
.data-edit-box {
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  height: 430px;
  padding-top: 10px;
  .data-edit-item {
    width: 100%;
    height: 42px;
    line-height: 42px;
    margin-bottom: 10px;
    .item-left {
      width: 180px;
      text-align: right;
      padding-right: 10px;
      float: left;
      color: #000000;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
    }
    .item-right {
      width: calc(100% - 180px);
      float: right;
    }
  }
}
</style>
