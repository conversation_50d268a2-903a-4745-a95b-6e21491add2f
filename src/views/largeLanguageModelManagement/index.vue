<template>
  <div>
    <!-- <div class="main-header"></div> -->
    <a-row :gutter="24" class="main-header">
      <div style="display: flex">
        <div class="title"></div>
        <div class="title-desc">
          <span> 模型总数{{ llmCount }}个 </span>
        </div>
      </div>

      <div class="import-button" @click="handleImport">
        <span>模型录入</span>
      </div>
    </a-row>

    <!-- 卡片区域：用 CSS Grid 自动换列 -->
    <div class="card-container">
      <!-- <div style="display: flex; justify-content: center;">

      </div> -->
      <!-- LLM cards -->
      <div class="llm-card" v-for="item in llmList" :key="item.id">
        <div class="card-info-container">
          <div class="thumb">
            <img src="./images/ds.png" alt="" />
          </div>

          <div class="info">
            <div class="title-row">
              <div class="info-title">
                {{ item.llmName }}
              </div>

              <div class="info-status" :class="item.isEnable === 1 ? 'on' : 'off'">
                {{ item.isEnable === 1 ? '已启用' : '未启用' }}
              </div>
            </div>
          </div>

          <div class="more" @click="toggleSet(item.id)">
            <div class="setlist" v-if="activeSetId === item.id" @mouseleave="toggleSet(null)">
              <div class="setitem" @click="editClick(item)">
                <a-icon type="edit" class="item-icon" />
                <div class="item-label">编辑</div>
              </div>

              <div v-if="item.isEnable === 1" class="setitem" @click="stopClick(item)">
                <a-icon type="stop" class="item-icon" />
                <div class="item-label">停止启用</div>
              </div>
              <div v-if="item.isEnable === 0" class="setitem" @click="stopClick(item)">
                <a-icon type="check" class="item-icon" />
                <div class="item-label">启用</div>
              </div>

              <div class="setitem" style="color: #ff3c2f" @click="deleteClick(item.id)">
                <a-icon type="delete" class="item-icon" />
                <div class="item-label">删除</div>
              </div>
            </div>

            <a-icon type="ellipsis" class="icon" />
          </div>
        </div>

        <div class="meta">
          <span>创建人：{{ item.createBy ? item.createBy : '未知' }}</span>
          <span>创建时间：{{ item.createTime }}</span>
        </div>
      </div>

      <!-- “新增大模型”卡片 -->
      <a-tooltip placement="rightTop" title="点击新增大模型">
        <div class="import-card" style="cursor: pointer" @click="handleImport">
          <a-icon type="plus-circle" class="import" />
        </div>
      </a-tooltip>
    </div>

    <llmModal ref="llmModal" @formSubmit="onSubmit" />
  </div>
</template>

<script>
import { getAction, deleteAction, postAction } from '@/api/manage'
import llmModal from './components/llmModal.vue'

export default {
  name: 'LargeLanguageModelManagement',
  components: {
    llmModal
  },
  data() {
    return {
      llmCount: 0,
      llmList: [],
      activeSetId: null
    }
  },
  methods: {
    handleImport() {
      this.$refs.llmModal.show()
    },
    toggleSet(id) {
      this.activeSetId = this.activeSetId === id ? null : id
    },

    async getData() {
      const res = await getAction('/llmConfig/list', {})

      if (res.success) {
        this.llmList = res.result.records
        this.llmCount = res.result.total
      }
    },

    editClick(item) {
      this.$refs.llmModal.show(item)
    },

    async stopClick(item) {
      let res
      try {
        if (item.isEnable === 1) {
          res = await postAction('/llmConfig/toggleStatus', { id: item.id, status: 0 })

          if (res.success) {
            this.$message.success('已禁用')
          }
        } else {
          res = await postAction('/llmConfig/toggleStatus', { id: item.id, status: 1 })
          if (res.success) {
            this.$message.success('已启用')
          }
        }

        if (res.success) {
          console.log(res)
          this.getData()
        }
      } catch (e) {
        console.log(e)
      }
    },

    async deleteClick(id) {
      const res = await deleteAction('/llmConfig/delete', { id: id })

      if (res.success) {
        this.$message.success('删除成功')
        this.getData()
      } else {
        this.$message.warning('删除失败')
      }
    },

    onSubmit() {
      this.getData()
    }
  },

  mounted() {
    this.getData()
  }
}
</script>

<style lang="less" scoped>
.main-header {
  width: 100%;
  height: 230px;

  padding: 67px 0 0 90px;
  background: url('./images/bg.png') no-repeat center / 100% 100%;

  .import-button {
    width: 118px;
    height: 44px;

    margin: 28px 0 0 0;
    padding: 7px 20px 9px 18px;

    background: url('./images/bg2.png') no-repeat center / 100% 100%;

    cursor: pointer;

    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 20px;
      color: #ffffff;
    }
  }
}

.title {
  width: 196px;
  height: 53px;

  background: url('./images/title.png') no-repeat center / 100% 100%;
}

.title-desc {
  width: 296px;
  height: 46px;

  margin: 0 0 0 19px;

  display: flex;
  align-items: center;

  padding: 0 0 0 11px;

  background: url('./images/rect.png') no-repeat center / 100% 100%;

  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 26px;
    color: #333333;
  }
}

.card-container {
  display: grid;
  /* 固定列宽 570px，自动换列 */
  grid-template-columns: repeat(auto-fill, 570px);
  grid-gap: 24px;

  /* 关键：让整张网格在父容器中水平居中 */
  justify-content: center;

  /* 上下留白 */
  padding: 0 12px 24px;
  background: #f8f8fa;
}

.llm-card,
.import-card {
  flex-shrink: 0;
  justify-self: stretch; /* 让它左右撑满 570px */
  align-self: start;

  margin: auto;
  // min-width: 500px; /* 最小宽度，不再任意被压扁 */
}

.import-card {
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 5 / 2; /* 宽 : 高，比如 296 / 240 */
  height: auto; /* 不再写死 */

  padding: 28px 22px 25px !important;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04);
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1);

  margin: 12px 30px 0;

  .import {
    color: #d0ddef;
    font-size: 90px;
  }

  &:hover,
  &:focus-within {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 10px 24px rgba(0, 0, 0, 0.08);
  }
}

.llm-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  aspect-ratio: 5 / 2; /* 宽 : 高，比如 296 / 240 */
  height: auto; /* 不再写死 */

  justify-content: space-between;

  padding: 28px 22px 25px !important;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04);
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  backface-visibility: hidden; /* 减少晃动 */

  margin: 12px 30px 12px;

  .card-info-container {
    display: flex;
    justify-content: space-around;
    width: 100%;

    .thumb {
      flex: 0 0 90px; /* 固定宽高 */
      height: 90px;
      border-radius: 8px;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .info {
      display: flex;
      justify-content: center;
      flex: 1 1 0;
      overflow: hidden;

      width: 100%;

      padding: 8px 0 14px 8px;

      .title-row {
        display: flex;
        flex-direction: column;
        align-items: start;
        width: 100%;
        .info-title {
          font-size: 26px;
          padding-bottom: 6px;

          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: block;
          width: 100%;
        }

        .info-status {
          padding: 2px 12px;

          border-radius: 14px;
          font-size: 14px;

          &.off {
            background: #eff4fe;
            color: #999999;
          }
          &.on {
            color: #ffffff;
            background: #47d4b2;
          }
        }
      }
    }

    .more {
      display: flex;
      align-items: center;

      flex: 0 0 40px;

      font-size: 32px;

      cursor: pointer;

      .icon {
        padding: 4px;
        border-radius: 24px;
        background: rgba(51, 114, 232, 0.08);
      }

      .icon:hover {
        background: rgba(25, 98, 232, 0.16);
      }
    }
  }

  .meta {
    color: #999;
    font-size: 14px;

    display: flex;
    flex-wrap: wrap; /* 宽度不足时自动换行 */
    row-gap: 4px;
    column-gap: 8px;
    width: 100%;

    span {
      min-width: 120px; /* 保证单条信息的最小展示宽度 */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &:hover,
  &:focus-within {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 10px 24px rgba(0, 0, 0, 0.08);
  }
}

.setlist {
  position: absolute;
  width: 138px;
  padding: 6px;
  background: #fff;

  right: 45px;
  top: 95px;
  z-index: 999;

  border-radius: 8px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.06);

  .setitem {
    position: relative;
    cursor: pointer;

    width: 100%;
    border-radius: 8px;
    padding: 8px 14px 8px 12px;

    font-size: 14px;
    font-weight: 500;
    line-height: 25px;
    min-height: 28px;

    align-items: center;
    display: flex;

    transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    .item-icon {
      font-size: 20px;
    }

    .item-label {
      margin: 0 10px;
    }
    &:hover {
      background: rgba(#dfe9f9, 0.7);
      color: #3372e8; /* 文字配合背景高亮，可自行调整 */
    }
  }
}

// /* 手机端 ≤480px：创建信息垂直排列 */
// @media (max-width: 480px) {
//   .llm-card .meta {
//     flex-direction: column;
//     align-items: flex-start;
//   }
// }
</style>
