<template>
  <div>
    <a-modal :visible="isVisible" :width="800" :title="title" @ok="handleOk" @cancel="handleCancel">
      <a-form-model
        ref="modalRef"
        :rules="rules"
        :model="form"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-model-item label="大模型名称" prop="name">
          <a-input v-model="form.name" placeholder="请输入大模型名称" />
        </a-form-model-item>

        <a-form-model-item label="大模型平台" prop="plat">
          <a-select v-model="form.plat" placeholder="请输入大模型平台">
            <a-select-option v-for="(item, index) in platList" :key="index" :value="item.value">
              {{ item.text }}
            </a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item label="基础模型名称" prop="modelName">
          <a-input v-model="form.modelName" placeholder="请选择基础大模型名称" />
        </a-form-model-item>

        <a-form-model-item label="大模型地址" prop="address">
          <a-input v-model="form.address" placeholder="请输入大模型地址" />
        </a-form-model-item>

        <a-form-model-item label="大模型 key" prop="key">
          <a-input v-model="form.key" placeholder="请输入大模型 key" />
        </a-form-model-item>

        <a-form-model-item label="大模型秘钥" prop="private_key">
          <a-input v-model="form.private_key" placeholder="请输入大模型秘钥" />
        </a-form-model-item>

        <a-form-model-item label="对话 url" prop="url">
          <a-input v-model="form.url" placeholder="请输入对话 url" />
        </a-form-model-item>

        <a-form-model-item label="是否推理" prop="is_deduction">
          <a-radio-group v-model="form.is_deduction">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-model-item>

        <!-- 新增：启用开关 -->
        <a-form-model-item label="启用" prop="isEnable">
          <a-switch v-model="form.isEnable" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { getAction, postAction2 } from '@/api/manage'

export default {
  name: 'LlmModal',
  data() {
    return {
      isAdd: false,
      isVisible: false,
      title: '',
      form: {
        name: '',
        plat: '',
        modelName: null,
        address: '',
        key: '',
        private_key: '',
        url: '',
        is_deduction: 1,
        isEnable: true
      },
      rules: {
        name: [{ required: true, message: '请输入大模型名称', trigger: 'change' }],
        plat: [{ required: true, message: '请输入大模型平台', trigger: 'change' }],
        modelName: [{ required: true, message: '请选择基础模型名称', trigger: 'change' }],
        address: [{ required: false, message: '请输入大模型地址', trigger: 'blur' }],
        key: [{ required: true, message: '请输入大模型 key', trigger: 'blur' }],
        private_key: [{ required: false, message: '请输入大模型秘钥', trigger: 'blur' }],
        url: [{ required: false, message: '请输入对话 url', trigger: 'blur' }],
        is_deduction: [{ required: true, message: '请选择是否推理', trigger: 'change' }],
        isEnable: [
          { required: true, type: 'boolean', message: '请选择启用状态', trigger: 'change' }
        ]
      },
      llmList: [],
      platList: [],
      id: ''
    }
  },
  methods: {
    async getData() {
      const res = await getAction('/llmConfig/list', {})

      if (res.success) {
        this.llmList = res.result.records
      }

      const platRes = await getAction('/sys/dict/getDictItems/llm_platform_type', {})

      if (platRes.success) {
        this.platList = platRes.result
      }
    },
    show(item) {
      this.getData()
      this.form = {}

      if (item) {
        /* 编辑场景 */
        this.isAdd = false
        this.title = '模型编辑'

        /* 把后端字段映射到表单字段 */
        this.form = {
          name: item.llmName || '',
          plat: item.platform || '',
          modelName: item.model || null,
          address: item.apiHost || '',
          key: item.apiKey || '',
          private_key: item.apiSecret || '',
          url: item.chatUrl || '',
          is_deduction: item.isReason ? item.isReason : 1,
          isEnable: !!item.isEnable
        }
        this.id = item.id
      } else {
        /* 新增场景，重置为空 */
        this.title = '模型新增'
        this.isAdd = true
        this.form = {
          name: '',
          plat: '',
          modelName: null,
          address: '',
          key: '',
          private_key: '',
          url: '',
          is_deduction: 1,
          isEnable: true
        }
      }

      this.isVisible = true
    },

    handleOk() {
      this.$refs.modalRef.validate((valid) => {
        let query = {
          apiKey: this.form.key,
          apiHost: this.form.address,
          llmName: this.form.name,
          apiSecret: this.form.private_key || null,
          platform: this.form.plat,
          isEnable: this.form.isEnable ? 1 : 0,
          promptTokens: null,
          model: this.form.modelName,
          chatUrl: this.form.url || null,
          isReason: this.form.is_deduction
        }

        if (!valid) {
          console.log(this.form)
          this.$message.error('请输入必填项信息')
          return
        } else {
          if (this.isAdd) {
            postAction('/llmConfig/add', query)
              .then((res) => {
                if (res.success) {
                  this.$message.success('新增成功')
                } else {
                  this.$message.warning('新增失败')
                }
              })
              .catch((e) => {
                this.$message.warning('e')
              })
          } else {
            query.id = this.id
            console.log(query)
            postAction2('/llmConfig/edit', query)
              .then((res) => {
                if (res.success) {
                  this.$message.success('修改成功')
                } else {
                  this.$message.warning('修改失败')
                }
              })
              .catch((e) => {
                this.$message.warning('e')
              })
          }

          this.$emit('formSubmit')
          this.isVisible = false
        }
      })
    },

    handleCancel() {
      this.isVisible = false
      this.form = {}
    }
  }
}
</script>

<style lang="scss" scoped></style>
