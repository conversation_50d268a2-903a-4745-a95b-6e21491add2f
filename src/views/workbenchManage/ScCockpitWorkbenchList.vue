<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="工作台名称">
              <a-input v-model="queryParam.workbenchName" placeholder="请输入工作台名称" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" icon="search" @click="searchQuery">查询</a-button>
              <a-button type="primary" icon="reload" style="margin-left: 8px" @click="searchReset"
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-alert type="info" showIcon style="margin-bottom: 16px">
        <template slot="message">
          <span>已选择</span>
          <a style="font-weight: 600; padding: 0 4px">{{ selectedRowKeys.length }}</a>
          <span>项</span>
          <a style="margin-left: 24px" @click="onClearSelected">清空</a>
        </template>
      </a-alert>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="bgImage" slot-scope="text, record">
          <img :src="getFileAccessHttpUrl(text)" alt="" style="height: 100px; width: 100px" />
        </template>
        <span slot="map" slot-scope="text">
          {{ text ? `@/components/custom/${text}.vue` : '' }}
        </span>
        <span slot="menu" slot-scope="text">
          {{ text ? `@/components/custom/${text}.vue` : '' }}</span
        >
        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <ScCockpitWorkbenchModal ref="modalForm" @ok="loadData" />
  </a-card>
</template>
<script>
import { getFileAccessHttpUrl } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ScCockpitWorkbenchModal from './ScCockpitWorkbenchModal.vue'
export default {
  name: 'ScCockpitWorkbenchList',
  components: { ScCockpitWorkbenchModal },
  mixins: [JeecgListMixin],
  data() {
    return {
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (t, r, index) => index + 1
        },
        {
          title: '工作台名称',
          align: 'center',
          dataIndex: 'workbenchName'
        },
        {
          title: '工作台别名',
          align: 'center',
          dataIndex: 'workbenchAlias'
        },
        {
          title: '工作台编码',
          align: 'center',
          dataIndex: 'workbenchCode'
        },
        {
          title: '宽度',
          align: 'center',
          dataIndex: 'screenWidth'
        },
        {
          title: '高度',
          align: 'center',
          dataIndex: 'screenHeight'
        },
        {
          title: '默认背景',
          align: 'center',
          dataIndex: 'bgImage',
          scopedSlots: { customRender: 'bgImage' }
        },
        {
          title: '默认地图',
          align: 'center',
          dataIndex: 'mapComponent',
          scopedSlots: { customRender: 'map' }
        },
        {
          title: '默认菜单',
          align: 'center',
          dataIndex: 'menuComponent',
          scopedSlots: { customRender: 'menu' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      fuzzyParam: ['workbenchName'],
      url: {
        list: '/workbench/scCockpitWorkbench/allWorkbenchList',
        delete: '/workbench/scCockpitWorkbench/delete'
      }
    }
  },
  methods: {
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    }
  }
}
</script>
<style lang="less" scoped>
.table-operator {
  margin-bottom: 20px;
}
</style>
