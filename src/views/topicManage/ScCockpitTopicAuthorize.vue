<template xmlns:background-color="http://www.w3.org/1999/xhtml">
  <a-row :gutter="20">
    <a-col :md="6" :sm="24">
      <a-card :bordered="false">
        <!-- 按钮操作区域 -->
        <a-row style="margin-left: 14px">
          <a-button type="primary" @click="handleBatchAuthorize">批量授权</a-button>
        </a-row>
        <div style="background: #fff; padding-left: 16px; height: 100%; margin-top: 5px">
          <a-alert type="info" :showIcon="true">
            <div slot="message">
              当前选择：<span v-if="currSelected && currSelected.groupName">{{
                getCurrSelectedTitle()
              }}</span>
              <a
                v-if="currSelected && currSelected.groupName"
                style="margin-left: 10px"
                @click="onClearSelected"
              >取消选择</a
              >
            </div>
          </a-alert>
          <a-input-search style="width: 100%; margin-top: 10px" placeholder="请输入分组或专题名称" @search="onSearch" />
          <!-- 树-->
          <a-col :md="8" :sm="24" class="department-tree">
            <template>
              <a-dropdown :trigger="[dropTrigger]" @visibleChange="dropStatus">
                <span style="user-select: none">
                  <a-spin class="tree" :style="{ overflow: loading ? 'hidden' : 'auto' }" :spinning="loading"></a-spin>
                  <a-tree
                    v-model="checkedKeys"
                    show-icon
                    multiple
                    checkable
                    :replaceFields="replaceFields"
                    :selectedKeys="selectedKeys"
                    :treeData="treeData"
                    :checkStrictly="checkStrictly"
                    :expandedKeys="iExpandedKeys"
                    :autoExpandParent="autoExpandParent"
                    :icon="getIcon"
                    @select="onSelect"
                    @expand="onExpand"
                  >
                    <template slot="title" slot-scope="{ groupName }">
                      <span v-if="groupName.indexOf(searchValue) > -1">
                        {{ groupName.substr(0, groupName.indexOf(searchValue)) }}
                        <span style="color: #f50">{{ searchValue }}</span>
                        {{ groupName.substr(groupName.indexOf(searchValue) + searchValue.length) }}
                      </span>
                      <span v-else>{{ groupName }}</span>
                    </template>
                  </a-tree>
                </span>
              </a-dropdown>
            </template>
          </a-col>
        </div>
      </a-card>
      <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      <div class="drawer-bootom-button">
        <a-dropdown :trigger="['click']" placement="topCenter">
          <a-menu slot="overlay">
            <a-menu-item key="1" @click="switchCheckStrictly(1)">父子关联</a-menu-item>
            <a-menu-item key="2" @click="switchCheckStrictly(2)">取消关联</a-menu-item>
            <a-menu-item key="5" @click="expandAll">展开所有</a-menu-item>
            <a-menu-item key="6" @click="closeAll">合并所有</a-menu-item>
          </a-menu>
          <a-button>
            树操作
            <a-icon type="up" />
          </a-button>
        </a-dropdown>
      </div>
    </a-col>
    <a-col v-if="showRightForm" :md="18" :sm="24">
      <a-card><ScCockpitTopicAuthorizeConfig ref="ScCockpitTopicAuthorizeConfig" /></a-card>
    </a-col>
    <a-col v-else :md="18" :sm="24">
      <a-card>
        <a-empty>
          <span slot="description"> 请先选择一个专题! </span>
        </a-empty>
      </a-card>
    </a-col>
  </a-row>
</template>
<script>
import { getAction } from '@/api/manage'
import { traverseTree } from '@/api/api'
import ScCockpitTopicAuthorizeConfig from './modules/ScCockpitTopicAuthorizeConfig.vue'
export default {
  name: 'ScCockpitTopicAuthorize',
  components: { ScCockpitTopicAuthorizeConfig },
  data() {
    return {
      currSelected: undefined,
      loading: false,
      hiding: true,
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id'
      },
      selectReplaceFields: {
        children: 'children',
        title: 'groupName',
        key: 'id',
        value: 'id'
      },
      activeKey: '1',
      dropTrigger: '',
      selectedKeys: [],
      checkedKeys: [],
      treeData: [],
      treeList: [],
      searchValue: '',
      checkStrictly: true,
      iExpandedKeys: [],
      autoExpandParent: false,
      allTreeKeys: [],
      showRightForm: false,
      url: {
        list: '/group/scCockpitGroup/queryByType?groupType=topic'
      }
    }
  },
  created() {
    this.loadTree()
  },
  methods: {
    loadTree() {
      this.loading = true
      this.addTopic = false
      this.treeData = []
      getAction(this.url.list)
        .then((res) => {
          if (res.success) {
            // this.allTreeKeys = res.result.ids
            let treeDataInit = res.result
            treeDataInit.forEach((node) => {
              traverseTree(node, (node) => {
                if (node.groupType) node.checkable = false
                if (node.data) {
                  // 将指标集data进行字段转换，并push到childList中，保留其type字段
                  node.data.forEach((data) => {
                    let obj = data
                    obj.groupName = data.name
                    delete obj.name
                    if (!node.childList) node.childList = [obj]
                    else node.childList.push(obj)
                  })
                  delete node.data
                }
              })
            })
            this.treeData = treeDataInit
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getCurrSelectedTitle() {
      return this.currSelected.groupName
    },
    onClearSelected() {
      this.hiding = true
      this.currSelected = {}
      // this.form.resetFields()
      this.selectedKeys = []
      this.showRightForm = false
    },
    handleBatchAuthorize() {},
    onSearch(value) {
      this.treeList = []
      this.treeList = this.generateList(this.treeData)
      const expandedKeys = this.treeList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            return this.getParentKey(item.key, this.treeData)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        iExpandedKeys: expandedKeys,
        searchValue: value,
        autoExpandParent: true
      })
    },
    getParentKey(key, tree) {
      let parentKey
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.childList) {
          if (node.childList.some((item) => item.id === key)) {
            parentKey = node.id
          } else if (this.getParentKey(key, node.childList)) {
            parentKey = this.getParentKey(key, node.childList)
          }
        }
      }
      return parentKey
    },
    generateList(data) {
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        const key = node.id
        this.treeList.push({ key, title: node.groupName })
        if (node.childList) {
          this.generateList(node.childList)
        }
      }
      return this.treeList
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    }, // 右键点击下拉关闭下拉框
    onExpand(expandedKeys) {
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    getIcon(props) {
      if (!props.sortOrder) {
        return <a-icon type="folder-open" />
      } else {
        return <a-icon type="profile" />
      }
    },
    onSelect(selectedKeys, e) {
      const record = e.node.dataRef
      this.currSelected = Object.assign({}, record)
      this.selectedKeys = [record.id]
      this.showRightForm = !this.currSelected.groupType
      this.showRightForm &&
        this.$nextTick(() => {
          this.$refs.ScCockpitTopicAuthorizeConfig.topicId = record.id
        })
    },
    closeDrop() {},
    expandAll() {
      this.iExpandedKeys = this.allTreeKeys
    },
    closeAll() {
      this.iExpandedKeys = []
    }
  }
}
</script>
<style scoped>
.ant-card-body .table-operator {
  margin: 15px;
}

.anty-form-btn {
  width: 100%;
  text-align: center;
}

.anty-form-btn button {
  margin: 0 5px;
}

.anty-node-layout .ant-layout-header {
  padding-right: 0;
}

.header {
  padding: 0 8px;
}

.header button {
  margin: 0 3px;
}

.ant-modal-cust-warp {
  height: 100%;
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden;
}

#app .desktop {
  height: auto !important;
}

/** Button按钮间距 */
.ant-btn {
  margin-left: 10px;
}

.drawer-bootom-button {
  /*position: absolute;*/
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: left;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}

.department-tree {
  margin-top: 5px;
  width: 100%;
  height: 495px;
  overflow: auto;
}
</style>
