<template>
  <div class="cockpit-container">
    <div class="main-layout">
      <!-- 左侧树形控件区域 -->
      <a-row :gutter="20" style="width: 100%">
        <a-col :md="6" :sm="24">
          <a-card :bordered="false">
            <a-row style="display: flex; justify-content: start; margin-bottom: 10px">
              <a-button type="primary" @click="handleAddTopic(currentTreeNode)">新增专题</a-button>
              <a-button type="primary" @click="handleAddModule(currentTreeNode)">新增模块</a-button>
            </a-row>
            <a-alert type="info" :showIcon="true">
              <div slot="message">
                当前选择：<span v-if="currSelected && currSelected.groupName">{{
                  getCurrSelectedTitle()
                }}</span>
                <a
                  v-if="currSelected && currSelected.groupName"
                  style="margin-left: 10px"
                  @click="onClearSelected"
                  >取消选择</a
                >
              </div>
            </a-alert>
            <a-select
              v-model="currentWorkbenchId"
              style="width: 100%; margin-top: 10px"
              placeholder="请选择工作台"
              @change="(currentWorkbenchId) => loadTopicList(currentWorkbenchId, true)"
            >
              <a-select-option
                v-for="item in workbenchList"
                :key="item.id"
                :value="item.id"
                :label="item.workbenchName"
                >{{ item.workbenchName }}</a-select-option
              >
            </a-select>
            <a-input-search
              placeholder="请输入专题或模块名称进行查询"
              style="width: 100%; margin: 10px 0"
              @search="searchTopicModule"
            />

            <a-dropdown :trigger="[dropTrigger]" @visibleChange="dropStatus">
              <span style="user-select: none">
                <a-spin
                  class="tree"
                  :style="{ overflow: loading ? 'hidden' : 'auto' }"
                  :spinning="loading"
                ></a-spin>
                <a-tree
                  v-if="topicList.length > 0"
                  :tree-data="topicList"
                  :replaceFields="{ title: 'treeName', key: 'id', children: 'childTreeList' }"
                  :icon="getIcon"
                  show-icon
                  :selectedKeys="currentTopicModuleId ? [currentTopicModuleId] : []"
                  @select="onTreeSelect"
                  @rightClick="rightHandle"
                >
                  <template slot="title" slot-scope="{ treeName }">
                    <span v-if="treeName.indexOf(searchValue) > -1">
                      {{ treeName.substr(0, treeName.indexOf(searchValue)) }}
                      <span style="color: #f50">{{ searchValue }}</span>
                      {{ treeName.substr(treeName.indexOf(searchValue) + searchValue.length) }}
                    </span>
                    <span v-else>{{ treeName }}</span>
                  </template>
                </a-tree>
                <a-empty v-else description="暂无专题数据" />
              </span>
              <a-menu slot="overlay">
                <a-menu-item key="1" @click="handleAddTopic(rightClickSelectedRecord)"
                  >添加专题</a-menu-item
                >
                <a-menu-item key="2" @click="handleAddModule(rightClickSelectedRecord)"
                  >添加模块</a-menu-item
                >
                <a-menu-item key="3" @click="handleDelete(rightClickSelectedRecord)"
                  >删除</a-menu-item
                >
              </a-menu>
            </a-dropdown>
          </a-card>
        </a-col>
        <!-- 右侧主内容区域 -->

        <!-- 专题、模块基本信息区域 -->
        <a-col :md="18" :sm="24" style="padding: 0 10px">
          <a-card>
            <a-empty v-if="!showRightForm">
              <span slot="description"> 请先选择一个专题或模块! </span>
            </a-empty>
            <a-spin v-else :spinning="confirmLoading">
              <j-form-container :disabled="false">
                <a-form-model
                  v-if="currentTreeNode?.treeType !== 'module'"
                  ref="rightForm"
                  slot="detail"
                  :model="currentTopicModuleInfo"
                  :rules="rightFormRules"
                >
                  <!-- 专题表单 -->
                  <a-row>
                    <a-col :span="24">
                      <a-form-model-item
                        label="专题名称"
                        prop="topicName"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-input
                          v-model="currentTopicModuleInfo.topicName"
                          placeholder="请输入专题名称"
                        ></a-input>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="专题编码"
                        prop="topicCode"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-input
                          v-model="currentTopicModuleInfo.topicCode"
                          placeholder="请输入专题编码"
                        ></a-input>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="专题描述"
                        prop="topicDescription"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-input
                          v-model="currentTopicModuleInfo.topicDescription"
                          placeholder="请输入专题描述"
                        ></a-input>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="专题图标"
                        prop="topicIcon"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <j-image-upload v-model="currentTopicModuleInfo.topicIcon"></j-image-upload>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="专题排序"
                        prop="topicOrder"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-input-number v-model="currentTopicModuleInfo.topicOrder" />
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="链接类型"
                        prop="linkType"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-radio-group v-model="currentTopicModuleInfo.linkType">
                          <a-radio :value="0"> 无链接 </a-radio>
                          <a-radio :value="1"> 内部链接 </a-radio>
                          <a-radio :value="2"> 外部链接 </a-radio>
                        </a-radio-group>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="专题地址"
                        prop="topicUrl"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-input
                          v-model="currentTopicModuleInfo.topicUrl"
                          placeholder="请输入专题地址"
                        >
                        </a-input>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="专题类型"
                        prop="topicType"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-radio-group v-model="currentTopicModuleInfo.topicType" disabled>
                          <a-radio value="topic"> 专题画布 </a-radio>
                          <a-radio value="group"> 专题分组 </a-radio>
                        </a-radio-group>
                      </a-form-model-item>
                    </a-col>
                  </a-row>
                </a-form-model>
                <a-form-model
                  v-else
                  ref="rightForm"
                  slot="detail"
                  :model="currentTopicModuleInfo"
                  :rules="rightFormRules"
                >
                  <!-- 模块表单 -->
                  <a-row>
                    <a-col :span="24">
                      <a-form-model-item
                        label="模块名称"
                        prop="moduleName"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-input
                          v-model="currentTopicModuleInfo.moduleName"
                          placeholder="请输入模块名称"
                        ></a-input>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="标题名称"
                        prop="titleName"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-input
                          v-model="currentTopicModuleInfo.titleName"
                          placeholder="请输入标题名称"
                        ></a-input>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="模块默认高度"
                        prop="moduleLength"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-input-number
                          v-model="currentTopicModuleInfo.moduleLength"
                          placeholder="请输入模块默认高度"
                        ></a-input-number>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="模块默认宽度"
                        prop="moduleWidth"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-input-number
                          v-model="currentTopicModuleInfo.moduleWidth"
                          placeholder="请输入模块默认宽度"
                        ></a-input-number>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="模块描述"
                        prop="moduleDes"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-textarea
                          v-model="currentTopicModuleInfo.moduleDes"
                          placeholder="请输入模块描述"
                        ></a-textarea>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-model-item
                        label="是否弹窗"
                        prop="isPopup"
                        :labelCol="labelCol"
                        :wrapperCol="wrapperCol"
                      >
                        <a-radio-group v-model="currentTopicModuleInfo.isPopup">
                          <a-radio :value="1"> 是 </a-radio>
                          <a-radio :value="0"> 否 </a-radio>
                        </a-radio-group>
                      </a-form-model-item>
                    </a-col>
                  </a-row>
                </a-form-model>
              </j-form-container>
            </a-spin>
            <div v-if="showRightForm" class="button-group" style="float: right">
              <a-button
                v-if="currentTreeNode?.treeType !== 'group'"
                type="primary"
                @click="handleEditCanvas(currentTopicModuleId, currentWorkbenchId)"
                >编辑画布</a-button
              >
              <a-button type="primary" @click="handleSaveEdit">保存</a-button>
              <a-button
                v-if="currentTreeNode?.treeType !== 'group'"
                type="primary"
                @click="handlePreview(currentTopicModuleId, currentWorkbenchId)"
                >预览</a-button
              >
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 保留原有的模态框组件 -->
    <ScCockpitTopicAddModal ref="topicAddModal" @ok="loadTopicList(currentWorkbenchId)" />
    <ScCockpitModuleAddModal
      ref="moduleAddModal"
      @ok="loadTopicList(currentWorkbenchId)"
    ></ScCockpitModuleAddModal>
  </div>
</template>
<script>
import { getAction, postAction, deleteAction, getFileAccessHttpUrl } from '@/api/manage'
import ScCockpitTopicAddModal from '@/views/index/modules/ScCockpitTopicAddModal.vue'
import ScCockpitModuleAddModal from '@/views/index/modules/ScCockpitModuleAddModal.vue'
import ScCockpitTopicManage from './modules/ScCockpitTopicManage.vue'
// import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { routerTurnByPath } from '@/utils/router'
import { randomUUID } from '@/utils/util'
import draggable from 'vuedraggable'
import { pick } from 'lodash-es'

export default {
  name: 'ScCockpitTopicList',
  // mixins: [JeecgListMixin],
  components: { ScCockpitTopicAddModal, ScCockpitModuleAddModal, ScCockpitTopicManage, draggable },
  data() {
    return {
      searchParams: {
        workbenchName: '',
        topicName: ''
      },
      workbenchList: [],
      topicList: [],
      currentWorkbenchId: undefined, // 用于存储当前选中的工作台ID
      currentTopicModuleId: '', // 用于存储当前选中的专题或模块ID
      currentTopicModuleInfo: {},
      currentTreeNode: null,
      confirmLoading: false,
      workbenchInfo: {},
      loading: false,
      dropTrigger: '',
      rightClickSelectedRecord: undefined,
      showRightForm: false,
      addTopic: false,
      currSelected: {},
      searchValue: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      // 大屏工作台示例数据
      url: {
        listWorkbench: '/workbench/scCockpitWorkbench/allWorkbenchList',
        treeListTopic: '/workbench/scCockpitWorkbench/treeList',
        deleteWorkbench: '/workbench/scCockpitWorkbench/delete',
        deleteTopic: '/topic/scCockpitTopicInfo/delete',
        deleteModule: '/module/scCockpitModuleConfig/delete',
        queryModuleById: '/module/scCockpitModuleConfig/queryById',
        queryTopicById: '/topic/scCockpitTopicInfo/queryById',
        queryWorkBenchById: '/workbench/scCockpitWorkbench/queryById',
        editModule: '/module/scCockpitModuleConfig/edit',
        editTopic: '/topic/scCockpitTopicInfo/edit'
      },
      rightFormRules: {
        moduleName: [{ required: true, message: '请输入模块名称', trigger: 'change' }],
        titleName: [{ required: true, message: '请选择标题名称', trigger: 'change' }],
        moduleDes: [{ required: true, message: '请输入模块描述', trigger: 'change' }],
        topicName: [{ required: true, message: '请输入专题名称', trigger: 'change' }],
        topicType: [{ required: true, message: '请选择专题类型', trigger: 'change' }],
        topicCode: [{ required: true, message: '请输入专题编码', trigger: 'change' }],
        topicDescription: [{ required: true, message: '请输入专题描述', trigger: 'change' }]
      }
    }
  },
  computed: {
    displayedWorkbenchList() {
      if (!this.currentWorkbenchId) {
        // 如果没有选中任何工作台（例如初始加载或重置后），则显示所有工作台
        // 或者根据产品需求，可以默认显示第一个，或者不显示任何内容直到用户选择
        return this.workbenchList
      }
      return this.workbenchList.filter((w) => w.id === this.currentWorkbenchId)
    }
  },
  created() {
    this.loadWorkbenchList()
  },
  methods: {
    loadWorkbenchList() {
      const params = {
        ...this.searchParams,
        pageNo: 1,
        pageSize: 1000
      }
      getAction(this.url.listWorkbench, params).then((res) => {
        if (res.success) {
          this.workbenchList = res.result
          this.currentWorkbenchId = this.workbenchList[0]?.id
          this.loadTopicList(this.currentWorkbenchId)
        } else {
          this.$message.error(res.message || '加载工作台列表失败')
          this.workbenchList = []
          this.topicList = []
          this.currentWorkbenchId = undefined
        }
      })
    },
    loadTopicList(currentWorkbenchId, clearCurrentSelect = false) {
      if (clearCurrentSelect) {
        this.currentTreeNode = null
        this.currentTopicModuleId = ''
        this.currentTopicModuleInfo = {}
      }
      this.loading = true
      this.currentWorkbenchId = currentWorkbenchId
      getAction(this.url.queryWorkBenchById, { id: currentWorkbenchId }).then((res) => {
        if (res.success) {
          this.workbenchInfo = Object.assign(this.workbenchInfo, {
            defaultMapUrl: res.result.mapComponent,
            defaultMenuUrl: res.result.menuComponent,
            defaultBgImage: res.result.bgImage
          })
        }
      })
      getAction(this.url.treeListTopic, { workbenchId: currentWorkbenchId })
        .then((res) => {
          if (res.success) {
            this.topicList = this.processTreeData(res.result)
          } else {
            this.$message.error(res.message || '加载专题列表失败')
            this.topicList = []
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    searchTopicModule(treeName) {
      let topicListFlat = []
      topicListFlat = this.generateList(this.topicList)
      const expandedKeys = topicListFlat
        .map((item) => {
          if (item.title.indexOf(treeName) > -1) {
            return this.getParentKey(item.key, this.topicList)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        iExpandedKeys: expandedKeys,
        searchValue: treeName,
        autoExpandParent: true
      })
    },
    generateList(data) {
      let topicListFlat = []
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        const key = node.id
        topicListFlat.push({ key, title: node.treeName })
        if (node.childTreeList) {
          this.generateList(node.childTreeList)
        }
      }
      return topicListFlat
    },
    getParentKey(key, tree) {
      let parentKey
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.childTreeList) {
          if (node.childTreeList.some((item) => item.id === key)) {
            parentKey = node.id
          } else if (this.getParentKey(key, node.childTreeList)) {
            parentKey = this.getParentKey(key, node.childTreeList)
          }
        }
      }
      return parentKey
    },
    getIcon(props) {
      if (props.treeType === 'group') {
        return <a-icon type="folder-open" />
      } else if (props.treeType === 'module') {
        return <a-icon type="profile" />
      } else if (props.treeType === 'topic') {
        return <a-icon type="appstore" />
      }
    },
    test(workbenchId) {
      const id = randomUUID()
      const path = '/chart/home'
      routerTurnByPath(path, [id, workbenchId, 'topic'], false, true)
    },
    modalFormOk() {
      this.loadWorkbenchList()
    },
    hide() {
      this.visible = false
    },
    handleAddTopic(parent) {
      if (parent && (parent.treeType === 'module' || parent.treeType === 'topic')) {
        this.$message.warning('只能在分组专题或根目录下新增专题')
        return
      }
      const record = {
        pid: parent ? parent.id : '0',
        relWorkbench: this.currentWorkbenchId
      }
      this.$refs.topicAddModal.title = '新增专题'
      this.$refs.topicAddModal.add(record)
    },
    handleAddModule(parent) {
      if ((parent && (parent.treeType === 'module' || parent.treeType === 'topic')) || !parent) {
        this.$message.warning('只能在分组专题下新增模块')
        return
      }
      const record = {
        topicId: parent.id,
        relWorkbench: this.currentWorkbenchId
      }
      this.$refs.moduleAddModal.title = '新增模块'
      this.$refs.moduleAddModal.add(record)
    },
    handleDelete(node) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除此专题吗?',
        onOk: () => {
          // if (topic.type === 'screen') {
          //   this.screenTopics = this.screenTopics.filter((t) => t.id !== topic.id)
          // } else {
          //   this.customTopics = this.customTopics.filter((t) => t.id !== topic.id)
          // }
          const url = node.treeType === 'module' ? this.url.deleteModule : this.url.deleteTopic
          deleteAction(url, { id: node.id })
            .then((res) => {
              if (res.success) {
                this.$message.success('删除成功')
                this.showRightForm = false
                this.loadTopicList(this.currentWorkbenchId)
              } else {
                this.$message.warning('删除失败')
              }
            })
            .finally(() => {})
        }
      })
    },
    handleDeleteWorkbench(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除工作台 "${record.workbenchName}" 吗？删除后关联的专题将同时被删除`,
        onOk: () => {
          getAction(this.url.deleteWorkbench, { id: record.id }).then((res) => {
            if (res.success) {
              this.$message.success('删除成功')
              this.loadWorkbenchList() // 重新加载列表，树和右侧都会更新
              // 如果删除的是当前选中的工作台，则清空currentWorkbenchId或选中第一个
              if (this.currentWorkbenchId === record.id) {
                this.currentWorkbenchId =
                  this.workbenchList.length > 0 ? this.workbenchList[0].id : null
              }
            }
          })
        }
      })
    },
    getAvatar(avatar) {
      return getFileAccessHttpUrl(avatar)
    },
    onTreeSelect(selectedKeys, e) {
      this.confirmLoading = true
      this.currentTopicModuleId = selectedKeys[0]
      this.currentTreeNode = this.currentTopicModuleId ? e.node.dataRef : null
      if (!this.currentTreeNode) {
        this.showRightForm = false
        this.currentTopicModuleInfo = {}
        return
      }
      const url =
        this.currentTreeNode.treeType === 'module'
          ? this.url.queryModuleById
          : this.url.queryTopicById
      getAction(url, { id: this.currentTopicModuleId })
        .then((res) => {
          if (res.success) {
            this.currentTopicModuleInfo = res.result
            this.showRightForm = true
          } else {
            this.$message.warning('信息查询失败', res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    }, // 右键点击下拉关闭下拉框
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      const record = node.node.dataRef
      this.rightClickSelectedRecord = record
      // this.topicType = record.topicType
    },
    handleEditCanvas(id, workbenchId) {
      const path = '/chart/home'
      routerTurnByPath(path, [id, workbenchId, this.currentTreeNode.treeType], false, true) // 数组第三个元素为是否模块
    },
    handleSaveEdit() {
      let url, formData
      if (this.currentTreeNode.treeType !== 'module') {
        url = this.url.editTopic
        formData = this.currentTopicModuleInfo
      } else {
        url = this.url.editModule
        formData = this.currentTopicModuleInfo
      }
      formData.id = this.currentTopicModuleId
      console.log('formData', formData)
      postAction(url, formData).then((res) => {
        if (res.success) {
          this.$message.success('编辑成功')
          this.loadTopicList(this.currentWorkbenchId)
        } else {
          this.$message.warning('编辑失败', res.message)
        }
      })
    },
    handlePreview(id, workbenchId) {
      const url =
        this.currentTreeNode.treeType === 'module'
          ? this.url.queryModuleById
          : this.url.queryTopicById
      getAction(url, { id: this.currentTopicModuleId }).then((res) => {
        if (res.success) {
          this.currentTopicModuleInfo = res.result
          this.showRightForm = true
          let designData
          if (this.currentTreeNode.treeType !== 'module') {
            const content = JSON.parse(this.currentTopicModuleInfo.topicJson)
            designData = {
              ...this.currentTopicModuleInfo,
              ...content,
              ...this.workbenchInfo,
              scaleX: content.topicWidth,
              scaleY: content.topicHeight
            }
          } else {
            const content = JSON.parse(this.currentTopicModuleInfo.moduleData)
            designData = {
              ...this.currentTopicModuleInfo,
              ...this.workbenchInfo,
              ...content,
              scaleX: this.currentTopicModuleInfo.moduleWidth,
              scaleY: this.currentTopicModuleInfo.moduleLength
            }
          }
          localStorage.setItem('designCache', JSON.stringify(designData))
          const path = '/chart/preview'
          routerTurnByPath(path, [id, workbenchId, this.currentTreeNode.treeType], false, true)
        } else {
          this.$message.warning(
            `${this.currentTreeNode.treeType === 'module' ? '模块' : '专题'}信息查询失败`
          )
        }
      })
    },
    processTreeData(nodes, parentDisabled = false) {
      return nodes.map((node) => {
        const isTopic = node.treeType === 'topic'

        // 核心逻辑
        let disabled = parentDisabled // 默认继承父级禁用状态
        let childParentDisabled = parentDisabled // 默认传递父级状态

        if (isTopic) {
          disabled = false // 强制不禁用自身
          childParentDisabled = true // 强制子节点被禁用
        }

        const childTreeList = node.childTreeList
          ? this.processTreeData(node.childTreeList, childParentDisabled)
          : undefined

        return {
          ...node,
          disabled,
          childTreeList
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.cockpit-container {
  // padding: 0 20px; // 已移动到 right-panel
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.main-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-panel {
  width: 250px;
  // padding: 20px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
  // margin-right: 1px; // 暂时移除，看是否需要
}

.right-panel {
  flex: 1;
  padding: 0 20px;
  overflow-y: auto;
  // background-color: #f0f2f5; // 如果 cockpit-container 已有，这里可能不需要重复
}

.search-area {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #e8e8e8;
}

.search-item {
  display: flex;
  align-items: center;
  margin-right: 20px;

  .label {
    white-space: nowrap;
    margin-right: 8px;
  }

  .ant-input {
    width: 200px;
  }
}

.search-btn {
  margin-right: 10px;
}

.add-btn {
  margin-left: auto;
}

.workspace-section {
  margin-top: 20px;
  background-color: #fff;
  border-radius: 4px;
  padding: 0 0 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #f0f2f5;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.actions {
  display: flex;

  .action-link {
    margin-left: 15px;
    color: #1890ff;
    cursor: pointer;
  }
}

.topic-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20px;
}

.topic-item {
  position: relative;
  width: 250px;
  height: 180px;
  margin: 0 15px 15px 0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);

    .topic-actions {
      opacity: 1;
    }
  }

  .topic-actions {
    position: absolute;
    top: 0;
    right: 0;
    padding: 8px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 0 0 0 4px;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1;

    .action-icon {
      color: #fff;
      font-size: 16px;
      margin-left: 8px;
      cursor: pointer;

      &:hover {
        color: #1890ff;
      }
    }
  }

  img {
    width: 100%;
    height: calc(100% - 40px);
    object-fit: cover;
    background-color: #f0f2f5;
  }

  p {
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin: 0;
    padding: 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: #fff;
  }
}

.add-item {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;

  .add-icon {
    font-size: 40px;
    color: #999;
  }

  &:hover {
    border-color: #1890ff;

    .add-icon {
      color: #1890ff;
    }
  }
}

// 保留一些原有的样式
.ant-btn {
  margin-left: 10px;
}

.department-tree {
  margin-top: 5px;
  width: 100%;
  height: 495px;
  overflow: auto;
}
::v-deep .ant-tree {
  height: calc(100vh - 360px);
  overflow-y: auto;
}
</style>
