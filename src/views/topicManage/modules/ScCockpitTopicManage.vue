<template>
  <div>
    <div style="display: flex; justify-content: start; align-items: center">
      <div class="config-mode">
        <!-- 切换配置模式，单一项目的手机端/pad端和大屏端按钮一般不能同时可选 -->
        <div>配置模式：</div>
        <a-radio-group v-model="configMode">
          <a-radio :value="1" :disabled="configMode === 3"> 手机端 </a-radio>
          <a-radio :value="2" :disabled="configMode === 3"> pad端 </a-radio>
          <a-radio :value="3" :disabled="configMode !== 3"> 大屏端 </a-radio>
        </a-radio-group>
      </div>
      <a-button type="primary" style="margin-left: 10px" @click="handleTopicInfo"
        >专题信息</a-button
      >
      <a-button type="primary" style="margin-left: 10px" @click="handleAddModule"
        >添加模块</a-button
      >
      <!-- <a-button type="primary" @click="handlePreview" style="margin-left: 10px">预览</a-button> -->
      <a-button type="primary" style="margin-left: 10px" @click="submitForm">保存</a-button>
    </div>
    <a-divider type="horizontal"></a-divider>
    <div class="config-preview-container">
      <a-alert
        class="alert"
        :message="unSave ? '有修改未保存' : '所有修改已保存'"
        :type="unSave ? 'warning' : 'success'"
        show-icon
      />
      <ScCockpitModuleContainer
        ref="ScCockpitModuleContainer"
        :previewImg="previewImgUrl"
        @modules="handleModulesChange"
      ></ScCockpitModuleContainer>
    </div>
    <ScCockpitModuleListModal
      ref="ScCockpitModuleListModal"
      @ok="moduleFormOk"
    ></ScCockpitModuleListModal>
    <ScCockpitTopicInfoModal
      ref="ScCockpitTopicInfoModal"
      @ok="topicInfoOk"
    ></ScCockpitTopicInfoModal>
  </div>
</template>
<script>
import { getAction, postAction, base64ToFile, httpAction } from '../../../api/manage'
import ScCockpitModuleListModal from './ScCockpitModuleListModal.vue'
import ScCockpitModuleContainer from './ScCockpitModuleContainer.vue'
import ScCockpitTopicInfoModal from './ScCockpitTopicInfoModal.vue'
import domToImage from 'dom-to-image'
import { pick } from 'lodash-es'
export default {
  name: 'ScCockpitTopicManage',
  components: { ScCockpitModuleListModal, ScCockpitModuleContainer, ScCockpitTopicInfoModal },
  data() {
    return {
      relModule: null,
      topicMap: '',
      topicIcon: '',
      domToImage,
      previewImgUrl: '',
      groupId: '',
      addTopic: false,
      model: null,
      currentSelectTopicId: '',
      unSave: false,
      sortOrder: '',
      topicInfo: {},
      configMode: 3,
      url: {
        queryModuleById: '/module/scCockpitModuleConfig/queryById',
        upload: window._CONFIG['domianURL'] + '/sys/common/upload',
        add: '/topic/scCockpitTopicInfo/add',
        edit: '/topic/scCockpitTopicInfo/edit',
        queryById: '/topic/scCockpitTopicInfo/queryById'
      }
    }
  },
  watch: {
    configMode(val) {
      this.$refs.ScCockpitModuleContainer.changeConfigMode(val)
    }
  },
  mounted() {
    this.$refs.ScCockpitModuleContainer.changeConfigMode(this.configMode)
  },
  methods: {
    initFormData(id, sortOrder, groupId) {
      if (id === this.currentSelectTopicId) return
      this.currentSelectTopicId = id
      this.groupId = groupId
      this.sortOrder = sortOrder
      getAction(this.url.queryById, { id }).then(async (res) => {
        if (res.success) {
          this.model = res.result
          this.topicInfo = {
            topicMap: this.model.topicMap,
            sortOrder: this.sortOrder,
            status: this.model.status,
            topicName: this.model.topicName
          }
          this.previewImgUrl = this.model.previewImg
          this.relModule = this.model.relModule
          this.$refs.ScCockpitModuleContainer.leftModules = []
          this.$refs.ScCockpitModuleContainer.rightModules = []
          this.$refs.ScCockpitModuleContainer.changeBg(this.model.topicMap)
          const relModuleArr = this.model.relModule.split(',').filter((relModule) => {
            return relModule && relModule !== 'separator'
          })
          const leftLastIndex = this.model.relModule.split(',').indexOf('separator')
          for (let i = 0; i < relModuleArr.length; i++) {
            const id = relModuleArr[i]
            await getAction(this.url.queryModuleById, { id }).then((res) => {
              if (res.success) {
                this.$refs.ScCockpitModuleContainer.addModule(
                  res.result,
                  false,
                  i === relModuleArr.length - 1,
                  i < leftLastIndex ? 'left' : 'right'
                )
              } else {
                this.$message.warning('模块信息获取失败')
              }
            })
          }
        }
      })
    },

    handleTopicInfo() {
      this.$refs.ScCockpitTopicInfoModal.visible = true
      this.addTopic
        ? this.$refs.ScCockpitTopicInfoModal.add()
        : this.$refs.ScCockpitTopicInfoModal.edit(this.topicInfo)
    },
    handleAddModule() {
      this.$refs.ScCockpitModuleListModal.visible = true
    },
    handleAddBackground() {},
    async moduleFormOk(moduleIdList) {
      for (let i = 0; i < moduleIdList.length; i++) {
        const id = moduleIdList[i]
        await getAction(this.url.queryModuleById, { id }).then((res) => {
          if (res.success) {
            this.$refs.ScCockpitModuleContainer.addModule(
              res.result,
              true,
              i === moduleIdList.length - 1,
              'left'
            )
            this.unSave = true
          }
        })
      }
    },
    topicInfoOk(topicInfo) {
      this.topicInfo = topicInfo
      console.log('topicInfo', topicInfo)

      this.$refs.ScCockpitTopicInfoModal.visible = false
      this.$refs.ScCockpitModuleContainer.changeBg(topicInfo.topicMap)
    },
    handlePreview() {
      this.$refs.ScCockpitModuleContainer.render()
    },
    handleModulesChange(obj) {
      const leftModules = obj.left
        .map((module) => {
          return module.id
        })
        .join(',')
      const rightModules = obj.right
        .map((module) => {
          return module.id
        })
        .join(',')
      this.relModule = leftModules + ',separator,' + rightModules
      this.handlePreview()
    },
    generatePreviewImg(topicName, callback) {
      const palGradientGap = document.querySelector('.module-wrapper')
      const canvas = document.createElement('img')
      canvas.width = palGradientGap.offsetWidth
      canvas.height = palGradientGap.offsetHeight
      const that = this
      this.domToImage.toPng(palGradientGap).then(async function (canvas) {
        const previewImgFile = base64ToFile(canvas, `${topicName}.png`)
        let formData = new FormData()
        formData.append('file', previewImgFile)
        formData.append('fileName', `${topicName}.png`)
        const res = await postAction(that.url.upload, formData)
        if (res.success) {
          that.previewImgUrl = res.message
          callback()
        } else {
          that.$message.warning('专题预览图上传失败')
        }
      })
    },
    submitForm() {
      if (!this.topicInfo.topicMap.length) {
        this.$message.warning('请上传专题底图')
        return
      }
      if (!this.relModule) {
        this.$message.warning('请选择关联模块')
        return
      }
      let formData = Object.assign(
        pick(this.topicInfo, 'topicName', 'status', 'topicMap', 'sortOrder'),
        {
          relModule: this.relModule,
          groupId: this.groupId,
          topicLayout: 'default',
          topicPosition: 'default'
        }
      )
      this.generatePreviewImg(this.topicInfo.topicName, () => {
        formData.previewImg = this.previewImgUrl
        const method = this.addTopic ? 'post' : 'post'
        const url = this.addTopic ? this.url.add : this.url.edit
        httpAction(url, formData, method)
          .then((res) => {
            if (res.success) {
              this.$message.success('操作成功')
              this.$emit('ok')
            } else {
              this.$message.warning('操作失败')
            }
          })
          .finally(() => {
            this.unSave = false
          })
      })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .ant-form-explain {
  position: absolute;
}
::v-deep .j-upload {
  .ant-btn {
    i {
      display: none;
    }
    span {
      margin-left: 0;
    }
  }
  .ant-upload-list {
    display: none;
  }
}
.config-preview-container {
  position: relative;
  padding-top: 30px;
  .alert {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
  }
}
.config-mode {
  display: flex;
}
::v-deep .ant-divider-horizontal {
  margin-bottom: 0;
}
</style>
