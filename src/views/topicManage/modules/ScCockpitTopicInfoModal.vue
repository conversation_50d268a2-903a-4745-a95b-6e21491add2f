<template>
  <a-modal
    :title="title"
    :width="width"
    placement="right"
    :closable="true"
    :visible="visible"
    @close="close"
    @cancel="close"
    @ok="saveTopicInfo"
  >
    <div>
      <a-form-model slot="detail" :model="form">
        <a-form-item label="专题名称：" prop="topicName" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-model="form.topicName" placeholder="请输入专题名称"></a-input>
        </a-form-item>
        <a-form-item label="专题排序：" prop="sortOrder" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input-number v-model="form.sortOrder"></a-input-number>
        </a-form-item>
        <a-form-item label="专题状态：" prop="status" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="form.status">
            <a-radio :value="1"> 开启 </a-radio>
            <a-radio :value="0"> 关闭 </a-radio>
          </a-radio-group>
        </a-form-item>
        <!-- <a-form-item label="专题路由：" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            v-decorator="['topicRoute', { rules: [{ required: true, message: '请输入专题路由' }] }]"
            placeholder="请输入专题路由"
          ></a-input>
        </a-form-item> -->
        <a-form-item prop="topicMap" :labelCol="labelCol" :wrapperCol="wrapperCol" label="底图">
          <j-image-upload v-model="form.topicMap"></j-image-upload>
        </a-form-item>
        <!-- <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="专题图标">
          <j-image-upload v-model="topicIcon"></j-image-upload>
        </a-form-item> -->
      </a-form-model>
    </div>
  </a-modal>
</template>
<script>
export default {
  name: 'ScCockpitTopicInfoModal',
  data() {
    return {
      form: {
        topicName: undefined,
        sortOrder: undefined,
        status: 1,
        topicMap: undefined,
        topicIcon: undefined
      },
      title: '专题信息',
      width: 600,
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
  },
  methods: {
    add() {
      this.form = {
        topicName: undefined,
        sortOrder: undefined,
        status: 1,
        topicMap: undefined,
        topicIcon: undefined
      }
    },
    edit(record) {
      this.$nextTick(() => {
        this.form = {
          topicMap: record.topicMap,
          topicName: record.topicName,
          status: record.status,
          sortOrder: record.sortOrder,
          topicIcon: record.topicIcon
        }
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    saveTopicInfo() {
      this.$emit('ok', this.form)
      this.$message.success('操作成功')
    }
  }
}
</script>
