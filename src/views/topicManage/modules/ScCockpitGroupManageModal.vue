<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    cancelText="关闭"
    destroyOnClose
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form-model ref="form" slot="detail" :model="form" :rules="rules">
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="工作台名称"
                prop="workbenchName"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.workbenchName" placeholder="请输入工作台名称"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="工作台别名"
                prop="workbenchAlias"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.workbenchAlias" placeholder="请输入工作台别名"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="工作台编码"
                prop="workbenchCode"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-model="form.workbenchCode" placeholder="请输入工作台编码"></a-input>
              </a-form-model-item>
            </a-col>
            <!-- <a-col :span="24">
              <a-form-model-item
                label="是否自适应"
                prop="autpAdaptive"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-radio-group v-model="form.autpAdaptive">
                  <a-radio :value="1">是</a-radio>
                  <a-radio :value="0">否</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col> -->
            <a-col :span="24">
              <a-form-model-item
                label="宽度"
                prop="screenWidth"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input-number
                  v-model="form.screenWidth"
                  :min="0"
                  placeholder="请输入宽度"
                  style="width: 50%"
                ></a-input-number>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="高度"
                prop="screenHeight"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input-number
                  v-model="form.screenHeight"
                  :min="0"
                  placeholder="请输入高度"
                  style="width: 50%"
                ></a-input-number>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="默认背景"
                prop="bgImage"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <j-image-upload v-model="form.bgImage"></j-image-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="默认地图"
                prop="mapComponent"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-model="form.mapComponent"
                  placeholder="请输入默认地图组件地址"
                  addon-before="@/components/custom/"
                  addon-after=".vue"
                ></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item
                label="默认菜单"
                prop="menuComponent"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-model="form.menuComponent"
                  placeholder="请输入默认菜单组件地址"
                  addon-before="@/components/custom/"
                  addon-after=".vue"
                ></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
export default {
  name: 'ScCockpitGroupManageModal',
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
      form: {
        workbenchName: undefined,
        workbenchAlias: undefined,
        workbenchCode: undefined,
        autpAdaptive: 1,
        screenWidth: undefined,
        screenHeight: undefined
      },
      rules: {
        workbenchName: [{ required: true, message: '请输入工作台名称', trigger: 'blur' }],
        workbenchAlias: [{ required: true, message: '请输入工作台别名', trigger: 'blur' }],
        workbenchCode: [{ required: true, message: '请输入工作台编码', trigger: 'blur' }],
        autpAdaptive: [{ required: true, message: '请选择是否自适应', trigger: 'change' }],
        screenWidth: [{ required: true, message: '请输入宽度', trigger: 'blur' }],
        screenHeight: [{ required: true, message: '请输入高度', trigger: 'blur' }]
      },
      selectedKey: [],
      rightClickSelectedKey: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/workbench/scCockpitWorkbench/add',
        edit: '/workbench/scCockpitWorkbench/edit'
        // queryById: '/group/scCockpitGroup/queryById',
      }
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.visible = true
      this.form = { ...record }
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      if (this.disableSubmit) {
        this.close()
        return
      }
      this.submitForm()
    },
    submitForm() {
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.form.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }
          let formData = { ...this.form }
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.visible = false
                this.$emit('ok')
              } else {
                this.$message.warning(res.message)
              }
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
