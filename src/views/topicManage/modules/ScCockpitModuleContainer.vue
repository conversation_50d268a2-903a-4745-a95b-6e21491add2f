<template>
  <div class="module-wrapper">
    <div class="col">
      <div class="col-title">左列</div>
      <draggable
        v-model="leftModules"
        :group="groupA"
        animation="300"
        dragClass="dragClass"
        ghostClass="ghostClass"
        chosenClass="chosenClass"
        @start="onStart"
        @end="onEnd"
      >
        <transition-group :style="style">
          <div
            v-for="(module, moduleIndex) in leftModules"
            :id="module.id"
            :key="module.id"
            class="module"
            :class="{ 'chart-select': currentSelectModule === module.id }"
            @click="(e) => handleModuleClick(e, module.id)"
          >
            <div style="display: flex; width: 100%">
              <div class="left-compContainer" style="width: 100%">
                <div
                  v-for="(item, chartIndex) in module.moduleData.componentList.slice(0, module.leftLastIndex)"
                  :key="item.id"
                  :ref="`chart-${moduleIndex}-${chartIndex}`"
                  class="chart-item"
                  style="width: 100%"
                ></div>
              </div>
              <div
                v-if="module.leftLastIndex < module.moduleData.componentList.length - 1"
                class="right-compContainer"
                style="width: 100%"
              >
                <div
                  v-for="(item, chartIndex) in module.moduleData.componentList.slice(module.leftLastIndex + 1)"
                  :key="item.id"
                  :ref="`chart-${moduleIndex}-${chartIndex + module.leftLastIndex}`"
                  class="chart-item"
                ></div>
              </div>
            </div>
            <a-icon type="edit" class="edit-btn" title="跳转至模块编辑" @click="editModule(module.id)" />
            <a-popconfirm
              title="确定要删除此模块吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="confirmDeleteModule(module.id, moduleIndex, 'left')"
            >
              <a-icon type="close-square" class="close-btn" title="删除模块" />
            </a-popconfirm>
            <div class="moduleName">{{ module.moduleName }}</div>
          </div>
        </transition-group>
      </draggable>
    </div>
    <div class="col">
      <div class="col-title">右列（pad和大屏配置模式下可用）</div>
      <draggable
        v-model="rightModules"
        :group="groupB"
        animation="300"
        dragClass="dragClass"
        ghostClass="ghostClass"
        chosenClass="chosenClass"
        @start="onStart"
        @end="onEnd"
      >
        <transition-group :style="style">
          <div
            v-for="(module, moduleIndex) in rightModules"
            :id="module.id"
            :key="module.id"
            class="module"
            :class="{ 'chart-select': currentSelectModule === module.id }"
            @click="(e) => handleModuleClick(e, module.id)"
          >
            <div style="display: flex; width: 100%">
              <div class="left-compContainer" style="width: 100%">
                <div
                  v-for="(item, chartIndex) in module.moduleData.componentList.slice(0, module.leftLastIndex)"
                  :key="item.id"
                  :ref="`chart-${moduleIndex + leftModules.length}-${chartIndex}`"
                  class="chart-item"
                ></div>
              </div>
              <div
                v-if="module.leftLastIndex < module.moduleData.componentList.length - 1"
                class="right-compContainer"
                style="width: 100%"
              >
                <div
                  v-for="(item, chartIndex) in module.moduleData.componentList.slice(module.leftLastIndex + 1)"
                  :key="item.id"
                  :ref="`chart-${moduleIndex + leftModules.length}-${chartIndex + module.leftLastIndex}`"
                  class="chart-item"
                ></div>
              </div>
            </div>
            <a-icon type="edit" class="edit-btn" @click="editModule(module.id)" />
            <a-popconfirm
              title="确定要删除此模块吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="confirmDeleteModule(module.id, moduleIndex, 'right')"
            >
              <a-icon type="close-square" class="close-btn" />
            </a-popconfirm>
            <div class="moduleName">{{ module.moduleName }}</div>
          </div>
        </transition-group>
      </draggable>
    </div>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import { postAction, getAction } from '../../../api/manage'
import * as echarts from 'echarts'

export default {
  name: 'ScCockpitModuleContainer',
  components: { draggable },
  data() {
    return {
      currentSelectModule: null,
      leftModules: [],
      rightModules: [],
      groupA: {
        name: 'site',
        pull: true, // 可以拖入
        put: true // 可以拖出
      },
      groupB: {
        name: 'site',
        pull: false,
        put: false
      },
      style: 'min-height:250px;display: block;',
      drag: false,
      isPad: false,
      chartInstanceList: [],
      leftOffsetLeft: 0,
      rightOffsetLeft: 0,
      url: {
        preview: '/module/scCockpitModuleConfig/previewModule'
      }
    }
  },
  mounted() {
    getAction('/topic/api/listTopic').then((res) => {
      console.log('res', res)
    })
    const dom = document.querySelectorAll('.col')
    this.leftOffsetLeft = dom[0].offsetLeft + 1
    this.rightOffsetLeft = dom[1].offsetLeft + 1
  },
  methods: {
    handleModuleClick(e, id) {},
    changeHeight(id, componentList) {
      const leftLastIndex = componentList.findIndex((comp) => {
        return comp.sourceId === 'separatorFlag'
      })
      const maxCompNum = Math.max(leftLastIndex, componentList.length - leftLastIndex - 1)
      let dom = document.getElementById(id)
      let domLeft = document.querySelectorAll('.col')[0]
      let domRight = document.querySelectorAll('.col')[1]
      domLeft.style.height = 'revert'
      domRight.style.height = 'revert'
      dom.style.height = maxCompNum * 250 + 'px'
      const domHeight = Math.max(domLeft.clientHeight, domRight.clientHeight)
      domLeft.style.height = domHeight + 'px'
      domRight.style.height = domHeight + 'px'
    },
    addModule(value, messageFlag = true, emitFlag = false, position) {
      if (
        this.leftModules.some((module) => {
          return module.id === value.id
        }) ||
        this.rightModules.some((module) => {
          return module.id === value.id
        })
      ) {
        messageFlag && this.$message.warning(`模块${value.id}已存在`)
        return
      }
      value.moduleData = JSON.parse(value.moduleData)
      let leftLastIndex = value.moduleData.componentList.findIndex((comp) => {
        return comp.sourceId === 'separatorFlag'
      })
      if (leftLastIndex === -1) leftLastIndex = value.moduleData.componentList.length
      position === 'left'
        ? this.leftModules.push({ ...value, leftLastIndex })
        : this.rightModules.push({ ...value, leftLastIndex })
      this.$nextTick(() => {
        this.changeHeight(value.id, value.moduleData.componentList)
        const dom = document.getElementById(value.id)
        dom.style.background = value.moduleData.bgImage
          ? `url(${window._CONFIG['staticDomainURL']}/${value.moduleData.bgImage}) center center / 100% 100%`
          : '#f0f2f5'
      })
      this.chartInstanceList.push(new Array(value.moduleData.componentList.length).fill(null))
      emitFlag && this.$emit('modules', { left: this.leftModules, right: this.rightModules })
    },
    // 开始拖拽事件
    onStart(e) {
      this.drag = true
    },
    // 拖拽结束事件
    onEnd(e) {
      let data
      if (e.to.offsetLeft === this.leftOffsetLeft) {
        data = this.leftModules[e.newIndex]
      } else if (e.to.offsetLeft === this.rightOffsetLeft) {
        data = this.rightModules[e.newIndex]
      }
      this.drag = false
      this.$emit('modules', { left: this.leftModules, right: this.rightModules })
      setTimeout(() => {
        this.changeHeight(e.item.id, data.moduleData.componentList)
      }, 100)
    },
    editModule(id) {
      this.$router.push({ path: '/moduleList', query: { id } })
    },
    confirmDeleteModule(id, index, position) {
      let domLeft = document.querySelectorAll('.col')[0]
      let domRight = document.querySelectorAll('.col')[1]
      domLeft.style.height = 'revert'
      domRight.style.height = 'revert'
      position === 'left' ? this.leftModules.splice(index, 1) : this.rightModules.splice(index, 1)
      setTimeout(() => {
        const height = Math.max(domLeft.clientHeight, domRight.clientHeight)
        domLeft.style.height = height + 'px'
        domRight.style.height = height + 'px'
        this.$emit('modules', { left: this.leftModules, right: this.rightModules })
        this.$message.success('删除成功')
      }, 100)
    },
    renderChart(modules, position) {
      modules.forEach((module, moduleIndex) => {
        let moduleData = { ...module.moduleData }
        moduleData.componentList = moduleData.componentList.filter((component) => {
          return component.sourceId !== 'separatorFlag'
        })
        postAction(this.url.preview, moduleData).then((res) => {
          if (res.success) {
            const componentList = res.result.componentList
            module.componentList = componentList
            // 所有图表的数据列表
            const dataList = componentList.map((item) => {
              return item.dataList.records
            })
            if (
              dataList.some((data) => {
                return data.length === 0
              })
            ) {
              this.$message.warning(`模块${module.id}图表无数据`)
            }
            // 所有图表的维度列表
            const dimenssionColumnList = componentList.map((item) => {
              return item.dimensionList
            })
            // 所有图表的指标列表
            const indexColumnList = componentList.map((item) => {
              return item.indexList
            })
            for (let i = 0; i < dataList.length; i++) {
              if (!componentList[i].jsData) return
              const option = JSON.parse(componentList[i].jsData)

              dimenssionColumnList.forEach((dimensionColumnListItem) => {
                // 当前图表的维度列表
                const dimenssionName = dimensionColumnListItem[0].sourceColumn
                if (componentList[i].componentType.includes('BarChart')) {
                  option.yAxis.data = dataList[i].map((data) => {
                    return data[dimenssionName]
                  })
                  option.yAxis.name = dimenssionName
                } else {
                  option.xAxis.data = dataList[i].map((data) => {
                    return data[dimenssionName]
                  })
                  option.xAxis.name = dimenssionName
                }
              })
              indexColumnList.forEach((indexColumnListItem) => {
                // 当前图表的指标列表
                indexColumnListItem.forEach((index, i) => {
                  const indexName = index.sourceColumn
                  if (!option.series[i]) {
                    option.series[i] = Object.assign(option.series[0], { data: [] })
                  }
                  option.series[i].name = indexName
                  option.series[i].data = dataList[i].map((data) => {
                    return data[indexName]
                  })
                  if (componentList[i].componentType.includes('BarLineChart')) {
                    option.series[i].type = indexColumn.barIndex.includes(index.sourceColumnId) ? 'bar' : 'line'
                  }
                  if (componentList[i].componentType.includes('BarChart')) {
                    option.xAxis.name = indexName
                  } else if (componentList[i].componentType.includes('BarLineChart')) {
                    option.yAxis[i].name = indexName
                  } else {
                    option.yAxis.name = indexName
                  }
                })
              })
              const index = position === 'left' ? moduleIndex : moduleIndex + this.leftModules.length
              this.$nextTick(() => {
                echarts.dispose(this.$refs[`chart-${index}-${i}`][0])
                this.chartInstanceList[index][i] = echarts.init(this.$refs[`chart-${index}-${i}`][0])
                this.chartInstanceList[index][i].setOption(option)
                const that = this
                window.addEventListener('resize', function () {
                  that.chartInstanceList[index][i].resize()
                })
              })
            }
          } else {
            this.$message.warning(`模块${module.id}数据查询失败`)
          }
        })
      })
    },
    render() {
      this.chartInstanceList.forEach((item) => {
        item = new Array(item.length).fill(null)
      })
      this.renderChart(this.leftModules, 'left')
      this.renderChart(this.rightModules, 'right')
    },
    changeBg(url) {
      let dom = this.isPad ? document.querySelector('.module-wrapper') : document.querySelectorAll('.col')[0]
      dom.style.background = `url(${window._CONFIG['staticDomainURL']}/${url}) center center / 100% 100%`
    },
    changeConfigMode(mode) {
      this.isPad = mode !== 1
      this.groupB = {
        name: 'site',
        pull: true,
        put: mode !== 1
      }
    }
  }
}
</script>
<style lang="less" scoped>
.module {
  height: 250px;
  width: 100%;
  background: #f0f2f5;
  padding: 10px;
  border: 1px solid #fff;
  position: relative;
  cursor: pointer;
  &:hover {
    border: 1px solid #1890ff;
  }
  &.chart-select {
    border: 1px solid #1890ff;
  }
  .chart-item {
    height: 240px;
    width: 100%;
    cursor: pointer;
  }
  .moduleName {
    position: absolute;
    top: 10px;
    left: 10px;
  }
  .edit-btn {
    position: absolute;
    top: 0;
    right: 30px;
    font-size: 20px;
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }
  .close-btn {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 20px;
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }
}

.ghostClass {
  background-color: blue !important;
}
.chosenClass {
  background-color: #e6f7ff !important;
  opacity: 1 !important;
}
.dragClass {
  background-color: #e6f7ff !important;
  opacity: 1 !important;
  box-shadow: none !important;
  outline: none !important;
}
.itxst {
  margin: 10px;
}
.col-title {
  padding: 6px 12px;
}
.col,
.col-null {
  width: 45%;
  border: solid 1px #eee;
  border-radius: 5px;
  padding-bottom: 20px;
}

.item {
  padding: 6px 12px;
  margin: 0px 10px 0px 10px;
  border: solid 1px #eee;
  background-color: #f1f1f1;
}
.module-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-top: 30px;
}
</style>
