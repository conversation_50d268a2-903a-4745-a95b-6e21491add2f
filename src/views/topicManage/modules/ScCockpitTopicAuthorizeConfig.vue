<template>
  <div>
    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px">
      <a-input-search
        v-model="searchValue"
        placeholder="请输入角色名称进行查询"
        style="width: 85%"
        @search="onSearch"
      ></a-input-search>
      <a-button style="width: 10%" @click="onSearch('')">重置</a-button>
    </div>
    <div style="display: flex; align-items: center; justify-content: start; margin-bottom: 20px">
      <a-button type="primary" :disabled="!selectedRowKeys.length">批量添加授权</a-button>
      <a-button type="primary" :disabled="!selectedRowKeys.length" style="margin-left: 10px">批量取消授权</a-button>
    </div>
    <a-table
      ref="table"
      size="middle"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource2"
      :pagination="ipagination"
      :loading="loading"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      @change="handleTableChange"
    >
      <template slot="authorizeStatus" slot-scope="text, record">
        <div :class="getAuthorizeStatus(record) === '未授权' ? 'unAuthorized' : 'authorized'">
          {{ getAuthorizeStatus(record) }}
        </div>
      </template>
      <template slot="action" slot-scope="text, record">
        <a v-if="getAuthorizeStatus(record) === '未授权'" @click="handleAuthorize(record, 1)">添加授权</a>
        <a v-else @click="handleAuthorize(record, 2)">取消授权</a>
      </template>
    </a-table>
  </div>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
export default {
  name: 'ScCockpitTopicAuthorizeConfig',
  mixins: [JeecgListMixin],
  data() {
    return {
      dataSource2: null,
      searchValue: '',
      topicId: '',
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (t, r, index) => index + 1,
        },
        {
          title: '角色编码',
          align: 'center',
          dataIndex: 'roleCode',
        },
        {
          title: '角色名称',
          align: 'center',
          dataIndex: 'roleName',
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime',
        },
        {
          title: '授权状态',
          align: 'center',
          scopedSlots: { customRender: 'authorizeStatus' },
        },
        {
          title: '操作',
          align: 'center',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/sys/role/list',
      },
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        this.dataSource2 = val
      },
      deep: true,
    },
    topicId(val) {
      this.loadData()
      this.selectedRowKeys = []
    },
  },
  methods: {
    handleAuthorize(record, flag) {
      console.log('flag', flag)
    },
    getAuthorizeStatus(record) {
      if (record.roleCode === 'test_user') return '未授权'
      return '已授权'
    },
    onSearch(value) {
      this.searchValue = value
      this.dataSource2 = this.dataSource.filter((record) => {
        return record.roleName.indexOf(value) > -1
      })
    },
  },
}
</script>
<style lang="less" scoped>
.unAuthorized {
  width: 50%;
  margin-left: 25%;
  color: #f53e48;
  background: #fff1f0;
  border: 1px solid #ffa39e;
  border-radius: 4px;
}
.authorized {
  width: 50%;
  margin-left: 25%;
  color: #68cc3c;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}
</style>
