<template>
  <a-drawer
    :title="title"
    :width="width"
    placement="right"
    :closable="false"
    :visible="visible"
    @close="close">
    <a-tree
      v-model="selectedKeys"
      show-icon
      multiple
      treeCheckable
      :replaceFields="replaceFields"
      :treeData="treeData"
      :checkStrictly="checkStrictly"
      :expandedKeys="iExpandedKeys"
      :autoExpandParent="autoExpandParent"
      :icon="getIcon"
      @change="onChange"
      @select="onSelect"
      @expand="onExpand"
    >
    </a-tree>
    <div class="drawer-footer">
      <a-button @click="close">关闭</a-button>
      <a-button type="primary" style="margin-left: 10px" @click="handleOk">确认</a-button>
    </div>
  </a-drawer>
</template>
<script>
import { getAction } from '@/api/manage'
import { traverseTree } from '@/api/api'

export default {
  name: 'ScCockpitModuleListModal',
  data() {
    return {
      title: '选择模块',
      width: 400,
      visible: false,
      disableSubmit: false,
      iExpandedKeys: [],
      autoExpandParent: false,
      checkStrictly: true,
      treeData: [],
      selectedKeys: [],
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
      },
      url: {
        list: '/group/scCockpitGroup/queryByType?groupType=module',
      },
    }
  },
  created() {
    this.loadTree()
    this.onClearSelected()
  },
  methods: {
    loadTree() {
      this.treeData = []
      getAction(this.url.list)
        .then((res) => {
          if (res.success) {
            // this.allTreeKeys = res.result.ids
            let treeDataInit = res.result
            treeDataInit.forEach((node) => {
              traverseTree(node, (node) => {
                node.disabled = !!node.groupType
                if (node.data) {
                  // 将指标集data进行字段转换，并push到childList中，保留其type字段
                  node.data.forEach((data) => {
                    let obj = data
                    obj.groupName = data.name
                    delete obj.name
                    if (!node.childList) node.childList = [obj]
                    else node.childList.push(obj)
                  })
                  delete node.data
                }
              })
            })
            this.treeData = treeDataInit
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    onClearSelected() {
      this.selectedKeys = []
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    onSelect(selectedKeys, e) {
      this.selectedKeys = selectedKeys
    },
    onExpand(expandedKeys) {
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    onChange(val) {
      console.log('val', val)
    },
    getIcon(props) {
      if (!props.type) {
        return <a-icon type="folder-open" />
      } else if (props.type === 'module') {
        return <a-icon type="appstore" />
      }
    },
    handleOk() {
      this.visible = false
      this.$emit('ok', this.selectedKeys)
    },
  },
}
</script>
<style scoped>
.drawer-footer {
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
