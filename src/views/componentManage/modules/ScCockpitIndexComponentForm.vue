<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model slot="detail" ref="form" :model="form">
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              label="组件名称"
              prop="componentName"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input v-model="form.componentName" placeholder="请输入组件名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="组件编码"
              prop="componentCode"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input v-model="form.componentCode" placeholder="请输入组件编码"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="组件大类"
              prop="componentClass"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <j-dict-select-tag
                v-model="form.componentClass"
                type="list"
                dictCode="component_class"
                placeholder="请选择组件大类"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="组件类别"
              prop="componentType"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <j-dict-select-tag
                v-model="form.componentType"
                type="list"
                dictCode="component_type"
                placeholder="请选择组件类别"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="预览图片"
              prop="previewImage"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <j-image-upload v-model="form.previewImage" isMultiple></j-image-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否自定义" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="isCustomize">
                <a-radio :value="true">是</a-radio>
                <a-radio :value="false">否</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col v-if="isCustomize" :span="24">
            <a-form-model-item
              label="组件地址"
              prop="componentPath"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-model="form.componentPath"
                placeholder="请输入自定义组件地址"
                addon-before="@/components/custom/"
                addon-after=".vue"
              ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col v-if="!isCustomize" :span="24">
            <a-form-model-item
              label="js样式"
              prop="jsData"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <monaco-editor
                ref="jsData"
                v-model="attribute.jsData"
                language="javascript"
                width="100%"
                height="200px"
                :options="editorOptions"
              ></monaco-editor>
            </a-form-model-item>
          </a-col>
          <a-col v-if="!isCustomize" :span="24">
            <a-form-model-item
              label="css样式"
              prop="cssData"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <monaco-editor
                ref="cssData"
                v-model="attribute.cssData"
                language="javascript"
                width="100%"
                height="200px"
                :options="editorOptions"
              ></monaco-editor>
            </a-form-model-item>
          </a-col>

          <a-col v-if="!isCustomize" :span="24">
            <a-form-model-item
              label="组件代码"
              prop="styleCode"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <monaco-editor
                ref="styleCode"
                v-model="attribute.styleCode"
                language="javascript"
                width="100%"
                height="200px"
                :options="editorOptions"
              ></monaco-editor>
            </a-form-model-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction } from '@/api/manage'
import { pick } from 'lodash-es'

export default {
  name: 'ScCockpitIndexComponentForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: {
        componentName: '',
        componentCode: '',
        componentClass: '',
        componentType: '',
        previewImage: '',
        componentPath: '',
        cssData: '',
        jsData: '',
        styleCode: ''
      },
      model: {},
      isCustomize: 0,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        componentName: { rules: [{ required: true, message: '请输入组件名称!' }] },
        componentCode: { rules: [{ required: true, message: '请输入组件编码!' }] },
        componentClass: { rules: [{ required: true, message: '请选择组件大类!' }] },
        componentType: { rules: [{ required: true, message: '请选择组件类别!' }] }
      },
      editorOptions: {
        tabSize: 4,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      },
      url: {
        add: '/component/scCockpitIndexComponent/add',
        edit: '/component/scCockpitIndexComponent/edit',
        queryById: '/component/scCockpitIndexComponent/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.jsData && this.$refs.jsData.resize()
      this.$refs.cssData && this.$refs.cssData.resize()
      this.$refs.styleCode && this.$refs.styleCode.resize()
    })
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.isCustomize = !!this.model.componentPath
      this.$nextTick(() => {
        this.form = pick(
          this.model,
          'componentName',
          'componentCode',
          'componentClass',
          'componentType',
          'previewImage',
          'cssData',
          'jsData',
          'styleCode',
          'componentPath'
        )
        if (!this.form.jsData) this.form.jsData = 'option = {\n\n}'
        if (!this.form.cssData) this.form.cssData = 'option = {\n\n}'
        if (!this.form.styleCode) this.form.styleCode = 'option = {\n\n}'
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }
          let formData = Object.assign(this.model, this.form)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    resetFields() {
      this.form = {
        componentName: '',
        componentCode: '',
        componentClass: '',
        componentType: '',
        previewImage: '',
        componentPath: '',
        cssData: '',
        jsData: '',
        styleCode: '{}'
      }
    }
  }
}
</script>
