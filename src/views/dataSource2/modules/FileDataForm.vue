<template>
  <a-form :form="form">
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="文件名称">
      <a-input placeholder="请输入文件名称" v-decorator="['fileName', validatorRules.fileName]" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="文件上传">
      <j-upload :number="1" v-model="fileList"></j-upload>
    </a-form-item>

  </a-form>
</template>

<script>
import pick from 'lodash.pick'
import { queryDepartTreeList } from '@/api/api'
export default {
  name: 'InterfaceBaseForm',
  components: {},
  props: {

  },
  data() {
    return {
      fileList: [],
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        fileName: { rules: [{ required: true, message: '请输入文件名称!' }] }
      }
    }
  },
  created() {
  },
  methods: {
    close() {
      this.fileList = []
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped>
</style>
