<template>
  <a-modal
    v-model="visible"
    :title="title"
    :width="1200"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
    </template>
    <a-spin :spinning="loading">
      <div class="data-edit-container">
        <div class="data-top">
          <div v-if="queryParam.where.length > 0">
            <div class="top-filter">
              <span style="line-height: 36px">过滤条件匹配：</span>
              <a-select v-model="queryParam.connType" style="width: 430px; margin-right: 15px">
                <a-select-option key="And"> And(所有条件都要求匹配) </a-select-option>
                <a-select-option key="Or"> Or(条件中的任意一个匹配) </a-select-option>
              </a-select>
              <a-button type="primary" icon="search" @click="handleSearchQuery">查询</a-button>
              <a-button
                type="primary"
                icon="reload"
                style="margin-left: 8px"
                @click="handleSearchReset"
                >重置</a-button
              >
            </div>
            <div class="top-content">
              <div v-for="item in queryParam.where" :key="item.sid" class="item-select">
                <a-select
                  v-model="item.column"
                  allowClear
                  placeholder="请选择字段名称"
                  style="width: 352px"
                  @change="handleSelectName($event, item)"
                >
                  <a-select-option v-for="option in columns" :key="option.dataIndex">
                    {{ option.title }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="item.operator"
                  allowClear
                  placeholder="请选择过滤条件"
                  style="width: 182px; margin-left: 15px; margin-right: 15px"
                >
                  <a-select-option v-for="tag in optionRuleArray" :key="tag.id">
                    {{ tag.name }}
                  </a-select-option>
                </a-select>
                <a-input
                  v-if="item.columnType === 'string'"
                  v-model="item.value"
                  allowClear
                  placeholder="请输入字段值"
                  style="width: 252px"
                />
                <a-input-number
                  v-if="item.columnType === 'number'"
                  v-model="item.value"
                  placeholder="请输入数值"
                  style="width: 140px"
                />
                <a-date-picker
                  v-if="item.columnType === 'date'"
                  v-model="item.value"
                  allowClear
                  show-time
                  placeholder="请选择日期"
                  format="YYYY-MM-DD HH:mm:ss"
                  style="width: 240px"
                />
                <a-button style="width: 40px; margin-left: 15px" @click="handleAddItem(item)">
                  +
                </a-button>
                <a-button
                  style="width: 40px"
                  :style="{ marginLeft: queryParam.where.length > 2 ? '3px' : '5px' }"
                  @click="handleDeleteItem(item)"
                >
                  -
                </a-button>
              </div>
            </div>
          </div>
          <a-empty
            v-if="queryParam.where.length === 0"
            :description="false"
            style="margin-top: 20px"
          >
            <a-button type="link" style="color: #3171ee; margin-right: 0" @click="handleAddItemNew"
              >新建查询条件</a-button
            >
          </a-empty>
        </div>
        <div class="data-content" :style="{ marginTop: tableType === '3' ? '' : '16px' }">
          <div v-if="tableType === '3'" class="btn-group">
            <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
            <a-button type="primary" icon="delete" @click="handleBatchDelete">批量删除</a-button>
          </div>
          <div class="table-content">
            <div
              v-if="tableType === '3'"
              class="ant-alert ant-alert-info"
              style="margin-bottom: 16px"
            >
              <i class="anticon anticon-info-circle ant-alert-icon"></i>
              已选择
              <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>
              项
              <a style="margin-left: 24px" @click="handleClearSelected">取消选择</a>
            </div>
            <a-table
              ref="table"
              size="middle"
              bordered
              rowKey="id"
              :scroll="{ x: 1160, y: 230 }"
              :columns="columns"
              :dataSource="dataSource"
              :pagination="ipagination"
              :loading="tableLoading"
              :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: handleSelectChange }"
              @change="handleTableChange"
            >
              <template slot="action" slot-scope="text, record">
                <span>
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical" />
                  <a-popconfirm
                    title="数据删除后无法恢复，确定删除吗?"
                    @confirm="() => handleDelete(record.id, 'POST')"
                  >
                    <a>删除</a>
                  </a-popconfirm>
                </span>
              </template>
              <!-- 字符串超长截取省略号显示-->
              <span slot="ellipsis" slot-scope="text">
                <j-ellipsis v-if="text && text.length > 30" :value="text" />
                <span v-else-if="text === null || text === undefined">-</span>
                <span v-else>{{ text }}</span>
              </span>
            </a-table>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { deleteAction, getAction, postParamAction, postAction } from '@/api/manage'
import { pick } from 'lodash-es'
export default {
  name: 'DataEditFile',
  data() {
    return {
      title: '查看数据',
      visible: false,
      loading: false,
      tableType: '',
      baseParam: {},
      queryOptionArray: [],
      optionRuleArray: [
        { id: 'eq', name: '等于' },
        { id: 'like', name: '包含' },
        { id: 'right_like', name: '以..开始' },
        { id: 'left_like', name: '以..结尾' },
        { id: 'in', name: '在...中' },
        { id: 'ne', name: '不等于' },
        { id: 'gt', name: '大于' },
        { id: 'ge', name: '大于等于' },
        { id: 'lt', name: '小于' },
        { id: 'le', name: '小于等于' }
      ],
      // 查询条件
      queryParam: {
        connType: 'And',
        pageNo: 0,
        pageSize: 0,
        where: []
      },
      columns: [], // 表头数据
      /* 数据源 */
      dataSource: [],
      tableLoading: false,
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return `${range[0]}-${range[1]} 共${total}条`
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      selectedRowKeys: [], // table选中keys
      selectionRows: [], // table选中records
      editingKey: '', // 当前正在行编辑的数据对应id
      url: {
        list: '/index/scCockpitIndexGroupConfig/previewDataWithOutSave',
        listColumns: '/data/scCockpitDataSource/queryById',
        delete: '/dataSource/tableMaintain/delete',
        deleteBatch: '/dataSource/tableMaintain/deleteBatch'
      },
      operateFlag: false,
      operateAction: 'Add',
      editRecord: {}
    }
  },
  methods: {
    /**
     * @description 打开modal框
     * @param {Object} record
     * @param {Object} obj
     * @returns void
     */
    handleOpenData(record, obj) {
      this.visible = true
      this.tableType = record.tableType
      this.baseParam = obj
      // 查询公用列数据
      this.getColumnsData(this.baseParam)
    },

    /**
     * @description 获取公用列数据
     * @param {Object} obj
     * @returns void
     */
    getColumnsData(obj) {
      getAction(this.url.listColumns, { id: obj.sourceId })
        .then((res) => {
          if (res.success) {
            this.baseParam.dataSourceCode = res.result.tableSourceCode
            const dataColumnList = res.result.dataColumnList
            // 处理table表头
            if (dataColumnList.length > 0) {
              // 需要处理查询条件得高级选项
              this.queryOptionArray = Object.assign([], dataColumnList)
              this.columns = dataColumnList
              this.columns.map((item) => {
                if (this.columns.length > 4) item.width = 215 // 数据列超过4列时设置列宽度
                item.ellipsis = true
                item.scopedSlots = { customRender: 'ellipsis' }
                item.title = item.columnName
                item.align = 'center'
                item.dataIndex = item.columnName
              })
              if (this.tableType === '3') {
                // 新增的表，追加操作栏
                this.columns.push({
                  title: '操作',
                  dataIndex: 'action',
                  align: 'center',
                  fixed: 'right',
                  width: 180,
                  scopedSlots: { customRender: 'action' }
                })
              }
              // 请求表格数据
              this.getTableData(1)
            }
          } else {
            this.$message.warning(res.massage)
          }
        })
        .catch(() => {
          this.columns = []
        })
    },

    /**
     * @description 获取表格数据
     * @param {Number} start
     * @returns void
     */
    getTableData(start) {
      // 加载数据 若传入参数1则加载第一页的内容
      if (start === 1) this.ipagination.current = 1
      // 处理查询条件
      const params = Object.assign(
        {},
        {
          ...this.queryParam,
          ...pick(this.baseParam, 'tableName', 'dataSourceCode'),
          columns: this.columns.map((column) => {
            return column.columnName
          })
        }
      )
      params.pageNo = this.ipagination.current
      params.pageSize = this.ipagination.pageSize
      // 过滤高级查询条件(仅收集字段名称和过滤条件存在的查询条件行数据)
      const tempArr = []
      params.where.map((item) => {
        if (item.column && item.operator) {
          const tempObj = {
            column: item.column,
            operator: item.operator,
            // 时间类型查询参数需进行格式化
            value: ''
          }
          // 时间类型查询参数需进行格式化
          if (item.columnType === 'date') {
            if (item.value === '' || item.value === null || item.value === undefined) {
              tempObj.value = ''
            } else {
              tempObj.value = moment(item.value).format('YYYY-MM-DD HH:mm:ss')
            }
          } else {
            tempObj.value = item.value
          }
          tempArr.push(tempObj)
        }
      })
      params.where = tempArr

      this.tableLoading = true
      postAction(this.url.list, params)
        .then((res) => {
          if (res.success) {
            this.dataSource = res.result.records
            if (!this.dataSource.length) {
              this.ipagination.total = 0
              this.tableLoading = false
              return
            }
            const keys = Object.keys(this.dataSource[0])
            this.columns = this.columns.filter((column) => {
              return keys.includes(column.dataIndex)
            })
            if (res.result.total) {
              this.ipagination.total = res.result.total
            } else {
              this.ipagination.total = 0
            }
          } else {
            this.$message.warning(res.message)
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
          this.ipagination.total = 0
        })
    },

    /**
     * @description 分页查询操作
     * @param {Object} pagination
     * @param {Object} filters
     * @param {Object} sorter
     * @returns void
     */
    handleTableChange(pagination, filters, sorter) {
      this.ipagination = pagination
      this.getTableData()
    },

    /**
     * @description 多选触发
     * @returns void
     */
    handleSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
    },

    /**
     * @description 取消选中项
     * @returns void
     */
    handleClearSelected() {
      this.selectedRowKeys = []
      this.selectionRows = []
    },

    /**
     * @description 字段名字选择操作
     * @param {String} value
     * @param {Object} item
     * @returns void
     */
    handleSelectName(value, item) {
      // 清空原先输入的值
      item.value = ''
      this.queryOptionArray.map((option, index) => {
        if (option.dataIndex === value) {
          item.columnType = option.dbType
        }
      })
    },

    /**
     * @description 初始化追加数据
     * @returns void
     */
    handleAddItemNew() {
      this.queryParam.where.push({
        sid: Math.random(),
        column: undefined,
        operator: undefined,
        value: '',
        columnType: 'string'
      })
    },

    /**
     * @description 追加操作
     * @param {Object} obj
     * @returns void
     */
    handleAddItem(obj) {
      if (this.queryParam.where.length === 0) return
      // 追加数据操作(再当前位置的下方追加)
      let indexFlag = -1
      this.queryParam.where.map((item, index) => {
        if (item.sid === obj.sid) {
          indexFlag = index
        }
      })
      // 追加数据
      this.queryParam.where.splice(indexFlag + 1, 0, {
        sid: Math.random(),
        column: undefined,
        operator: undefined,
        value: '',
        columnType: 'string'
      })
    },

    /**
     * @description 删除操作
     * @param {Object} obj
     * @returns void
     */
    handleDeleteItem(obj) {
      // 删除数据操作(当数组的数据还剩最后一条时，允许删除)
      if (this.queryParam.where.length === 1) {
        this.queryParam.where = []
        this.getTableData() // 重新进行查询
        return
      }
      const tempArr = []
      this.queryParam.where.map((item, index) => {
        if (item.sid !== obj.sid) {
          tempArr.push(item)
        }
      })
      this.queryParam.where = tempArr
    },

    /**
     * @description 查询操作
     * @returns void
     */
    handleSearchQuery() {
      this.getTableData(1)
    },

    /**
     * @description 重置操作
     * @returns void
     */
    handleSearchReset() {
      // 重置查询条件
      this.queryParam.where = []
      this.handleAddItemNew()
      this.getTableData(1) // 重置查询结果
    },

    /**
     * @description 添加操作
     * @returns void
     */
    handleAdd() {
      this.operateFlag = true
      this.operateAction = 'Add'
      this.editRecord = {}
    },

    /**
     * @description 编辑操作
     * @param {Object} record
     * @returns void
     */
    handleEdit(record) {
      // 编辑操作
      this.operateFlag = true
      this.operateAction = 'Update'
      this.editRecord = record
    },

    /**
     * @description 批量删除操作
     * @returns void
     */
    handleBatchDelete() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条数据！')
      } else {
        let ids = ''
        for (let a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        this.$confirm({
          title: '确认删除',
          content: '数据删除后无法恢复，确定删除选中数据?',
          onOk: () => {
            const params = Object.assign({ ids: ids }, this.baseParam)
            getAction(this.url.deleteBatch, params)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success(res.message)
                  this.getTableData()
                  this.handleClearSelected()
                } else {
                  this.$message.warning(res.message)
                }
              })
              .catch(() => {
                this.$message.warning(res.message)
              })
          }
        })
      }
    },

    /**
     * @description 单条删除
     * @param {String} id
     * @returns void
     */
    handleDelete(id) {
      const params = Object.assign({ id: id }, this.baseParam)
      getAction(this.url.delete, params)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success(res.message)
            this.getTableData()
          } else {
            this.$message.warning(res.message)
          }
        })
        .catch(() => {
          this.$message.warning(res.message)
        })
    },

    /**
     * @description 关闭modal框
     * @returns void
     */
    handleCancel() {
      this.visible = false
      // 清空数据
      this.baseParam = {}
      this.queryOptionArray = []
      this.queryParam = {
        connType: 'And',
        pageNo: 0,
        pageSize: 0,
        where: []
      }
      this.columns = []
      this.dataSource = []
      this.selectedRowKeys = []
      this.selectionRows = []
      this.$emit('ok')
    }
  }
}
</script>

<style lang="less" scoped>
.data-edit-container {
  width: 100%;
  display: flex;
  flex-flow: column;
  .data-top {
    width: 100%;
    height: 187px;
    border-radius: 5px;
    border: 1px solid #d9d9d9;
    overflow: hidden;
    .top-filter {
      width: 100%;
      height: 36px;
      padding-left: 17px;
      margin-top: 10px;
    }
    .top-content {
      // width: 800px;
      height: 140px;
      overflow-x: hidden;
      overflow-y: auto;
      padding-top: 10px;
      padding-left: 16px;
      padding-bottom: 3px;
      .item-select {
        // width: 720px;
        height: 33px;
        margin-bottom: 10px;
      }
    }
  }
  .data-content {
    width: 100%;
    flex: 1;
    overflow: hidden;
    .btn-group {
      width: 100%;
      height: 36px;
      margin-top: 10px;
      margin-bottom: 8px;
    }
    .table-content {
      width: 100%;
    }
  }
}
::v-deep .ant-modal-body {
  padding: 10px 20px;
}
::v-deep .ant-table-header-column {
  display: inline-block;
  max-width: 100%;
  vertical-align: top;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
</style>
