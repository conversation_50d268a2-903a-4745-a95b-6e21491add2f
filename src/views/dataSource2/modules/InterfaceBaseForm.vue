<template>
  <a-form :form="form">
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="接口服务名称">
      <a-input
        v-decorator="['apiName', validatorRules.apiName]"
        placeholder="请输入接口服务名称"
        :disabled="disableSubmit"
      />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="所属部门">
      <a-tree-select
        v-model="model.parentId"
        v-decorator="['sysOrgCode', validatorRules.sysOrgCode]"
        style="width: 100%"
        :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
        :disabled="disableSubmit"
        :treeData="treeData"
        placeholder="无"
      >
      </a-tree-select>
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="负责人">
      <a-input
        v-decorator="['picPerson', validatorRules.picPerson]"
        placeholder="请输入负责人"
        :disabled="disableSubmit"
      />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="负责人联系方式">
      <a-input
        v-decorator="['picPhone', validatorRules.picPhone]"
        placeholder="请输入负责人联系方式"
        :disabled="disableSubmit"
      />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="所属业务系统">
      <a-input
        v-decorator="['clientName', validatorRules.clientName]"
        placeholder="请输入所属业务系统"
        :disabled="disableSubmit"
      />
    </a-form-item>
    <!-- <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="负责人联系方式">
      <a-input placeholder="负责人联系方式" v-decorator="['name', validatorRules.name]" :disabled="disableSubmit" />
    </a-form-item> -->
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="表名">
      <a-input
        v-decorator="['tableName', validatorRules.tableName]"
        placeholder="请输入表名"
        disabled
      />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="cron表达式">
      <!--                    <a-input placeholder="请输入cron表达式" v-decorator="['cronExpression', {'initialValue':'0/1 * * * * ?',rules: [{ required: true, message: '请输入任务类名!' }]}]" />-->
      <!--                    <a target="_blank" href="http://cron.qqe2.com/">-->
      <!--                      <a-icon type="share-alt" />-->
      <!--                      在线cron表达式生成-->
      <!--                    </a>-->
      <!--          <j-cron ref="innerVueCron" v-decorator="['cronExpression', {'initialValue':'0/1 * * * * ?',rules: [{ required: true, message: '请输入cron表达式!' }]}]"  @change="setCorn"></j-cron>-->
      <j-cron
        ref="innerVueCron"
        v-decorator="['cronExpression', { initialValue: '* * * * * ? *' }]"
        :disabled="true"
        @change="setCorn"
      ></j-cron>
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="接口描述">
      <a-textarea
        v-decorator="['apiDescription', {}]"
        placeholder="请输入接口描述"
        :disabled="disableSubmit"
        :rows="4"
      />
    </a-form-item>
  </a-form>
</template>

<script>
import pick from 'lodash.pick'
import { queryDepartTreeList } from '@/api/api'
import JCron from '@/components/jeecg/JCron'
export default {
  name: 'InterfaceBaseForm',
  components: { JCron },
  props: {
    disableSubmit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      treeData: [],
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        apiName: { rules: [{ required: true, message: '请输入接口服务名称!' }] },
        sysOrgCode: { rules: [{ required: true, message: '请选择所属部门!' }] },
        picPerson: { rules: [{ required: true, message: '负责人联系方式!' }] },
        picPhone: { rules: [{ required: true, message: '负责人联系方式!' }] },
        tableName: { rules: [{ required: false, message: '请输入表名' }] }
      },
      url: {
        add: '/sys/dataSource/add',
        edit: '/sys/dataSource/edit'
      }
    }
  },
  created() {
    this.loadTree()
  },
  methods: {
    setCorn(data) {
      console.log('data)', data)
      this.$nextTick(() => {
        this.model.cronExpression = data
      })

      // console.log(Object.keys(data).length==0);
      if (Object.keys(data).length == 0) {
        this.$message.warning('请输入cron表达式!')
      }
    },
    loadTree() {
      var that = this
      that.treeData = []
      queryDepartTreeList().then((res) => {
        if (res.success) {
          // 部门全选后，再添加部门，选中数量增多
          this.allTreeKeys = []
          for (let i = 0; i < res.result.length; i++) {
            const temp = res.result[i]
            that.treeData.push(temp)
            // console.log(temp.id)
          }
          this.loading = false
        }
      })
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'id',
            'apiName',
            'sysOrgCode',
            'picPerson',
            'picPhone',
            'clientName',
            'apiDescription',
            'cronExpression',
            'tableName'
          )
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    getParams() {},
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped>
.atn-testbtn {
  color: #2f77f2 !important;
  background: #ffffff !important;
  box-shadow: 0px 1px 3px 0px rgba(4, 0, 0, 0.08) !important;
  border: 1px solid #2f77f2 !important;
  border-radius: 2px !important;
  padding: 0 10px !important;
  height: 34px !important;
  margin: 0 8px 8px 0;
}
</style>
