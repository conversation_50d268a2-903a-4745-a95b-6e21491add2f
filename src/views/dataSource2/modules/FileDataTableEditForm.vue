<template>
  <a-form :form="form">
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="源文件">
      <a-input
        v-model="fileName"
        placeholder="请输入字段名行"
        style="width: 100%"
        :disabled="true"
      />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="数据源">
      <a-select
        v-model="dataSourceId"
        :filter-option="filterOption"
        show-search
        allowClear
        :getPopupContainer="(trigger) => trigger.parentNode"
        placeholder="请选择数据源"
        @change="dataSourceChange"
      >
        <a-select-option v-for="item in dataSourceList" :key="item.code" :value="item.code">{{
          item.name
        }}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="数据表">
      <a-select
        v-decorator="['tableId', validatorRules.tableId]"
        :filter-option="filterOption"
        show-search
        allowClear
        placeholder="请选择数据表"
        @change="changeTable"
      >
        <a-select-option v-for="item in tableArr" :key="'shujubiao' + item.id" :value="item.id">{{
          item.tableName
        }}</a-select-option>
      </a-select>
    </a-form-item>
    <FileDataTableForm ref="fileDataTableForm" />
  </a-form>
</template>

<script>
import FileDataTableForm from './FileDataTableFormForEdit'
import { postAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
export default {
  name: 'FileDataTableEditForm',
  components: { FileDataTableForm },
  props: {},
  data() {
    return {
      tableName: null,
      selectedTable: null,
      tableArr: [],
      dataSourceList: [],
      dataSourceId: null,
      dataSource: [],
      fileName: null,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        tableId: {
          rules: [
            {
              required: true,
              message: '请输入表名!'
            }
          ]
        }
      }
    }
  },
  created() {},
  methods: {
    // 切换数据源
    dataSourceChange(value, options) {
      this.form.setFieldsValue({ tableId: null })
      getAction(`/data/scCockpitDataSource/list?tableSourceCode=${value}`).then((res) => {
        this.tableArr = res.result.records
      })
    },
    changeTable(value) {
      this.tableName = this.tableArr.find((item) => item.id == value).tableName
      this.$refs.fileDataTableForm.columns[2].options = []
      getAction('/data/scCockpitDataSource/queryById', { id: value }).then((res) => {
        if (res.success) {
          var options = []
          res.result.dataColumnList.forEach((item) => {
            if (item.columnName != 'id') {
              item.title = item.columnName
              item.value = item.columnName
              options.push(item)
            }
          })
          this.$refs.fileDataTableForm.dataSource = [
            this.$refs.fileDataTableForm.dataSource.filter((item) => item.columnName === 'id')[0],
            ...options
          ]
        }
      })
    },
    getDataSourceList() {
      getAction('/sys/dataSource/list', { pageNo: 1, pageSize: 10000 }).then((res) => {
        if (res.success) {
          if (res.result.records != null) {
            this.dataSourceList = res.result.records.filter((item) => item.dbStatus == '1')
          } else {
            this.dataSourceList = []
          }
        } else {
          this.dataSourceList = []
        }
      })
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    getTableData() {
      return this.$refs.fileDataTableForm.getValues()
    },
    initValues(data) {
      this.getDataSourceList()
      this.dataSource = data
      this.$refs.fileDataTableForm.initValues(data)
      // return this.$refs.editableTable.getValuesSync({ validate: true })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped></style>
