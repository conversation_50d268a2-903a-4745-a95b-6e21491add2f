<template>
  <div>
    <a-button class="editable-add-btn" type="primary" style="margin-bottom: 8px" @click="handleAdd" :disabled="disableSubmit">新增</a-button>
    <!-- <a-button class="editable-add-btn" style="margin-bottom: 8px" @click="handleBatchDelete">删除</a-button> -->

    <!-- :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" -->
    <a-table :dataSource="dataSource" :columns="columns"  >
      <template slot="typeCustomRender" slot-scope="text, record">
        <!-- {{ record.type }} -->
        {{ formatColumnDataType(record.type) }}
      </template>
      <template slot="isRequire" slot-scope="text, record">
        {{ record.isRequire == 1 ? '是' : '否' }}
      </template>
      <span slot="action" slot-scope="text, record">
        <a @click="handleEdit(record)" :disabled="disableSubmit">编辑</a>
        <a-divider type="vertical"/>
        <a @click="handleDelete(record)" :disabled="disableSubmit">删除</a>
      </span>
    </a-table>
    <a-modal :visible="visible" :title="title" @ok="handleOk" @cancel="handleCancel">
      <a-form :form="form">
        <a-form-item label="参数名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入参数名称" v-decorator="['name', validatorRules.name]" />
          <a-input v-decorator="['timeCode']" v-show="false" />
        </a-form-item>
        <a-form-item label="参数编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入参数名称" v-decorator="['code', validatorRules.code]" />
        </a-form-item>
        <a-form-item label="参数类型" :labelCol="labelCol" :wrapperCol="wrapperCol" >
          <j-dict-select-tag v-decorator="['type', validatorRules.type]" placeholder="请选择参数类型" :trigger-change="true" dict-code="api_param_type" />
        </a-form-item>
        <a-form-item label="参数默认值" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['defaultValue', validatorRules.defaultValue]" placeholder="请输入参数默认值" />
        </a-form-item>
        <a-form-item label="是否必填" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-switch :options="[1, 0]"  v-decorator="['isRequire', validatorRules.isRequire]"  />
        </a-form-item>
        <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-textarea placeholder="请输入描述" v-decorator="['description', validatorRules.description]" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ajaxGetDictItems } from '@/api/api'
import pick from 'lodash.pick'

export default {
  name: 'InterfaceParams',
  props: {
    disableSubmit: {
      type: Boolean,
      default: false
    },
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      addFlag: true,
      selectedRowKeys: [],
      selectedRows: [],
      columnDataTypes: [],
      validatorRules: {
        name: { rules: [{ required: true, message: '请输入参数名称!' }] },
        code: { rules: [{ required: true, message: '请输入参数编码!' }] },
        type: { rules: [{ required: true, message: '请选择参数类型!' }] },
        defaultValue: { rules: [] },
        isRequire: { rules: [{ required: true, message: '请选择是否必填!' }] }
      },
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      title: '新增',
      visible: false,
      columns: [
        {
          title: '参数名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '参数编码',
          align: 'center',
          dataIndex: 'code'
        },
        {
          title: '参数类型',
          align: 'center',
          dataIndex: 'type',
          scopedSlots: { customRender: 'typeCustomRender' }
        },
        {
          title: '参数默认值',
          align: 'center',
          dataIndex: 'defaultValue'
        },
        {
          title: '是否必填',
          align: 'center',
          dataIndex: 'isRequire',
          scopedSlots: { customRender: 'isRequire' }
        },
        {
          title: '描述',
          align: 'center',
          dataIndex: 'description'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 130,
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },
  created() {
    this.initColumnDataType()
  },
  methods: {
    onSelectChange(keys, rows) {
      this.selectedRowKeys = keys
      this.selectedRows = rows
    },
    handleDelete(val) {
      this.$emit('delete', [val.code])
    },
    handleBatchDelete() {
      console.log(this.selectedRows)
      // this.$emit('delete', [val.name])
    },
    formatColumnDataType(val) {
      var columnDataType = this.columnDataTypes.find(item => item.value == val)
      if (columnDataType) {
        return columnDataType.text
      }
      return '-'
    },
    initColumnDataType() {
      ajaxGetDictItems('api_param_type', null).then(res => {
        if (res.success) {
          //                console.log(res.result);
          this.columnDataTypes = res.result
        }
      })
      //
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          if (that.addFlag) {
            that.$emit('add', values)
          } else {
            that.$emit('edit', values)
          }
        }
      })
    },
    handleAdd() {
      this.addFlag = true
      this.title = '新增'
      this.form.resetFields()
      this.handleEdit({ isRequire: 1 }, true)
      this.visible = true
    },
    handleEdit(record, addFlag) {
      if (addFlag) {
        this.addFlag = addFlag
      } else {
        this.addFlag = false
      }

      this.title = '编辑'
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'timeCode', 'name', 'code', 'type', 'defaultValue', 'isRequire', 'description'))
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      this.form.resetFields()
    }
  }
}
</script>

<style>

</style>
