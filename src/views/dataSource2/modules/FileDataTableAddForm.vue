<template>
  <a-form :form="form">
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="源文件">
      <a-input v-model="fileName" placeholder="请输入字段名行" style="width: 100%" :disabled="true" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="目标表名">
      <a-input v-decorator="['tableName', validatorRules.tableName]" placeholder="请输入表名" style="width: 100%" />
    </a-form-item>
      <FileDataTableForm ref="fileDataTableForm" />
  </a-form>
</template>

<script>
import FileDataTableForm from './FileDataTableFormForAdd'
import pick from 'lodash.pick'
export default {
  name: 'FileDataTableAddForm',
  components: { FileDataTableForm },
  props: {

  },
  data() {
    return {
      dataSource: [],
      fileName: null,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        tableName: {
          rules: [{
            required: true, message: '请输入表名!'
          }, {
            validator: this.validateTableName
          }]
        }
      }
    }
  },
  created() {
  },
  methods: {
    getTableData() {
      return this.$refs.fileDataTableForm.getValues()
    },
    initValues(data) {
      this.dataSource = data
      this.$refs.fileDataTableForm.initValues(data)
      // return this.$refs.editableTable.getValuesSync({ validate: true })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped>
</style>
