<template>
  <j-modal
    :title="title"
    :switchFullscreen="true"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    cancelText="关闭"
    @ok="handleOk"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-tabs v-activeKey="activeKey">
        <a-tab-pane key="1" tab="基本信息">
          <InterfaceBaseForm ref="interfaceBaseForm" :disableSubmit="disableSubmit"/>
        </a-tab-pane>
        <a-tab-pane key="2" tab="接口信息" force-render style="max-height: 800px;overflow-y:auto">
          <InterfaceForm ref="interfaceForm" :disableSubmit="disableSubmit" @viewResultData="viewResultData" @formatJson="formatJson" />
        </a-tab-pane>
      </a-tabs>
    </a-spin>
    <a-modal v-model="jsonVisible" title="数据预览">
      <template slot="footer">
        <a-button @click="handleCloseJson">关闭</a-button>
      </template>
      <JsonEditor v-model="resultJson" :readOnly="true" />
    </a-modal>
  </j-modal>
</template>

<script>
import { httpAction, postAction, getAction } from '@/api/manage'
import InterfaceBaseForm from './InterfaceBaseForm'
import InterfaceForm from './InterfaceForm'
import JsonEditor from '@/components/JsonEditor.vue'
export default {
  name: 'SysDataSourceInterfaceModal',
  components: {
    InterfaceBaseForm,
    InterfaceForm,
    JsonEditor
  },
  data() {
    return {
      resultJson: null,
      jsonVisible: false,
      interfaceId: null,
      activeKey: '1',
      title: '操作',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      url: {
        add: '/sys/dataSource/add',
        edit: '/sys/dataSource/edit'
      }
    }
  },
  created() {
  },
  methods: {
    handleCloseJson() {
      this.jsonVisible = false
    },
    viewResultData() {
      this.checkForm1(true)
    },
    formatJson() {
      this.checkForm1(false, true)
    },
    dataPreviewByJson(params) {
      postAction('/dataSource/scApi/dataPreviewByJson', params).then(res => {
        if (res.success) {
          this.jsonVisible = true
          this.resultJson = JSON.stringify(res.result.apiData)
        } else {
          this.$message.warning(res.message)
        }
        this.confirmLoading = false
      })
    },
    add() {
      this.interfaceId = null
      this.visible = true
      this.$nextTick(() => {
        this.$refs.interfaceBaseForm.add()
        this.$refs.interfaceForm.add()
      })
    },
    edit(record) {
      this.interfaceId = record.id
      this.visible = true
      this.$nextTick(() => {
        getAction('/dataSource/scApi/queryById', { id: record.id }).then(res => {
          if (res.success) {
            this.$refs.interfaceBaseForm.edit(res.result.apiInfo)
            this.$refs.interfaceForm.edit(res.result)
          }
        })
      })
    },
    close() {
      this.$refs.interfaceBaseForm.close()
      this.$refs.interfaceForm.close()
      this.$emit('close')
      this.visible = false
      this.disableSubmit = false
    },
    handleOk() {
      // 触发表单验证
      this.checkForm1()
    },
    checkForm1(isDataPreviewByJson, isFormatJson) {
      // 验证基本信息
      this.$refs.interfaceBaseForm.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          this.checkForm2(values, isDataPreviewByJson, isFormatJson)
        } else {
          this.confirmLoading = false
        }
      })
    },
    checkForm2(formData, isDataPreviewByJson, isFormatJson) {
      this.$refs.interfaceForm.form.validateFields((err, values) => {
        if (!err) {
          Object.assign(formData, values)
          if (this.$refs.interfaceForm.fileList != null && this.$refs.interfaceForm.fileList.length > 0) {
            formData.interfaceDocumentationPath = this.$refs.interfaceForm.fileList
          }
          formData.bodyType = this.$refs.interfaceForm.bodyType

          var params = {
            apiInfo: formData,
            scApiReqList: this.$refs.interfaceForm.getAllParams()
          }

          // 数据预览
          if (isDataPreviewByJson) {
            this.dataPreviewByJson(params)
            return
          }

          // 生成
          if (isFormatJson) {
            postAction('/dataSource/scApi/dataPreviewByJson', params).then(res => {
              if (res.success) {
                this.$refs.interfaceForm.formatJsonResult(res.result.apiData)
              }
              this.confirmLoading = false
            })
            return
          }

          var apiRepsTableData = this.$refs.interfaceForm.getApiRepsTableData()

          console.log('apiRepsTableData.values----------------------------', apiRepsTableData.values)

          if (apiRepsTableData.values == null || apiRepsTableData.values.length == 0) {
            this.$message.warning('返回参数对应表必填')
            this.confirmLoading = false
            return
          }

          if (apiRepsTableData.error == 0) {
            params.scApiRepList = apiRepsTableData.values
          } else {
            this.confirmLoading = false
            return
          }

          if (this.interfaceId) {
            params.apiInfo.id = this.interfaceId
            httpAction('/dataSource/scApi/edit', params, 'POST').then(res => {
              if (res.success) {
                // this.$message.success(res.message)
                this.close()
              }
              this.confirmLoading = false
            })
          } else {
            postAction('/dataSource/scApi/add', params).then(res => {
              if (res.success) {
                // this.$message.success(res.message)
                this.close()
              }
              this.confirmLoading = false
            })
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped>
.atn-testbtn {
    color: #2F77F2 !important;
    background: #FFFFFF !important;
    box-shadow: 0px 1px 3px 0px rgba(4, 0, 0, 0.08) !important;
    border: 1px solid #2F77F2 !important;
    border-radius: 2px !important;
    padding: 0 10px !important;
    height: 34px !important;
    margin: 0 8px 8px 0;
}
</style>
