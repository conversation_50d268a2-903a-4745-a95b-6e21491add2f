<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top: 5%; height: 95%"
    @cancel="handleCancel"
  >
    <div>
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              label="数据源"
              prop="tableDataSource"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-model="form.tableDataSource"
                placeholder="请选择数据源"
                @change="getTableList"
              >
                <a-select-option
                  v-for="item in tableDataSourceList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.code"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-card>
          <a-row style="margin-bottom: 20px">
            <a-col
              style="margin-right: 20px"
              :span="10"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-model="queryTableName"
                placeholder="请输入数据表名称"
                @keyup.enter.native="getTableList"
              ></a-input>
            </a-col>
            <a-col :span="4">
              <a-button type="primary" icon="search" @click="getTableList">查询</a-button>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-table
                ref="table"
                size="middle"
                :scroll="{ x: true }"
                bordered
                rowKey="id"
                :columns="columns"
                :dataSource="tableList"
                :pagination="ipagination"
                :loading="tableLoading"
                :customRow="rowClick"
                class="j-table-force-nowrap"
                @change="handleTableChange"
              ></a-table>
            </a-col>
          </a-row>
        </a-card>
        <a-col style="margin-top: 20px" :span="24">
          <a-form-model-item
            label="数据表名称"
            prop="tableName"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-model="form.tableName" placeholder="请输入数据表名称"></a-input>
          </a-form-model-item>
          <a-form-model-item
            label="任务状态"
            prop="isScheduled"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-radio-group v-model="form.isScheduled" button-style="solid">
              <a-radio-button :value="1"> 正常 </a-radio-button>
              <a-radio-button :value="0"> 停止 </a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-item
            prop="cronExpression"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="cron表达式"
          >
            <j-cron ref="cronExpression" v-model="form.cronExpression"></j-cron>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item
            prop="syncType"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="导入模式"
          >
            <a-radio-group v-model="form.syncType">
              <a-radio :value="0"> 追加：添加记录到目标表 </a-radio>
              <a-radio :value="1"> 复制：删除目标全部记录，并从源重新导入 </a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-form-model>
    </div>
    <div slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
      <a-button type="primary" :loading="confirmLoading1" @click="handleSubmit(1)"
        >立即执行</a-button
      >
      <a-button type="primary" :loading="confirmLoading2" @click="handleSubmit"
        >保存并安排任务</a-button
      >
    </div>
  </a-modal>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import JCron from '@/components/jeecg/JCron'
import pick from 'lodash.pick'
export default {
  name: 'InterfaceSyncModal',
  components: { JCron },
  data() {
    return {
      title: '同步配置',
      visible: false,
      confirmLoading1: false,
      confirmLoading2: false,
      tableLoading: false,
      modal: {},
      queryTableName: '',
      form: {
        apiId: '',
        tableName: '',
        tableDataSource: '',
        scheduledJobClass: '',
        cronExpression: '',
        quartJobId: '',
        isScheduled: 0,
        syncType: 0
      },
      tableDataSourceList: [],
      tableList: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      rules: {
        tableDataSource: [{ required: true, message: '请选择数据源', trigger: 'blur' }],
        tableName: [{ required: true, message: '请选择数据表', trigger: 'blur' }],
        cronExpression: [{ required: true, message: '请输入cron表达式', trigger: 'blur' }]
      },
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '表名',
          align: 'center',
          dataIndex: 'tableName'
        },
        {
          title: '备注',
          align: 'center',
          dataIndex: 'tableComment'
        }
      ],
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      url: {
        listDataSource: '/sys/dataSource/list',
        listTable: '/data/scCockpitDataSource/list',
        add: '/dataSource/scApi/addApiJob',
        edit: '/dataSource/scApi/editApiJob',
        execute: '/dataSource/scApi/execute'
      }
    }
  },
  methods: {
    add(record) {
      this.edit(record)
    },
    edit(record) {
      this.visible = true
      this.form = {
        apiId: record.id,
        tableName: '',
        tableDataSource: '',
        scheduledJobClass: '',
        cronExpression: '',
        quartJobId: '',
        isScheduled: 0,
        syncType: 0
      }
      this.tableList = []
      this.modal = Object.assign({}, record)
      if (record.tableName) {
        this.form = Object.assign(
          this.form,
          pick(
            record,
            'tableDataSource',
            'scheduledJobClass',
            'cronExpression',
            'quartJobId',
            'tableName'
          )
        )
        this.form.isScheduled = record.isScheduled || 0
        this.form.syncType = record.syncType || 0
      }
      this.initData()
    },
    async initData() {
      const tableDataSourceListRes = await getAction(this.url.listDataSource)
      if (tableDataSourceListRes.success) {
        this.tableDataSourceList = tableDataSourceListRes.result.records
      } else {
        this.$message.warning('数据源列表获取失败')
      }
      this.getTableList()
    },
    async getTableList() {
      const param = {}
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      if (this.form.tableDataSource) {
        this.tableLoading = true
        const tableListRes = await getAction(this.url.listTable, {
          ...param,
          tableName: this.form.tableName ? '*' + this.queryTableName + '*' : '',
          tableSourceCode: this.form.tableDataSource
        })
        if (tableListRes.success) {
          this.tableList = tableListRes.result.records
          this.ipagination.total = tableListRes.result.total
        } else {
          this.$message.warning('数据表列表获取失败')
        }
        this.tableLoading = false
      }
    },
    handleTableChange(pagination) {
      this.ipagination = pagination
      this.getTableList()
    },
    rowClick(record, index) {
      return {
        on: {
          click: () => {
            this.$nextTick(() => {
              this.form.tableName = record.tableName
              this.$message.success('选中成功')
            })
          }
        }
      }
    },
    handleSubmit(executeFlag) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.cronExpression) {
            this.$message.warning('请输入正确的cron表达式')
            return
          }
          if (executeFlag === 1) {
            this.confirmLoading1 = true
          } else {
            this.confirmLoading2 = true
          }
          const formData = {
            ...pick(
              this.form,
              'apiId',
              'tableDataSource',
              'scheduledJobClass',
              'cronExpression',
              'quartJobId',
              'isScheduled',
              'tableName',
              'syncType'
            )
          }
          let url
          if (executeFlag === 1) {
            url = this.url.execute
          } else {
            url = this.modal.tableName ? this.url.edit : this.url.add
          }
          console.log('formData', formData)
          postAction(url, formData)
            .then((res) => {
              if (res.success) {
                this.visible = false
                this.$emit('ok')
                this.$message.success('操作成功')
              } else {
                this.$message.warning('操作失败', res.message)
              }
            })
            .finally(() => {
              if (executeFlag === 1) {
                this.confirmLoading1 = false
              } else {
                this.confirmLoading2 = false
              }
            })
        }
      })
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .ant-table-row {
  cursor: pointer;
}
</style>
