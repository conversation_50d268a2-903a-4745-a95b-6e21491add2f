<template>
  <j-editable-table
    ref="editableTable"
    :loading="loading"
    :columns="columns"
    :dataSource="dataSource"
    :rowNumber="true"
    :rowSelection="true"
    :actionButton="actionButton"
    :maxHeight="300"
  />
</template>

<script>
import { FormTypes } from '@/utils/JEditableTableUtil'

export default {
  name: 'ApiRepsTable',
  components: {},
  props: {
    actionButton: {
      type: Boolean,
      default: true,
      required: false
    }
  },
  data() {
    return {
      loading: false,
      dataSource: [],
      columns: [
        {
          title: '对应JSON路径', // 必填
          key: 'repRoute',
          width: '20%',
          type: FormTypes.input,
          defaultValue: '',
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        },
        {
          title: '名称', // 必填
          key: 'name',
          // width: '30%',
          type: FormTypes.input,
          defaultValue: '',
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        },
        {
          title: '编码', // 必填
          key: 'code',
          // width: '30%',
          type: FormTypes.input,
          defaultValue: '',
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        },
        {
          title: '类型', // 必填
          key: 'type',
          // width: '30%',
          type: FormTypes.input,
          defaultValue: '',
          placeholder: '请输入${title}'
          // validateRules: [{ required: true, message: '${title}不能为空' }],
        }
        // {
        //   title: '默认值', // 必填
        //   key: 'defaultValue',
        //   // width: '30%',
        //   type: FormTypes.input,
        //   options: [
        //     { title: 'normal', value: 'normal' },
        //     { title: 'unique', value: 'unique' }
        //   ],
        //   defaultValue: '',
        //   placeholder: '请选择${title}'
        //   // validateRules: [{ required: true, message: '请选择${title}' }],
        // },
        // {
        //   title: '数据格式', // 必填
        //   key: 'dataFormat',
        //   // width: '30%',
        //   type: FormTypes.input,
        //   options: [
        //     { title: 'normal', value: 'normal' },
        //     { title: 'unique', value: 'unique' }
        //   ],
        //   defaultValue: '',
        //   placeholder: '请选择${title}'
        //   // validateRules: [{ required: true, message: '请选择${title}' }],
        // },
        // {
        //   title: '单位', // 必填
        //   key: 'unit',
        //   // width: '30%',
        //   type: FormTypes.input,
        //   defaultValue: '',
        //   placeholder: '请输入${title}'
        //   // validateRules: [{ required: true, message: '${title}不能为空' }],
        // }
      ]
    }
  },
  methods: {
    getValues() {
      return this.$refs.editableTable.getValuesSync({ validate: true })
    },
    reset() {
      this.$refs.editableTable.initialize()
    },
    initValues(data) {
      this.dataSource = data
      // return this.$refs.editableTable.getValuesSync({ validate: true })
    }
  }
}
</script>

<style scoped></style>
