<template>
  <a-form :form="form">
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="记录分隔符">
      <j-dict-select-tag :trigger-change="true" v-decorator="['lineSep', validatorRules.lineSep]" placeholder="请选择记录分隔符" dict-code="line_sep" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="字段分隔符">
      <a-input v-decorator="['columnsep', validatorRules.columnsep]" placeholder="请输入字段名行" style="width: 100%" />
    </a-form-item>
	<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="文本识别符">
      <a-input v-decorator="['txtSign', validatorRules.txtSign]" placeholder="请输入文本识别符" style="width: 100%" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="字段名行">
      <a-input-number v-decorator="['nameRecord', validatorRules.nameRecord]" placeholder="请输入字段名行" style="width: 100%" :min="0" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="第一个数据行">
      <a-input-number v-decorator="['dataRecord1st', validatorRules.dataRecord1st]" placeholder="请输入第一个数据行" style="width: 100%" :min="0" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="最后一个数据行">
      <a-input-number v-decorator="['dataRecordLast', validatorRules.dataRecordLast]" placeholder="请输入最后一个数据行" style="width: 100%" :min="0" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="新建表">
      <j-switch :options="[1, 0]"  v-decorator="['createTable', validatorRules.createTable]"/>
    </a-form-item>
  </a-form>
</template>

<script>
import pick from 'lodash.pick'
export default {
  name: 'CsvDataForm',
  components: {},
  props: {

  },
  data() {
    return {
      fileList: [],
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        lineSep: { rules: [{ required: true, message: '请选择记录分隔符!' }] },
        columnsep: { rules: [{ required: true, message: '请输入字段分隔符!' }] },
        txtSign: { rules: [{ required: true, message: '请输入文本识别符!' }] },
        nameRecord: { rules: [{ required: true, message: '请输入字段名行!' }] },
        dataRecord1st: { rules: [{ required: true, message: '请输入第一个数据行!' }] },
        dataRecordLast: { rules: [] }
      }
    }
  },
  created() {
  },
  methods: {
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped>
</style>
