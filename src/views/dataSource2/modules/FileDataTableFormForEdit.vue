<template>
  <j-editable-table
    ref="editableTable"
    :loading="loading"
    :columns="columns"
    :dataSource="dataSource"
    :rowNumber="true"
    :rowSelection="false"
    :actionButton="actionButton"
    :disabledRows="{ columnName: 'id' }"
    :maxHeight="300"
  />
</template>

<script>
import { FormTypes } from '@/utils/JEditableTableUtil'
const MySQLKeywords = [
  'ADD',
  'ALL',
  'ALTER',
  'ANALYZE',
  'AND',
  'AS',
  'ASC',
  'ASENSITIVE',
  'BEFORE',
  'BETWEEN',
  'BIGINT',
  'BINARY',
  'BLOB',
  'BOTH',
  'BY',
  'CALL',
  'CASCADE',
  'CASE',
  'CHANGE',
  'CHAR',
  'CHARACTER',
  'CHECK',
  'COLLATE',
  'COLUMN',
  'CONDITION',
  'CONNECTION',
  'CONSTRAINT',
  'CONTINUE',
  'CONVERT',
  'CREATE',
  'CROSS',
  'CURRENT_DATE',
  'CURRENT_TIME',
  'CURRENT_TIMESTAMP',
  'CURRENT_USER',
  'CURSOR',
  'DATABASE',
  'DATABASES',
  'DAY_HOUR',
  'DAY_MICROSECOND',
  'DAY_MINUTE',
  'DAY_SECOND',
  'DEC',
  'DECIMAL',
  'DECLARE',
  'DEFAULT',
  'DELAYED',
  'DELETE',
  'DESC',
  'DESCRIBE',
  'DETERMINISTIC',
  'DISTINCT',
  'DISTINCTROW',
  'DIV',
  'DOUBLE',
  'DROP',
  'DUAL',
  'EACH',
  'ELSE',
  'ELSEIF',
  'ENCLOSED',
  'ESCAPED',
  'EXISTS',
  'EXIT',
  'EXPLAIN',
  'FALSE',
  'FETCH',
  'FLOAT',
  'FLOAT4',
  'FLOAT8',
  'FOR',
  'FORCE',
  'FOREIGN',
  'FROM',
  'FULLTEXT',
  'GOTO',
  'GRANT',
  'GROUP',
  'HAVING',
  'HIGH_PRIORITY',
  'HOUR_MICROSECOND',
  'HOUR_MINUTE',
  'HOUR_SECOND',
  'IF',
  'IGNORE',
  'IN',
  'INDEX',
  'INFILE',
  'INNER',
  'INOUT',
  'INSENSITIVE',
  'INSERT',
  'INT',
  'INT1',
  'INT2',
  'INT3',
  'INT4',
  'INT8',
  'INTEGER',
  'INTERVAL',
  'INTO',
  'IS',
  'ITERATE',
  'JOIN',
  'KEY',
  'KEYS',
  'KILL',
  'LABEL',
  'LEADING',
  'LEAVE',
  'LEFT',
  'LIKE',
  'LIMIT',
  'LINEAR',
  'LINES',
  'LOAD',
  'LOCALTIME',
  'LOCALTIMESTAMP',
  'LOCK',
  'LONG',
  'LONGBLOB',
  'LONGTEXT',
  'LOOP',
  'LOW_PRIORITY',
  'MATCH',
  'MEDIUMBLOB',
  'MEDIUMINT',
  'MEDIUMTEXT',
  'MIDDLEINT',
  'MINUTE_MICROSECOND',
  'MINUTE_SECOND',
  'MOD',
  'MODIFIES',
  'NATURAL',
  'NOT',
  'NO_WRITE_TO_BINLOG',
  'NULL',
  'NUMERIC',
  'ON',
  'OPTIMIZE',
  'OPTION',
  'OPTIONALLY',
  'OR',
  'ORDER',
  'OUT',
  'OUTER',
  'OUTFILE',
  'PRECISION',
  'PRIMARY',
  'PROCEDURE',
  'PURGE',
  'RAID0',
  'RANGE',
  'READ',
  'READS',
  'REAL',
  'REFERENCES',
  'REGEXP',
  'RELEASE',
  'RENAME',
  'REPEAT',
  'REPLACE',
  'REQUIRE',
  'RESTRICT',
  'RETURN',
  'REVOKE',
  'RIGHT',
  'RLIKE',
  'SCHEMA',
  'SCHEMAS',
  'SECOND_MICROSECOND',
  'SELECT',
  'SENSITIVE',
  'SEPARATOR',
  'SET',
  'SHOW',
  'SMALLINT',
  'SPATIAL',
  'SPECIFIC',
  'SQL',
  'SQLEXCEPTION',
  'SQLSTATE',
  'SQLWARNING',
  'SQL_BIG_RESULT',
  'SQL_CALC_FOUND_ROWS',
  'SQL_SMALL_RESULT',
  'SSL',
  'STARTING',
  'STRAIGHT_JOIN',
  'TABLE',
  'TERMINATED',
  'THEN',
  'TINYBLOB',
  'TINYINT',
  'TINYTEXT',
  'TO',
  'TRAILING',
  'TRIGGER',
  'TRUE',
  'UNDO',
  'UNION',
  'UNIQUE',
  'UNLOCK',
  'UNSIGNED',
  'UPDATE',
  'USAGE',
  'USE',
  'USING',
  'UTC_DATE',
  'UTC_TIME',
  'UTC_TIMESTAMP',
  'VALUES',
  'VARBINARY',
  'VARCHAR',
  'VARCHARACTER',
  'VARYING',
  'WHEN',
  'WHERE',
  'WHILE',
  'WITH',
  'WRITE',
  'X509',
  'XOR',
  'YEAR_MONTH',
  'ZEROFILL'
]
export default {
  name: 'ApiRepsTable',
  components: {},
  props: {
    actionButton: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      loading: false,
      dataSource: [],
      columns: [
        {
          title: '', // 必填
          key: 'fieldIndex',
          width: '190px',
          type: FormTypes.hidden,
          disabled: true
        },
        {
          title: '源字段', // 必填
          key: 'fieldName',
          width: '190px',
          type: FormTypes.input,
          disabled: true
        },
        {
          title: '字段名称',
          key: 'columnName',
          width: '190px',
          type: FormTypes.input,
          // options: [],
          defaultValue: null
          // placeholder: '请输入${title}'
        },
        {
          title: '字段备注', // 必填
          key: 'columnComment',
          width: '220px',
          type: FormTypes.input,
          disabled: true,
          defaultValue: '',
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        },
        {
          title: '字段长度', // 必填
          key: 'dbLength',
          width: '125px',
          type: FormTypes.inputNumber,
          disabled: true,
          defaultValue: 32,
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        },
        {
          title: '小数点', // 必填
          key: 'dbPointLength',
          width: '125px',
          type: FormTypes.inputNumber,
          disabled: true,
          defaultValue: 0,
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        },
        {
          title: '默认值',
          key: 'dbDefaultVal',
          width: '140px',
          type: FormTypes.input,
          disabled: true,
          defaultValue: ''
        },
        {
          title: '字段类型', // 下拉框，默认String
          key: 'dbType',
          width: '140px',
          type: FormTypes.select,
          disabled: true,
          options: [
            { title: 'String', value: 'string' },
            { title: 'Integer', value: 'int' },
            { title: 'Double', value: 'double' },
            { title: 'Date', value: 'Date' },
            { title: 'BigDecimal', value: 'BigDecimal' },
            { title: 'Text', value: 'Text' },
            { title: 'Blob', value: 'Blob' }
          ],
          defaultValue: 'string',
          placeholder: '请选择${title}',
          validateRules: [{ required: true, message: '请选择${title}' }]
        },
        {
          title: '主键',
          key: 'dbIsKey',
          width: '100px',
          type: FormTypes.checkbox,
          customValue: ['1', '0'],
          disabled: true,
          defaultChecked: false
        },
        {
          title: '允许空值',
          key: 'dbIsNull',
          width: '100px',
          type: FormTypes.checkbox,
          customValue: ['1', '0'],
          disabled: true,
          defaultChecked: true
        }
        // {
        //   title: '排序',
        //   key: 'sort',
        //   isOrder: true, //  是否是排序字段，如果是排序字段，那么新增的默认值永远是最大值+1
        //   type: FormTypes.hidden,
        //   disabled: true,
        //   defaultValue: 0
        // }
      ]
    }
  },
  watch: {
    dataSource: {
      handler(dataSource) {
        for (let item of dataSource) {
          item.fieldName = item.columnName
        }
      }
    }
  },
  methods: {
    handleChange({ row, column, value }) {
      var indexTemp = this.dataSource.findIndex((item) => item.fieldIndex == row.fieldIndex)

      var objTemp = column.options.find((item) => item.columnName == value)
      objTemp.fieldName = row.fieldName
      objTemp.fieldIndex = row.fieldIndex
      this.dataSource.splice(indexTemp, 1, objTemp)
    },
    getValues() {
      return this.$refs.editableTable.getValuesSync({ validate: true })
    },
    reset() {
      this.$refs.editableTable.initialize()
    },
    initValues(data) {
      console.log('data-----------------------------------', data)
      this.dataSource = data
      // return this.$refs.editableTable.getValuesSync({ validate: true })
    }
  }
}
</script>

<style scoped></style>
