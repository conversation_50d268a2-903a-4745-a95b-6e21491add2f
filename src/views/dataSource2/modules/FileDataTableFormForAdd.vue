<template>
  <j-editable-table
    ref="editableTable"
    :loading="loading"
    :columns="columns"
    :dataSource="dataSource"
    :rowNumber="true"
    :rowSelection="false"
    :actionButton="actionButton"
    :disabledRows="{ columnName: 'id' }"
    :maxHeight="300"
  />
</template>

<script>
import { FormTypes } from '@/utils/JEditableTableUtil'
const MySQLKeywords = [
  'ADD',
  'ALL',
  'ALTER',
  'ANALYZE',
  'AND',
  'AS',
  'ASC',
  'ASENSITIVE',
  'BEFORE',
  'BETWEEN',
  'BIGINT',
  'BINARY',
  'BLOB',
  'BOTH',
  'BY',
  'CALL',
  'CASCADE',
  'CASE',
  'CHANGE',
  'CHAR',
  'CHARACTER',
  'CHECK',
  'COLLATE',
  'COLUMN',
  'CONDITION',
  'CONNECTION',
  'CONSTRAINT',
  'CONTINUE',
  'CONVERT',
  'CREATE',
  'CROSS',
  'CURRENT_DATE',
  'CURRENT_TIME',
  'CURRENT_TIMESTAMP',
  'CURRENT_USER',
  'CURSOR',
  'DATABASE',
  'DATABASES',
  'DAY_HOUR',
  'DAY_MICROSECOND',
  'DAY_MINUTE',
  'DAY_SECOND',
  'DEC',
  'DECIMAL',
  'DECLARE',
  'DEFAULT',
  'DELAYED',
  'DELETE',
  'DESC',
  'DESCRIBE',
  'DETERMINISTIC',
  'DISTINCT',
  'DISTINCTROW',
  'DIV',
  'DOUBLE',
  'DROP',
  'DUAL',
  'EACH',
  'ELSE',
  'ELSEIF',
  'ENCLOSED',
  'ESCAPED',
  'EXISTS',
  'EXIT',
  'EXPLAIN',
  'FALSE',
  'FETCH',
  'FLOAT',
  'FLOAT4',
  'FLOAT8',
  'FOR',
  'FORCE',
  'FOREIGN',
  'FROM',
  'FULLTEXT',
  'GOTO',
  'GRANT',
  'GROUP',
  'HAVING',
  'HIGH_PRIORITY',
  'HOUR_MICROSECOND',
  'HOUR_MINUTE',
  'HOUR_SECOND',
  'IF',
  'IGNORE',
  'IN',
  'INDEX',
  'INFILE',
  'INNER',
  'INOUT',
  'INSENSITIVE',
  'INSERT',
  'INT',
  'INT1',
  'INT2',
  'INT3',
  'INT4',
  'INT8',
  'INTEGER',
  'INTERVAL',
  'INTO',
  'IS',
  'ITERATE',
  'JOIN',
  'KEY',
  'KEYS',
  'KILL',
  'LABEL',
  'LEADING',
  'LEAVE',
  'LEFT',
  'LIKE',
  'LIMIT',
  'LINEAR',
  'LINES',
  'LOAD',
  'LOCALTIME',
  'LOCALTIMESTAMP',
  'LOCK',
  'LONG',
  'LONGBLOB',
  'LONGTEXT',
  'LOOP',
  'LOW_PRIORITY',
  'MATCH',
  'MEDIUMBLOB',
  'MEDIUMINT',
  'MEDIUMTEXT',
  'MIDDLEINT',
  'MINUTE_MICROSECOND',
  'MINUTE_SECOND',
  'MOD',
  'MODIFIES',
  'NATURAL',
  'NOT',
  'NO_WRITE_TO_BINLOG',
  'NULL',
  'NUMERIC',
  'ON',
  'OPTIMIZE',
  'OPTION',
  'OPTIONALLY',
  'OR',
  'ORDER',
  'OUT',
  'OUTER',
  'OUTFILE',
  'PRECISION',
  'PRIMARY',
  'PROCEDURE',
  'PURGE',
  'RAID0',
  'RANGE',
  'READ',
  'READS',
  'REAL',
  'REFERENCES',
  'REGEXP',
  'RELEASE',
  'RENAME',
  'REPEAT',
  'REPLACE',
  'REQUIRE',
  'RESTRICT',
  'RETURN',
  'REVOKE',
  'RIGHT',
  'RLIKE',
  'SCHEMA',
  'SCHEMAS',
  'SECOND_MICROSECOND',
  'SELECT',
  'SENSITIVE',
  'SEPARATOR',
  'SET',
  'SHOW',
  'SMALLINT',
  'SPATIAL',
  'SPECIFIC',
  'SQL',
  'SQLEXCEPTION',
  'SQLSTATE',
  'SQLWARNING',
  'SQL_BIG_RESULT',
  'SQL_CALC_FOUND_ROWS',
  'SQL_SMALL_RESULT',
  'SSL',
  'STARTING',
  'STRAIGHT_JOIN',
  'TABLE',
  'TERMINATED',
  'THEN',
  'TINYBLOB',
  'TINYINT',
  'TINYTEXT',
  'TO',
  'TRAILING',
  'TRIGGER',
  'TRUE',
  'UNDO',
  'UNION',
  'UNIQUE',
  'UNLOCK',
  'UNSIGNED',
  'UPDATE',
  'USAGE',
  'USE',
  'USING',
  'UTC_DATE',
  'UTC_TIME',
  'UTC_TIMESTAMP',
  'VALUES',
  'VARBINARY',
  'VARCHAR',
  'VARCHARACTER',
  'VARYING',
  'WHEN',
  'WHERE',
  'WHILE',
  'WITH',
  'WRITE',
  'X509',
  'XOR',
  'YEAR_MONTH',
  'ZEROFILL'
]
export default {
  name: 'ApiRepsTable',
  components: {},
  props: {
    actionButton: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      loading: false,
      dataSource: [],
      columns: [
        {
          title: '', // 必填
          key: 'fieldIndex',
          width: '190px',
          type: FormTypes.hidden,
          disabled: true
        },
        {
          title: '源字段', // 必填
          key: 'fieldName',
          width: '190px',
          type: FormTypes.input,
          disabled: true
        },
        {
          title: '字段名称',
          key: 'columnName',
          width: '190px',
          type: FormTypes.input,
          defaultValue: null,
          placeholder: '请输入${title}',
          validateRules: [
            { required: true, message: '${title}不能为空' },
            {
              pattern: /^[a-zA-Z]{1}(?!_)[a-zA-Z0-9_\\$]+$/,
              message:
                '命名规则:只能由字母、数字、下划线、$符号组成;必须以字母开头;不能以单个字母加下滑线开头'
            },
            {
              handler(type, value, row, column, callback, target) {
                let findCount = 0
                for (var item of target.inputValues) {
                  const val = item['columnName']
                  /* 判断是否是关键字 */
                  if (
                    target.getCleanId(item.id) === row.id &&
                    MySQLKeywords.includes(val.toUpperCase())
                  ) {
                    callback(false, value + '是关键字，不能作为字段名称使用！')
                    return
                  }
                  /* 判断是否有重复的值 */
                  if (val === value && ++findCount >= 2) {
                    callback(false, value + '已存在，不能重复！')
                    return
                  }
                }
                callback(true) // true = 通过验证
              },
              message: '不能有重复的值'
            }
          ]
        },
        {
          title: '字段备注', // 必填
          key: 'columnComment',
          width: '220px',
          type: FormTypes.input,
          defaultValue: '',
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        },
        {
          title: '字段长度', // 必填
          key: 'dbLength',
          width: '125px',
          type: FormTypes.inputNumber,
          defaultValue: 32,
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        },
        {
          title: '小数点', // 必填
          key: 'dbPointLength',
          width: '125px',
          type: FormTypes.inputNumber,
          defaultValue: 0,
          placeholder: '请输入${title}',
          validateRules: [{ required: true, message: '${title}不能为空' }]
        },
        {
          title: '默认值',
          key: 'dbDefaultVal',
          width: '140px',
          type: FormTypes.input,
          defaultValue: ''
        },
        {
          title: '字段类型', // 下拉框，默认String
          key: 'dbType',
          width: '140px',
          type: FormTypes.select,
          options: [
            { title: 'String', value: 'string' },
            { title: 'Integer', value: 'int' },
            { title: 'Double', value: 'double' },
            { title: 'Date', value: 'Date' },
            { title: 'BigDecimal', value: 'BigDecimal' },
            { title: 'Text', value: 'Text' },
            { title: 'Blob', value: 'Blob' }
          ],
          defaultValue: 'string',
          placeholder: '请选择${title}',
          validateRules: [{ required: true, message: '请选择${title}' }]
        },
        {
          title: '主键',
          key: 'dbIsKey',
          width: '100px',
          type: FormTypes.checkbox,
          customValue: ['1', '0'],
          defaultChecked: false
        },
        {
          title: '允许空值',
          key: 'dbIsNull',
          width: '100px',
          type: FormTypes.checkbox,
          customValue: ['1', '0'],
          defaultChecked: true
        }
        // {
        //   title: '排序',
        //   key: 'sort',
        //   isOrder: true, //  是否是排序字段，如果是排序字段，那么新增的默认值永远是最大值+1
        //   type: FormTypes.hidden,
        //   defaultValue: 0
        // }
      ]
    }
  },
  watch: {
    dataSource: {
      handler(dataSource) {
        for (let item of dataSource) {
          item.columnName = item.fieldName
          item.dbPointLength = 0
          item.dbLength = 64
          item.dbType = 'String'
        }
      }
    }
  },
  methods: {
    getValues() {
      return this.$refs.editableTable.getValuesSync({ validate: true })
    },
    reset() {
      this.$refs.editableTable.initialize()
    },
    initValues(data) {
      this.dataSource = data
      // return this.$refs.editableTable.getValuesSync({ validate: true })
    }
  }
}
</script>

<style scoped></style>
