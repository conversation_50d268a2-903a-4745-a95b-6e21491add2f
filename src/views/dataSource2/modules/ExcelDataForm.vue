<template>
  <a-form :form="form">
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="sheet页">
      <a-select placeholder="请选择sheet页" v-decorator="['sheetPage', validatorRules.sheetPage]">
        <a-select-option v-for="item in sheets" :key="'id' + item.value" :value="item.value">{{ item.name }}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="字段名行">
      <a-input-number v-decorator="['nameRecord', validatorRules.nameRecord]" placeholder="请输入字段名行" style="width: 100%" :min="0" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="第一个数据行">
      <a-input-number v-decorator="['dataRecord1st', validatorRules.dataRecord1st]" placeholder="请输入第一个数据行" style="width: 100%" :min="0" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="最后一个数据行">
      <a-input-number v-decorator="['dataRecordLast', validatorRules.dataRecordLast]" placeholder="请输入最后一个数据行" style="width: 100%" :min="0" />
    </a-form-item>
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="新建表">
      <j-switch :options="[1, 0]"  v-decorator="['createTable', validatorRules.createTable]"/>
    </a-form-item>
  </a-form>
</template>

<script>
import pick from 'lodash.pick'
export default {
  name: 'CsvDataForm',
  components: {},
  props: {

  },
  data() {
    return {
      sheets: [],
      fileList: [],
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        sheetPage: { rules: [{ required: true, message: '请选择sheet页!' }] },
        nameRecord: { rules: [{ required: true, message: '请输入字段名行!' }] },
        dataRecord1st: { rules: [{ required: true, message: '请输入第一个数据行!' }] },
        dataRecordLast: { rules: [] }
      }
    }
  },
  created() {
  },
  methods: {
    setSheets(val) {
      this.sheets = val
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped>
</style>
