<template>
  <a-form :form="form">
    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="导入模式">
      <a-radio-group class="fontiframe" name="radioGroup" v-model="importType">
        <a-radio class="radioGroup" value="add">追加：添加记录到目标表</a-radio>
        <a-radio class="radioGroup" value="reAdd">复制：删除目标全部记录，并从源重新导入</a-radio>
      </a-radio-group>
    </a-form-item>
  </a-form>
</template>

<script>
import pick from 'lodash.pick'
export default {
  name: 'FileDataImportMode',
  components: {},
  props: {

  },
  data() {
    return {
      importType: 'add',
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        sheetPage: { rules: [{ required: true, message: '请选择sheet页!' }] }
      }
    }
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
</style>
