<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="文件名称">
              <j-input v-model="queryParam.fileName" placeholder="请输入文件名称"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="来源部门">
              <j-input v-model="queryParam.sysOrgCode" placeholder="请输入来源部门"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" icon="search" @click="searchQuery">查询</a-button>
              <a-button type="primary" icon="reload" style="margin-left: 8px" @click="searchReset"
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator" style="border-top: 5px">
      <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
      <a-button type="primary" @click="batchDel"> 批量删除 </a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i>已选择&nbsp;<a
          style="font-weight: 600"
          >{{ selectedRowKeys.length }}</a
        >项&nbsp;&nbsp;
        <a style="margin-left: 24px" @click="onClearSelected">取消选择</a>
      </div>

      <a-table
        ref="table"
        bordered
        size="middle"
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <span slot="action" slot-scope="text, record">
          <a @click="handleViewData(record)">查看数据</a>
          <a-divider type="vertical" />
          <a @click="handleDeleteBefore(record.id)">删除</a>
        </span>
      </a-table>
    </div>
    <a-modal
      v-model="deleteVisible"
      title="删除方式"
      @ok="handleDeleteCustom"
      @cancel="handleCancel"
    >
      <a-radio-group v-model="deleteType" class="fontiframe" name="radioGroup">
        <a-radio class="radioGroup" value="1">仅删除文件</a-radio>
        <a-radio class="radioGroup" value="2">同时删除表数据</a-radio>
        <a-radio class="radioGroup" value="3">同时删除表结构</a-radio>
      </a-radio-group>
    </a-modal>
    <!-- table区域-end -->
    <FileDataModal ref="modalForm" @ok="modalFormOk" />
    <data-edit ref="dataEdit" @ok="modalFormOk"></data-edit>
  </a-card>
</template>

<script>
import { postAction, getFileAccessHttpUrl } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import FileDataModal from './modules/FileDataModal'
import DataEdit from './modules/DataEditFile.vue'
import { getAction, deleteAction } from '@/api/manage'
export default {
  name: 'FileDataList',
  components: {
    FileDataModal,
    DataEdit
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      deleteVisible: false,
      deleteType: '1',
      deleteId: null,
      description: '文件管理页面',
      fuzzyParam: ['sysOrgCode', 'fileName'],
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '文件名称',
          align: 'center',
          dataIndex: 'fileName',
          sorter: true
        },
        {
          title: '文件类型',
          align: 'center',
          dataIndex: 'fileType',
          sorter: true
        },
        {
          title: '目标表',
          align: 'center',
          dataIndex: 'tableId_dictText'
        },
        {
          title: '来源部门',
          align: 'center',
          dataIndex: 'systemName'
        },
        {
          title: '创建人',
          align: 'center',
          dataIndex: 'createBy'
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
          align: 'center',
          width: 180
        }
      ],
      url: {
        list: '/dataSource/scDataFile/list',
        delete: '/dataSource/scDataFile/delete',
        deleteBatch: '/dataSource/scDataFile/deleteBatch',
        exportXlsUrl: '/sys/user/exportXls',
        importExcelUrl: 'sys/user/importExcel'
      }
    }
  },
  computed: {},
  methods: {
    handleDeleteBefore(id) {
      this.deleteId = id
      this.deleteVisible = true
    },
    handleCancel() {
      this.deleteType = '1'
      this.deleteId = null
      this.deleteVisible = false
    },
    handleDeleteCustom() {
      var params = {
        id: this.deleteId
      }
      if (this.deleteType == '1') {
        params.deleteTable = false
        params.deleteTableData = false
      } else if (this.deleteType == '2') {
        params.deleteTable = false
        params.deleteTableData = true
      } else if (this.deleteType == '3') {
        params.deleteTable = true
        params.deleteTableData = true
      }
      getAction(this.url.delete, params).then((res) => {
        if (res.success) {
          this.deleteId = null
          this.deleteType = '1'
          this.searchQuery()
          this.deleteVisible = false
        }
      })
      // alert(this.deleteType)
      // alert(this.deleteId)
    },
    /**
     * @description 查看数据
     * @param {Object} record
     * @returns void
     */
    handleViewData(record) {
      this.$refs.dataEdit.handleOpenData(record, {
        tableName: record.tableId_dictText,
        sourceId: record.tableId
      })
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
