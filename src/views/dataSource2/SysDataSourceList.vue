<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="数据源名称">
              <j-input placeholder="请输入数据源名称" v-model="queryParam.name" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="数据库类型">
              <j-dict-select-tag v-model="queryParam.dbType" placeholder="请选择数据库类型" dict-code="database_type" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('多数据源管理')">导出</a-button>
      <a-button @click="batchDel('POST')" type="primary"> 批量删除 </a-button>
      <j-super-query
        :fieldList="superQueryFieldList"
        ref="superQueryModal"
        @handleSuperQuery="handleSuperQuery"
      ></j-super-query>
    </div>
    <!-- table区域-begin -->
    <div>
      <a-alert type="info" showIcon style="margin-bottom: 16px">
        <template slot="message">
          <span>已选择</span>
          <a style="font-weight: 600; padding: 0 4px">{{ selectedRowKeys.length }}</a>
          <span>项</span>
          <a style="margin-left: 24px" @click="onClearSelected">取消选择</a>
        </template>
      </a-alert>
      <a-table
        ref="table"
        size="middle"
        bordered
        :scroll="{ x: 1300 }"
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <span slot="updateBy" slot-scope="text, record">
          <span v-if="text != null && text != ''">{{ text }}</span>
          <span v-else>{{ record.createBy }}</span>
        </span>
        <span slot="updateTime" slot-scope="text, record">
          <span v-if="text != null && text != ''">{{ text }}</span>
          <span v-else>{{ record.createTime }}</span>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="handleTest(record)">连接测试</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;" @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
        <!-- 状态渲染模板 -->
        <template slot="customRenderStatus" slot-scope="dbStatus">
          <a-tag v-if="dbStatus == 1" color="green">启用</a-tag>
          <a-tag v-if="dbStatus == 0" color="red">停用</a-tag>
        </template>
      </a-table>
    </div>
    <!-- table区域-end -->
    <!-- 表单区域 -->
    <sys-data-source-modal ref="modalForm" @ok="modalFormOkBefore" />
  </a-card>
</template>

<script>
import JEllipsis from '@/components/jeecg/JEllipsis'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SysDataSourceModal from './modules/SysDataSourceModal'
import { postAction, getAction } from '@/api/manage'
import JInput from '@comp/jeecg/JInput'
export default {
  name: 'SysDataSourceList',
  // eslint-disable-next-line vue/no-unused-components
  components: { JEllipsis, SysDataSourceModal, JInput },
  mixins: [JeecgListMixin],
  data() {
    const ellipsis = (v, l = 20) => <j-ellipsis value={v} length={l} />
    return {
      description: '数据库连接管理页面',
      dataSourceList: [],
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (t, r, index) => index + 1
        },
        {
          title: '数据源名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '数据库类型',
          align: 'center',
          sorter: true,
          width: 130,
          dataIndex: 'dbType_dictText'
        },
        {
          title: '驱动类',
          align: 'center',
          dataIndex: 'dbDriver',
          width: 130,
          customRender: (t) => ellipsis(t)
        },
        {
          title: '数据源地址',
          align: 'center',
          dataIndex: 'dbUrl',
          width: 130,
          customRender: (t) => ellipsis(t)
        },
        {
          title: '用户名',
          align: 'center',
          dataIndex: 'dbUsername'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'dbStatus',
          scopedSlots: { customRender: 'customRenderStatus' },
          filterMultiple: false,
          filters: [
            { text: '启用', value: '1' },
            { text: '停用', value: '0' }
          ]
        },
        {
          title: '编辑人',
          align: 'center',
          dataIndex: 'updateBy',
          scopedSlots: { customRender: 'updateBy' }
        },
        {
          title: '编辑时间',
          align: 'center',
          dataIndex: 'updateTime',
          scopedSlots: { customRender: 'updateTime' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 180,
          scopedSlots: { customRender: 'action' }
        }
      ],
      superQueryFieldList: [
        { type: 'input', value: 'name', text: '数据源名称' },
        { type: 'string', value: 'dbType', text: '数据库类型', dictCode: 'database_type' },
        { type: 'input', value: 'dbDriver', text: '驱动类' },
        { type: 'input', value: 'dbUrl', text: '数据源地址' },
        { type: 'input', value: 'dbUsername', text: '用户名' }
      ],
      url: {
        connectTest: '/online/cgreport/api/testConnection',
        list: '/sys/dataSource/list',
        delete: '/sys/dataSource/delete',
        deleteBatch: '/sys/dataSource/deleteBatch',
        exportXlsUrl: `sys/dataSource/exportXls?exportFields=name,dbType,dbDriver,dbUrl,dbUsername,dbStatus,updateBy,updateTime`,
        importExcelUrl: 'sys/dataSource/importExcel'
      }
    }
  },
  computed: {
    importExcelUrl() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    this.getDataSourceList()
  },
  methods: {
    modalFormOkBefore() {
      // this.refleshCache()
      this.modalFormOk()
    },
    refleshCache() {
      getAction('sys/dict/refleshCache')
        .then((res) => {
          if (res.success) {
            console.log('缓存刷新成功')
          } else {
            console.log('缓存刷新失败', res)
          }
        })
        .catch((e) => {
          console.log('缓存刷新成功', e)
        })
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      // 加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) this.ipagination.current = 1
      const params = this.getQueryParams() // 查询条件
      this.loading = true
      var queryParam = JSON.parse(JSON.stringify(params))
      // if (queryParam.name != null && queryParam.name != '') {
      //   queryParam.name = '*' + queryParam.name + '*'
      // }
      getAction(this.url.list, queryParam)
        .then((res) => {
          if (res.success) {
            this.dataSource = res.result.records || res.result
            if (res.result.total) {
              this.ipagination.total = res.result.total
            } else {
              this.ipagination.total = 0
            }
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 测试数据源配置是否可以正常连接
    handleTest(data) {
      const loading = this.$message.loading('连接中……', 0)
      postAction(this.url.connectTest, data)
        .then((res) => {
          if (res.success) {
            this.$message.success('连接成功')
          } else throw new Error(res.message)
        })
        .catch((error) => {
          this.$warning({ title: '连接失败', content: error.message || error })
        })
        .finally(() => loading())
    },
    // 数据源名称下拉框数据
    getDataSourceList() {
      getAction('/sys/dataSource/list', { pageNo: 1, pageSize: 10000 }).then((res) => {
        if (res.success) {
          this.dataSourceList = res.result.records
        } else {
          this.dataSourceList = []
        }
      })
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>

