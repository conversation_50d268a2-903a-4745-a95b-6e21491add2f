<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="接口服务名称">
              <j-input v-model="queryParam.apiName" placeholder="请输入接口服务名称"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="接入类型">
              <j-dict-select-tag
                v-model="queryParam.apiMethod"
                dictCode="api_method"
                placeholder="请选择接入类型"
              ></j-dict-select-tag>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" icon="search" @click="searchQuery">查询</a-button>
              <a-button type="primary" icon="reload" style="margin-left: 8px" @click="searchReset"
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator" style="border-top: 5px">
      <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
      <a-button type="primary" @click="batchDel"> 批量删除 </a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i>已选择&nbsp;<a
          style="font-weight: 600"
          >{{ selectedRowKeys.length }}</a
        >项&nbsp;&nbsp;
        <a style="margin-left: 24px" @click="onClearSelected">取消选择</a>
      </div>

      <a-table
        ref="table"
        bordered
        size="middle"
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <span slot="authorizationMethod" slot-scope="text">
          {{
            text == '1' ? '无需授权' : text == '2' ? '提供方授权' : text == '3' ? '平台授权' : ''
          }}
        </span>
        <span slot="isAuthorized" slot-scope="text">
          {{ text == '1' ? '是' : '否' }}
        </span>
        <span slot="isScheduled" slot-scope="text" :style="{ color: !!text ? 'green' : 'red' }">
          {{ !!text ? '正常' : '停止' }}
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="handleViewData(record)">查看数据</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleSync(record)">同步配置</a>
              </a-menu-item>
              <a-menu-item>
                <a @click="handleEdit(record)">编辑</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->
    <SysDataSourceInterfaceModal
      ref="modalForm"
      @ok="modalFormOk"
      @close="freshTable"
    ></SysDataSourceInterfaceModal>
    <a-modal :visible="visible" title="同步" @ok="handleOk" @cancel="handleCancel">
      <a-form :form="form">
        <a-form-item label="数据表名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            v-decorator="['tableName', validatorRules.tableName]"
            placeholder="请输入数据表名称"
            :disabled="hasTable"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <interfaceSyncModal ref="interfaceSyncModal" @ok="modalFormOk"></interfaceSyncModal>
    <data-edit ref="dataEdit"></data-edit>
  </a-card>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixinNew'
import SysDataSourceInterfaceModal from './modules/SysDataSourceInterfaceModal'
import interfaceSyncModal from '@/views/dataSource2/modules/interfaceSyncModal.vue'
import dataEdit from '@/views/dataSource/modules/DataEditDataSource.vue'
export default {
  name: 'InterfaceList',
  components: {
    SysDataSourceInterfaceModal,
    interfaceSyncModal,
    dataEdit
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      hasTable: false,
      description: '接口管理页面',
      fuzzyParam: ['apiName'],
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '接口服务名称',
          align: 'center',
          dataIndex: 'apiName',
          sorter: true
        },
        {
          title: '数据源描述',
          align: 'center',
          dataIndex: 'apiDescription'
        },
        {
          title: '是否授权接口',
          align: 'center',
          width: 120,
          dataIndex: 'isAuthorized',
          scopedSlots: { customRender: 'isAuthorized' }
        },
        // {
        //   title: '授权方式',
        //   align: 'center',
        //   width: 100,
        //   dataIndex: 'authorizationMethod',
        //   scopedSlots: { customRender: 'authorizationMethod' }
        // },
        {
          title: '接入类型',
          align: 'center',
          width: 90,
          dataIndex: 'apiMethod_dictText'
        },
        {
          title: '服务URL',
          align: 'center',
          dataIndex: 'apiUrl'
        },
        {
          title: '同步任务',
          align: 'center',
          dataIndex: 'isScheduled',
          scopedSlots: { customRender: 'isScheduled' }
        },
        {
          title: '关联库表',
          align: 'center',
          dataIndex: 'tableName'
        },
        // {
        //   title: '接口文档',
        //   align: 'center',
        //   dataIndex: 'interfaceDocumentationPath'
        // },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
          align: 'center',
          width: 180
        }
      ],
      url: {
        list: '/dataSource/scApi/list',
        delete: '/dataSource/scApi/delete',
        deleteBatch: '/dataSource/scApi/deleteBatch',
        exportXlsUrl: '/sys/user/exportXls',
        importExcelUrl: 'sys/user/importExcel',
        listTable: '/data/scCockpitDataSource/list'
      },
      validatorRules: {
        tableName: { rules: [{ required: true, message: '请输入数据表名称!' }] }
      },
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      visible: false,
      apiId: null
    }
  },
  computed: {},
  methods: {
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          postAction('/dataSource/api/scApiDatatable/add', {
            apiId: this.apiId,
            tableName: values.tableName
          }).then((res) => {
            if (res.success) {
              that.searchQuery()
              that.visible = false
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      })
    },
    handleCancel() {
      this.visible = false
    },
    handleSync(record) {
      if (record.tableName) {
        this.$refs.interfaceSyncModal.title = '编辑同步配置'
        this.$refs.interfaceSyncModal.edit(record)
      } else {
        this.$refs.interfaceSyncModal.title = '新增同步配置'
        this.$refs.interfaceSyncModal.add(record)
      }
    },
    handleViewData(record) {
      getAction(this.url.listTable, { tableName: record.tableName }).then((res) => {
        if (res.success) {
          if (!res.result.records.length) {
            this.$message.warning('该表不存在')
            return
          }
          this.$refs.dataEdit.handleOpenData(record, {
            dataSourceCode: record.tableDataSource,
            tableName: record.tableName,
            sourceId: res.result.records[0].id
          })
        } else {
          this.$message.warning('表信息查询失败')
        }
      })
    },
    freshTable() {
      this.searchQuery()
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
