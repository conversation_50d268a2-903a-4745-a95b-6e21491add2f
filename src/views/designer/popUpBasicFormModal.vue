<template>
  <j-modal
    title="弹窗基本信息"
    width="30%"
    destroyOnClose
    :visible.sync="dialogVisible"
    :maskClosable="false"
    :keyboard="false"
    :closable="false"
  >
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="handleOk">确 定</el-button>
    </span>
    <div>
      <a-form-model ref="form" slot="detail" :model="form" :rules="rules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              label="弹窗标题"
              prop="title"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input v-model="form.title" placeholder="请输入弹窗标题"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="弹窗宽度"
              prop="width"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input-number
                v-model="form.width"
                :min="0"
                placeholder="请输入弹窗宽度"
              ></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="弹窗高度"
              prop="height"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input-number
                v-model="form.height"
                :min="0"
                placeholder="请输入弹窗高度"
              ></a-input-number>
            </a-form-model-item>
          </a-col> </a-row
      ></a-form-model>
    </div>
  </j-modal>
</template>
<script>
export default {
  name: '',
  data() {
    return {
      dialogVisible: false,
      form: {
        title: '',
        height: 400,
        width: 400
      },
      rules: {
        title: [{ required: true, message: '请输入弹窗标题', trigger: 'blur' }],
        height: [{ required: true, message: '请输入弹窗高度', trigger: 'blur' }],
        width: [{ required: true, message: '请输入弹窗宽度', trigger: 'blur' }]
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
  },
  methods: {
    edit() {
      this.form = {
        title: '',
        height: 400,
        width: 400
      }
      this.dialogVisible = true
    },
    handleOk() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$message.success('新建弹窗成功')
          this.$emit('popUpBasicInfo', { components: [], ...this.form })
          this.dialogVisible = false
        } else {
          return false
        }
      })
    }
  }
}
</script>
