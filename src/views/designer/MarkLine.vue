<template>
  <div class="mark-line">
    <div
      v-for="line in lines"
      v-show="lineStatus[line] || false"
      :key="line"
      :ref="line"
      class="line"
      :class="line.includes('x') ? 'xline' : 'yline'"
    ></div>
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { findNodeById } from '@/utils/util'

export default {
  data() {
    return {
      lines: ['xt', 'xc', 'xb', 'yl', 'yc', 'yr'], // 分别对应三条横线和三条竖线
      diff: 3, // 相距 dff 像素将自动吸附
      lineStatus: {
        xt: false,
        xc: false,
        xb: false,
        yl: false,
        yc: false,
        yr: false
      }
    }
  },
  computed: mapWritableState(useDesignerStore, ['currentCpt', 'componentData']),
  mounted() {
    // 监听元素移动和不移动的事件
    this.$bus.$on('move', (pid, isDownward, isRightward) => {
      let componentData = this.componentData
      if (pid) {
        const node = findNodeById(pid, componentData)
        componentData = node?.children ?? []
      }
      this.showLine(componentData, isDownward, isRightward)
    })

    this.$bus.$on('unmove', () => {
      this.hideLine()
    })
  },
  methods: {
    hideLine() {
      Object.keys(this.lineStatus).forEach((line) => {
        this.lineStatus[line] = false
      })
    },

    showLine(components, isDownward, isRightward) {
      const lines = this.$refs
      const dragNodeRectInfo = { ...this.currentCpt }
      const dragNodeHalfwidth = Math.round(dragNodeRectInfo.cptWidth / 2)
      const dragNodeHalfHeight = Math.round(dragNodeRectInfo.cptHeight / 2)
      dragNodeRectInfo.left = dragNodeRectInfo.cptX
      dragNodeRectInfo.top = dragNodeRectInfo.cptY
      dragNodeRectInfo.bottom = dragNodeRectInfo.cptY + dragNodeRectInfo.cptHeight
      dragNodeRectInfo.right = dragNodeRectInfo.cptX + dragNodeRectInfo.cptWidth

      this.hideLine()
      components.forEach((component) => {
        if (component == this.currentCpt) return
        const { cptY: top, cptHeight: height, cptX: left, cptWidth: width } = component
        const bottom = top + height
        const right = left + width
        const nodeHalfwidth = Math.round(width / 2)
        const nodeHalfHeight = Math.round(height / 2)

        const conditions = {
          top: [
            {
              isNearly: this.isNearly(dragNodeRectInfo.top, top),
              lineNode: lines.xt[0], // xt
              line: 'xt',
              dragShift: top,
              lineShift: top
            },
            {
              isNearly: this.isNearly(dragNodeRectInfo.bottom, top),
              lineNode: lines.xt[0], // xt
              line: 'xt',
              dragShift: top - dragNodeRectInfo.cptHeight,
              lineShift: top
            },
            {
              // 组件与拖拽节点的中间是否对齐
              isNearly: this.isNearly(
                dragNodeRectInfo.top + dragNodeHalfHeight,
                top + nodeHalfHeight
              ),
              lineNode: lines.xc[0], // xc
              line: 'xc',
              dragShift: top + nodeHalfHeight - dragNodeHalfHeight,
              lineShift: top + nodeHalfHeight
            },
            {
              isNearly: this.isNearly(dragNodeRectInfo.top, bottom),
              lineNode: lines.xb[0], // xb
              line: 'xb',
              dragShift: bottom,
              lineShift: bottom
            },
            {
              isNearly: this.isNearly(dragNodeRectInfo.bottom, bottom),
              lineNode: lines.xb[0], // xb
              line: 'xb',
              dragShift: bottom - dragNodeRectInfo.cptHeight,
              lineShift: bottom
            }
          ],
          left: [
            {
              isNearly: this.isNearly(dragNodeRectInfo.left, left),
              lineNode: lines.yl[0], // yl
              line: 'yl',
              dragShift: left,
              lineShift: left
            },
            {
              isNearly: this.isNearly(dragNodeRectInfo.right, left),
              lineNode: lines.yl[0], // yl
              line: 'yl',
              dragShift: left - dragNodeRectInfo.cptWidth,
              lineShift: left
            },
            {
              // 组件与拖拽节点的中间是否对齐
              isNearly: this.isNearly(
                dragNodeRectInfo.left + dragNodeHalfwidth,
                left + nodeHalfwidth
              ),
              lineNode: lines.yc[0], // yc
              line: 'yc',
              dragShift: left + nodeHalfwidth - dragNodeHalfwidth,
              lineShift: left + nodeHalfwidth
            },
            {
              isNearly: this.isNearly(dragNodeRectInfo.left, right),
              lineNode: lines.yr[0], // yr
              line: 'yr',
              dragShift: right,
              lineShift: right
            },
            {
              isNearly: this.isNearly(dragNodeRectInfo.right, right),
              lineNode: lines.yr[0], // yr
              line: 'yr',
              dragShift: right - dragNodeRectInfo.cptWidth,
              lineShift: right
            }
          ]
        }

        const needToShow = []
        Object.keys(conditions).forEach((key) => {
          // 遍历符合的条件并处理
          conditions[key].forEach((condition) => {
            if (!condition.isNearly) return
            // 修改当前组件位移
            this.currentCpt[key === 'left' ? 'cptX' : 'cptY'] = condition.dragShift
            condition.lineNode && (condition.lineNode.style[key] = `${condition.lineShift}px`)
            needToShow.push(condition.line)
          })
        })

        // 同一方向上的线只显示一条，例如多条横条只显示一条横线
        if (needToShow.length) {
          this.chooseTheTureLine(needToShow, isDownward, isRightward)
        }
      })
    },

    chooseTheTureLine(needToShow, isDownward, isRightward) {
      // 如果鼠标向右移动 则按从右到左的顺序显示竖线 否则按相反顺序显示
      // 如果鼠标向下移动 则按从下到上的顺序显示横线 否则按相反顺序显示
      if (isRightward) {
        if (needToShow.includes('yr')) {
          this.lineStatus.yr = true
        } else if (needToShow.includes('yc')) {
          this.lineStatus.yc = true
        } else if (needToShow.includes('yl')) {
          this.lineStatus.yl = true
        }
      } else {
        if (needToShow.includes('yl')) {
          this.lineStatus.yl = true
        } else if (needToShow.includes('yc')) {
          this.lineStatus.yc = true
        } else if (needToShow.includes('yr')) {
          this.lineStatus.yr = true
        }
      }

      if (isDownward) {
        if (needToShow.includes('xb')) {
          this.lineStatus.xb = true
        } else if (needToShow.includes('xc')) {
          this.lineStatus.xc = true
        } else if (needToShow.includes('xt')) {
          this.lineStatus.xt = true
        }
      } else {
        if (needToShow.includes('xt')) {
          this.lineStatus.xt = true
        } else if (needToShow.includes('xc')) {
          this.lineStatus.xc = true
        } else if (needToShow.includes('xb')) {
          this.lineStatus.xb = true
        }
      }
    },

    isNearly(dragValue, targetValue) {
      return Math.abs(dragValue - targetValue) <= this.diff
    }
  }
}
</script>

<style scoped>
.mark-line {
  height: 100%;
  pointer-events: none;
}
.line {
  background: #59c7f9;
  position: absolute;
  z-index: 10000;
}
.xline {
  width: 100%;
  height: 1px;
}
.yline {
  width: 1px;
  height: 100%;
}
</style>
