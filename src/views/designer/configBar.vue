<template>
  <div class="config-bar">
    <div>
      <div class="cptTitle">{{ configBarShow ? '组件配置' : '大屏配置' }}</div>
      <el-button
        v-show="currentCpt.cptOption"
        type="primary"
        style="width: 100%; margin: 20px 0"
        @click="refreshCptData(currentCpt.pid || null)"
        >刷新数据</el-button
      >
      <el-tabs v-show="configBarShow" v-model="configTab" :stretch="true">
        <el-tab-pane label="属性" name="custom" style="max-height: calc(100vh - 200px)">
          <el-form label-width="80px" size="mini">
            <!-- <a-collapse expandIconPosition="right">
              <a-collapse-panel key="基础" header="基础" forceRender> -->
            <el-form-item label="组件 ID">
              <el-input v-model="currentCpt.id" :title="currentCpt.id" disabled />
            </el-form-item>
            <el-form-item label="宽度">
              <el-input-number
                v-model="currentCpt.cptWidth"
                :min="1"
                :max="2000"
                size="small"
                @change="handleWidthChange"
              />
            </el-form-item>
            <el-form-item label="高度">
              <el-input-number
                v-model="currentCpt.cptHeight"
                :min="1"
                :max="9999"
                size="small"
                @change="handleHeightChange"
              />
            </el-form-item>
            <!-- <template v-if="isCptOnOutsideLayer"> -->
            <el-form-item label="X 轴">
              <el-input-number
                v-model="currentCpt.cptX"
                :min="-500"
                :max="2500"
                size="small"
                @change="handlePositionChange"
              />
            </el-form-item>
            <el-form-item label="Y 轴">
              <el-input-number
                v-model="currentCpt.cptY"
                :min="-500"
                size="small"
                @change="handlePositionChange"
              />
            </el-form-item>
            <el-form-item label="Z 轴">
              <el-input-number
                v-model="currentCpt.cptZ"
                :min="1"
                :max="1800"
                size="small"
                @change="handleZIndexChange"
              />
            </el-form-item>
            <el-form-item label="对齐">
              <el-button-group>
                <el-button
                  size="small"
                  icon="el-icon-sort"
                  title="水平居中"
                  style="transform: rotate(90deg)"
                  @click="alignHorizontalCenter"
                />
                <el-button
                  size="small"
                  icon="el-icon-sort"
                  title="垂直居中"
                  @click="alignVerticalCenter"
                />
                <el-button
                  size="small"
                  icon="el-icon-full-screen"
                  title="完全居中"
                  @click="alignFullCenter"
                />
              </el-button-group>
            </el-form-item>
            <!-- </template> -->
            <cpt-common-option
              v-if="currentCpt && currentCpt.cptOption"
              :attribute="currentCpt.cptOption.attribute"
            ></cpt-common-option>
            <!-- </a-collapse-panel>
            </a-collapse> -->
            <component
              :is="currentCpt.cptOptionKey || `${currentCpt.cptKey}-option`"
              v-if="currentCpt && currentCpt.cptOption"
              :attribute="currentCpt.cptOption.attribute"
            /> </el-form
        ></el-tab-pane>
        <!--      展示数据表单需在option.js初始化cptDataForm-->
        <el-tab-pane v-if="cptDataFormShow" label="数据" name="data">
          <div class="customForm" :style="{ height: height - 140 + 'px' }">
            <a-form-model layout="vertical">
              <!-- <a-form-model-item label="数据类型"> -->
              <!-- <el-radio-group
                  v-model="currentCpt.cptOption.cptDataForm.dataSource"
                  @change="changeDataSource"
                > -->
              <!-- <el-radio :label="1">静态数据</el-radio> -->
              <!-- <el-radio :label="2">接口</el-radio>
                  <el-radio :label="3">sql</el-radio> -->
              <!-- </el-radio-group> -->
              <!-- </a-form-model-item> -->
              <a-form-model-item
                v-show="currentCpt.cptOption.cptDataForm.dataSource !== 1"
                label="轮询"
              >
                <el-switch v-model="dataPollEnable" active-text="开启" inactive-text="关闭" />
              </a-form-model-item>
              <a-form-model-item v-show="dataPollEnable" label="轮询时间(s)">
                <el-input-number
                  v-model="currentCpt.cptOption.cptDataForm.pollTime"
                  :min="0"
                  :max="100"
                  label="描述文字"
                />
              </a-form-model-item>
              <a-form-model-item
                :label="dataLabels[currentCpt.cptOption.cptDataForm.dataSource - 1]"
              >
                <vue-json-editor
                  v-show="currentCpt.cptOption.cptDataForm.dataSource === 1"
                  v-model="dataJson"
                  :show-btns="false"
                  :expandedOnStart="true"
                  :mode="'code'"
                  @input="onJsonChange"
                />
                <el-input
                  v-show="currentCpt.cptOption.cptDataForm.dataSource === 2"
                  v-model="currentCpt.cptOption.cptDataForm.apiUrl"
                  type="textarea"
                  :rows="5"
                />
                <codemirror
                  v-show="currentCpt.cptOption.cptDataForm.dataSource === 3"
                  v-model="currentCpt.cptOption.cptDataForm.sql"
                  class="code"
                  :options="cmOptions"
                />
              </a-form-model-item>
            </a-form-model>
          </div>
        </el-tab-pane>
        <el-tab-pane label="交互" name="event">
          <div style="margin-bottom: 10px">
            组件ID: <span>{{ currentCptId }}</span>
          </div>
          <el-button style="width: 100%; margin-bottom: 10px" plain @click="addEvent"
            >添加事件</el-button
          >
          <el-form v-if="currentCpt.events" label-width="70px">
            <div v-for="(event, index) in currentCpt.events" :key="index" class="event-item">
              <div class="event-header">
                <span>事件{{ index + 1 }}</span>
                <el-button type="text" style="color: #f56c6c" @click="removeEvent(index)">
                  删除
                </el-button>
              </div>
              <el-form-item label="触发类型">
                <el-select v-model="event.triggerType" placeholder="请选择触发类型">
                  <el-option label="点击" value="click"></el-option>
                  <el-option label="双击" value="dblclick"></el-option>
                  <el-option label="右击" value="contextmenu"></el-option>
                  <el-option label="鼠标移动" value="mousemove"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="响应类型">
                <el-select v-model="event.responseType" placeholder="请选择响应类型">
                  <el-option label="打开链接" value="link"></el-option>
                  <el-option label="打开弹窗" value="popup"></el-option>
                  <el-option label="自定义JS事件" value="jsevent"></el-option>
                </el-select>
              </el-form-item>
              <template v-if="event.responseType === 'link'">
                <el-form-item label="链接类型">
                  <el-select v-model="event.linkType" placeholder="请选择链接类型">
                    <el-option label="本地页面" value="local"></el-option>
                    <el-option label="外部链接" value="external"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="链接地址">
                  <el-input v-model="event.linkUrl" placeholder="请输入链接地址"></el-input>
                </el-form-item>
              </template>
              <template v-if="event.responseType === 'popup'">
                <el-form-item label="弹窗组件">
                  <el-select v-model="event.popupComponentId" placeholder="请选择弹窗">
                    <el-option
                      v-for="item in popupComponentList"
                      :key="item.id"
                      :label="item.cptOption.attribute.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template v-else-if="event.responseType === 'jsevent'">
                <el-form-item label="JS代码">
                  <el-input
                    v-model="event.jsCode"
                    type="textarea"
                    :rows="5"
                    placeholder="请输入自定义JS代码"
                  ></el-input>
                </el-form-item>
              </template>
            </div>
          </el-form>
        </el-tab-pane>
        <!-- 目前只允许echarts图自定义代码 -->
        <el-tab-pane
          v-if="!currentCpt.isBindingComponent && !specialComponents.includes(currentCpt.cptTitle)"
          label="数据绑定"
          name="indexBinding"
        >
          <indexSetTree ref="indexSetTree" @tableColumn="handleTableColumn" />
        </el-tab-pane>
        <el-tab-pane
          v-if="currentCpt.cptOption && currentCpt.cptOption.series"
          label="自定义代码"
          name="code"
        >
          <customCodeForm
            :jsData="jsData"
            :cssData="cssData"
            :pid="currentCpt.pid || ''"
            @customCode="handleCustomCode"
            @refreshCptData="refreshCptData(currentCpt.pid || null)"
            @changeCssData="changeCssData"
          />
        </el-tab-pane>
      </el-tabs>
      <div v-show="!configBarShow" style="margin-top: 10px; overflow: auto">
        <a-form-model
          :model="designData"
          :colon="false"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-model-item :label="isModule ? '模块名称' : '专题名称'">
            <a-input
              v-model="isModule ? designData.moduleName : designData.topicName"
              autocomplete="off"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item :label="isModule ? '模块描述' : '专题描述'">
            <a-input
              v-model="isModule ? designData.moduleDes : designData.topicDescription"
              type="textarea"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="分辨率X">
            <el-input-number
              v-model="designData.scaleX"
              disabled
              :min="0"
              :max="10240"
              style="width: 100%"
            />
          </a-form-model-item>
          <a-form-model-item label="分辨率Y">
            <el-input-number
              v-model="designData.scaleY"
              disabled
              :min="0"
              :max="99999"
              style="width: 100%"
            />
          </a-form-model-item>
          <!-- <a-form-model-item label="背景颜色">
            <el-color-picker v-model="designData.bgColor" show-alpha />
          </a-form-model-item> -->
          <a-form-model-item label="背景图片">
            <j-image-upload v-model="designData.topicBackground"></j-image-upload>
            <el-button @click="showGallery">图库选择</el-button>
          </a-form-model-item>
          <div v-if="!isModule">
            <a-form-model-item label="专题图标">
              <j-image-upload v-model="designData.topicIcon"></j-image-upload>
            </a-form-model-item>
            <!-- <a-form-model-item label="个性链接">
            <a-input v-model="designData.id" disabled autocomplete="off" />
          </a-form-model-item> -->
            <!-- <a-form-model-item label="访问码">
            <a-input v-model="designData.viewCode" autocomplete="off" />
          </a-form-model-item> -->
            <a-form-model-item label="专题url">
              <a-input v-model="designData.topicUrl" autocomplete="off" />
            </a-form-model-item>
            <a-form-model-item label="专题编码">
              <a-input v-model="designData.topicCode" autocomplete="off" />
            </a-form-model-item>
            <a-form-model-item label="状态">
              <a-radio-group v-model="designData.status" name="statusGroup">
                <a-radio :value="1"> 展示 </a-radio>
                <a-radio :value="0"> 隐藏 </a-radio>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item label="专题模板">
              <a-radio-group v-model="designData.topicTemplate" name="statusGroup">
                <a-radio :value="1"> 是 </a-radio>
                <a-radio :value="0"> 否 </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </div>

          <a-form-model-item label="默认背景">
            <j-image-upload v-model="designData.defaultBgImage" disabled></j-image-upload>
          </a-form-model-item>
          <a-form-model-item label="默认地图">
            <a-input
              v-model="designData.defaultMapUrl"
              :title="designData.defaultMapUrl"
              disabled
              class="map-input"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="默认菜单">
            <a-input
              v-model="designData.defaultMenuUrl"
              :title="designData.defaultMenuUrl"
              disabled
              class="menu-input"
            ></a-input>
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
    <gallery ref="gallery" @confirmCheck="confirmCheck" />
  </div>
</template>

<script>
import { mapWritableState, mapActions } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { isJSON } from '@/utils/myutils'
import vueJsonEditor from 'vue-json-editor'
import { codemirror } from 'vue-codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/keymap/sublime' //sublime编辑器效果
import 'codemirror/theme/dracula.css' // 配置里面也需要theme设置为monokai
import 'codemirror/mode/sql/sql.js' // 配置里面也需要mode设置为vue
import 'codemirror/addon/selection/active-line'
import { fileUrl } from '/env'
import customCodeForm from './customCodeForm.vue'
import indexSetTree from './indexSetTree.vue'
import { pick } from 'lodash-es'
import { findNodeById } from '@/utils/util'
export default {
  name: 'ConfigBar',
  components: {
    vueJsonEditor,
    codemirror,
    customCodeForm,
    indexSetTree
  },
  props: {
    designData: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      fileUrl: fileUrl,
      cptDataFormShow: false,
      configTab: 'custom',
      dataLabels: ['数据', '接口地址', 'sql'],
      configBarShow: false,
      hasJsonFlag: true,
      cmOptions: {
        tabSize: 4, // tab的空格个数
        theme: 'dracula', //主题样式
        lineNumbers: true, //是否显示行数
        lineWrapping: true, //是否自动换行
        styleActiveLine: true, //line选择是是否加亮
        matchBrackets: true, //括号匹配
        mode: 'text/x-sparksql', //实现javascript代码高亮
        readOnly: false, //只读
        keyMap: 'default',
        extraKeys: { tab: 'autocomplete' },
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        hintOptions: {
          completeSingle: false,
          tables: {}
        }
      },
      jsData: '',
      cssData: '',
      specialComponents: [
        '按钮',
        '文字框',
        '图片',
        '轮播图',
        'iframe',
        '地图',
        '菜单',
        '空白容器',
        'flex容器',
        '画布容器'
      ],
      historyTimer: null // 防抖定时器
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, [
      'isModule',
      'currentCpt',
      'currentCptId',
      'componentData'
    ]),
    isCptOnOutsideLayer() {
      return this.componentData.some((item) => item.id === this.currentCptId)
    },
    dataPollEnable: {
      get() {
        return !!(
          this.currentCpt.cptOption.cptDataForm &&
          this.currentCpt.cptOption.cptDataForm.pollTime &&
          this.currentCpt.cptOption.cptDataForm.pollTime !== 0
        )
      },
      set(newValue) {
        if (newValue) {
          this.currentCpt.cptOption.cptDataForm.pollTime = 8
        } else {
          this.currentCpt.cptOption.cptDataForm.pollTime = 0
          this.refreshCptData(this.currentCpt.pid || null) //清除定时器
        }
        return newValue
      }
    },
    dataJson: {
      get() {
        if (isJSON(this.currentCpt.cptOption.cptDataForm.dataText)) {
          return JSON.parse(this.currentCpt.cptOption.cptDataForm.dataText)
        } else {
          return {}
        }
      },
      set(newValue) {
        this.currentCpt.cptOption.cptDataForm.dataText = JSON.stringify(newValue)
      }
    },
    popupComponentList() {
      return this.componentData.filter(
        (item) => item.cptKey.includes('container') && item.cptOption.attribute.isPopup
      )
    }
  },
  watch: {
    currentCptId(newVal) {
      this.configBarShow = !!newVal
    },
    currentCpt(newVal) {
      this.cptDataFormShow = false
      if (this.$refs.indexSetTree)
        this.$refs.indexSetTree.selectedKeys =
          newVal.cptDimensionList && newVal.cptDimensionList[0]
            ? [newVal.cptDimensionList[0].indexId]
            : []
      if (newVal.cptOption) {
        const { attribute, ...rest } = newVal.cptOption
        this.attribute = attribute
        this.jsData = `option = ${JSON.stringify(rest, null, 2)}`
          .replace(/"([^"]+)":/g, '$1:') // 移除键的双引号（如 "name": → name:）
          .replace(/:\s*"([^"]*)"/g, ": '$1'")
      }
      if (newVal.cptCssData || newVal.cptCssData === '') {
        this.cssData = JSON.stringify(newVal.cptCssData)
      }
      if (newVal && newVal.cptOption) {
        if (this.currentCpt.cptOption.cptDataForm && !this.currentCpt.cptData) {
          this.cptDataFormShow = true
        } else {
          this.configTab = 'custom' //解決上一組件沒有数据表单导致tab栏未选中bug
        }
      }
    },
    'designData.topicBackground': {
      handler(val) {
        this.$emit('changeTopicBg', val)
      }
    }
  },
  methods: {
    onJsonChange() {
      this.refreshCptData(this.currentCpt.pid || null)
    },
    // confirmCheck(fileUrl) {
    //   this.designData.topicBackground = fileUrl
    // },
    handleUpload() {
      // this.showGallery()
    },
    handleRemove(flag) {
      flag === 1 ? (this.designData.topicBackground = '') : (this.designData.topicIcon = '')
    },
    changeDataSource(val) {
      //静态数据不显示轮询按钮
      if (val === 1) {
        this.currentCpt.cptOption.cptDataForm.pollTime = 0
      }
    },
    // 刷新数据，调用父组件(index)中refreshCptData方法
    // 在父组件中再调用选中图层中的refreshCptData方法
    // 图层中的refreshCptData方法再自行调后端接口渲染数据，文本框的内容和数据类型组装在option.cptDataForm中
    refreshCptData(pid) {
      this.$emit('refreshCptData', pid)
    },
    showCptConfig() {
      this.configBarShow = true
    },
    handleCustomCode(customCode) {
      try {
        if (customCode.cssData) this.currentCpt.cptCssData = JSON.parse(customCode.cssData)
        if (customCode.jsData) {
          this.currentCpt.cptOption = {
            ...new Function('return ' + customCode.jsData)(),
            cptDataForm: this.currentCpt.cptOption.cptDataForm,
            attribute: this.attribute
          }
        }
      } catch (e) {}
    },
    setCssDataProperty(width, height) {
      if (this.currentCpt.cptCssData)
        this.cssData = JSON.stringify(Object.assign(this.currentCpt.cptCssData, { width, height }))
          .replace(/,/g, ',\n')
          .replace(/{(?!})/g, '{\n')
          .replace(/(?<!{)}/g, '\n$&')
    },
    changeCssData(cssJSONData) {
      if (cssJSONData) {
        try {
          const cssData = JSON.parse(cssJSONData)
          this.currentCpt.cptCssData = cssData
          if (cssData.width) this.currentCpt.cptWidth = cssData.width
          if (cssData.height) this.currentCpt.cptHeight = cssData.height
        } catch (e) {}
      }
    },
    changeScale() {
      if (this.currentCpt.cptKey !== 'cpt-popUp') return
      this.$emit('changePopUpScale', {
        id: this.currentCptId,
        height: this.currentCpt.cptHeight,
        width: this.currentCpt.cptWidth
      })
    },
    handleTableColumn(tableColumn) {
      this.$set(this.currentCpt, 'paramsToLoadDataWithoutBindingIndexSet', tableColumn)
      if (!this.currentCpt.cptOption.series) return
      while (this.currentCpt.cptOption.series.length < tableColumn.indexList.length) {
        this.currentCpt.cptOption.series.push({ ...this.currentCpt.cptOption.series[0] })
      }
      if (this.currentCpt.cptOption.series.length > tableColumn.indexList.length) {
        this.currentCpt.cptOption.series.splice(tableColumn.indexList.length)
      }
    },
    addEvent() {
      if (!this.currentCpt.events) {
        this.$set(this.currentCpt, 'events', [])
      }
      this.currentCpt.events.push({
        triggerType: 'click',
        responseType: 'link',
        linkType: 'external',
        linkUrl: '',
        jsCode: ''
      })
    },
    removeEvent(index) {
      this.currentCpt.events.splice(index, 1)
      if (this.currentCpt.events.length === 0) {
        this.$delete(this.currentCpt, 'events')
      }
    },
    // 防抖记录历史
    debounceRecordHistory(description) {
      if (this.historyTimer) {
        clearTimeout(this.historyTimer)
      }
      this.historyTimer = setTimeout(() => {
        this.$emit('recordHistory', description, this.currentCptId)
      }, 500) // 500ms 防抖
    },
    // 处理宽度变化
    handleWidthChange() {
      this.changeScale()
      this.debounceRecordHistory('修改组件宽度')
    },
    // 处理高度变化
    handleHeightChange() {
      this.changeScale()
      this.debounceRecordHistory('修改组件高度')
    },
    // 处理位置变化
    handlePositionChange() {
      this.debounceRecordHistory('修改组件位置')
    },
    // 处理层级变化
    handleZIndexChange() {
      this.debounceRecordHistory('修改组件层级')
    },
    showGallery() {
      this.$refs.gallery.opened()
    },
    confirmCheck(fileUrl) {
      this.designData.topicBackground = fileUrl
    },
    // 获取父容器尺寸
    getParentContainerSize() {
      try {
        const currentCpt = this.currentCpt
        if (!currentCpt) {
          throw new Error('当前组件不存在')
        }

        // 如果组件在最外层画布
        if (!currentCpt.pid) {
          return {
            width: this.designData.scaleX || 1920,
            height: this.designData.scaleY || 1080
          }
        }

        // 查找父容器
        const parentContainer = findNodeById(currentCpt.pid, this.componentData)
        if (!parentContainer) {
          throw new Error('父容器不存在')
        }

        // 根据容器类型获取尺寸
        return {
          width: parentContainer.cptWidth || parentContainer.cptOption?.attribute?.width || 400,
          height: parentContainer.cptHeight || parentContainer.cptOption?.attribute?.height || 300
        }
      } catch (error) {
        console.error('获取父容器尺寸失败:', error)
        return null
      }
    },
    // 水平居中对齐
    alignHorizontalCenter() {
      try {
        const containerSize = this.getParentContainerSize()
        if (!containerSize) return

        const newX = Math.max(0, (containerSize.width - this.currentCpt.cptWidth) / 2)
        this.currentCpt.cptX = Math.round(newX)

        this.debounceRecordHistory('水平居中对齐')
      } catch (error) {
        console.error('水平居中对齐失败:', error)
      }
    },
    // 垂直居中对齐
    alignVerticalCenter() {
      try {
        const containerSize = this.getParentContainerSize()
        if (!containerSize) return

        const newY = Math.max(0, (containerSize.height - this.currentCpt.cptHeight) / 2)
        this.currentCpt.cptY = Math.round(newY)

        this.debounceRecordHistory('垂直居中对齐')
      } catch (error) {
        console.error('垂直居中对齐失败:', error)
      }
    },
    // 完全居中对齐
    alignFullCenter() {
      try {
        const containerSize = this.getParentContainerSize()
        if (!containerSize) return

        const newX = Math.max(0, (containerSize.width - this.currentCpt.cptWidth) / 2)
        const newY = Math.max(0, (containerSize.height - this.currentCpt.cptHeight) / 2)

        this.currentCpt.cptX = Math.round(newX)
        this.currentCpt.cptY = Math.round(newY)

        this.debounceRecordHistory('完全居中对齐')
      } catch (error) {
        console.error('完全居中对齐失败:', error)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.config-bar {
  // padding: 10px;
  width: 100%;
  height: 100%;
  background: #f2f3f5;
  overflow: auto;
}
::v-deep .el-tabs__content {
  overflow: revert;
}
.cptTitle {
  line-height: 35px;
  text-align: center;
  background: #ffffff;
  color: rgb(51, 54, 57);
}
.customForm {
  padding: 0 6px 0 4px;
}
.event-item {
  margin-bottom: 10px;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: bold;
}
.uploadItem {
  width: 120px;
  height: 120px;
  text-align: center;
  line-height: 120px;
  border: 1px solid #ddd;
  cursor: pointer;
}
::v-deep .menu-input .ant-input {
  min-width: 50px;
}
::v-deep .map-input .ant-input {
  min-width: 50px;
}
</style>
