<template>
  <div
    :id="element.cptOption.attribute.name"
    class="shape"
    :class="{ active: active, selected: isSelected, 'multi-selected': isMultiSelected }"
    @mousedown="handleMouseDown"
  >
    <template v-if="!isPreview">
      <div
        v-for="(item, index) in active ? pointList : []"
        :key="index"
        class="shape-point"
        :style="getPointStyle(item)"
        @mousedown="handleMouseDownOnPoint($event, item)"
      ></div>
    </template>
    <slot></slot>
  </div>
</template>

<script>
import { mapWritableState, mapActions } from 'pinia'
import { useDesignerStore } from '@/stores/designer'

export default {
  props: {
    active: {
      type: Boolean,
      default: false
    },
    containerScale: {
      type: Number,
      default: 1
    },
    element: {
      type: Object,
      default: () => ({})
    },
    isPreview: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pointList: ['t', 'r', 'b', 'l', 'lt', 'rt', 'lb', 'rb'],
      directionKey: {
        // 光标显示样式
        t: 'n',
        b: 's',
        l: 'w',
        r: 'e'
      }
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCptId', 'currentCpt', 'componentData']),
    designerStore() {
      return useDesignerStore()
    },
    isSelected() {
      return this.designerStore.selectedComponentIds.includes(this.element.id)
    },
    isMultiSelected() {
      return this.designerStore.selectedComponentIds.length > 1 && this.isSelected
    }
  },
  mounted() {},
  methods: {
    ...mapActions(useDesignerStore, [
      'setSelectedComponentIds',
      'addSelectedComponentId',
      'removeSelectedComponentId',
      'clearSelectedComponents'
    ]),
    getPointStyle(point) {
      const width = this.element.cptWidth
      const height = this.element.cptHeight
      const hasT = /t/.test(point)
      const hasB = /b/.test(point)
      const hasL = /l/.test(point)
      const hasR = /r/.test(point)
      let newLeft = 0
      let newTop = 0

      // 四个角的点
      if (point.length === 2) {
        newLeft = hasL ? 0 : width
        newTop = hasT ? 0 : height
      } else {
        // 上下两点的点，宽度居中
        if (hasT || hasB) {
          newLeft = Math.round(width / 2)
          newTop = hasT ? 0 : height
        }

        // 左右两边的点，高度居中
        if (hasL || hasR) {
          newLeft = hasL ? 0 : width
          newTop = Math.round(height / 2)
        }
      }

      const style = {
        marginLeft: '-4px',
        marginTop: '-4px',
        left: `${newLeft}px`,
        top: `${newTop}px`,
        cursor:
          point
            .split('')
            .reverse()
            .map((m) => this.directionKey[m])
            .join('') + '-resize'
      }

      return style
    },

    handleMouseDown(e) {
      if (this.isPreview) return
      e.stopPropagation()

      // 处理多选逻辑
      if (e.ctrlKey || e.metaKey) {
        // Ctrl+点击切换选择状态
        if (this.isSelected) {
          this.designerStore.removeSelectedComponentId(this.element.id)
        } else {
          this.designerStore.addSelectedComponentId(this.element.id)
        }
        return
      }

      // 如果点击的组件不在选中列表中，清除之前的选择
      if (!this.isSelected) {
        this.designerStore.clearSelectedComponents()
        this.designerStore.setSelectedComponentIds([this.element.id])
      }

      this.currentCpt = this.element
      this.currentCptId = this.element.id

      const startY = e.clientY
      const startX = e.clientX
      const startTop = Number(this.element.cptY)
      const startLeft = Number(this.element.cptX)
      let hasMoved = false // 标记是否真正移动了

      // 记录所有选中组件的初始位置
      const selectedComponents = this.designerStore.selectedComponentIds
        .map((id) => {
          const comp = this.componentData.find((c) => c.id === id)
          return comp
            ? {
                id: comp.id,
                startX: comp.cptX,
                startY: comp.cptY,
                component: comp
              }
            : null
        })
        .filter(Boolean)

      const move = (moveEvent) => {
        hasMoved = true // 标记已经移动
        const currX = moveEvent.clientX
        const currY = moveEvent.clientY

        // 根据容器缩放比例调整移动距离
        const deltaX = Math.round((currX - startX) / this.containerScale)
        const deltaY = Math.round((currY - startY) / this.containerScale)

        // 如果有多个选中的组件，同时移动所有组件
        if (selectedComponents.length > 1) {
          selectedComponents.forEach((item) => {
            item.component.cptX = item.startX + deltaX
            item.component.cptY = item.startY + deltaY
          })
        } else {
          this.currentCpt.cptX = startLeft + deltaX
          this.currentCpt.cptY = startTop + deltaY
        }

        this.$nextTick(() => {
          // 触发元素移动事件，用于显示标线、吸附功能
          // 后面两个参数代表鼠标移动方向
          // currY - startY > 0 true 表示向下移动 false 表示向上移动
          // currX - startX > 0 true 表示向右移动 false 表示向左移动
          this.$bus.$emit('move', this.currentCpt.pid, currY - startY > 0, currX - startX > 0)
        })
      }

      const up = () => {
        // 触发元素停止移动事件，用于隐藏标线
        this.$bus.$emit('unmove')
        document.removeEventListener('mousemove', move)
        document.removeEventListener('mouseup', up)

        // 只有在真正移动了才触发 dragstop 事件
        if (hasMoved) {
          this.$emit('dragstop', this.element) // 通知父组件拖拽结束
        }
      }

      document.addEventListener('mousemove', move)
      document.addEventListener('mouseup', up)
    },

    handleMouseDownOnPoint(downEvent, point) {
      if (this.isPreview) return
      downEvent.stopPropagation()
      downEvent.preventDefault()
      const height = Number(this.element.cptHeight)
      const width = Number(this.element.cptWidth)
      const top = Number(this.element.cptY)
      const left = Number(this.element.cptX)
      const startX = downEvent.clientX
      const startY = downEvent.clientY
      let hasResized = false // 标记是否真正调整了大小

      const move = (moveEvent) => {
        hasResized = true // 标记已经调整大小
        const currX = moveEvent.clientX
        const currY = moveEvent.clientY

        // 根据容器缩放比例调整移动距离
        const disY = Math.round((currY - startY) / this.containerScale)
        const disX = Math.round((currX - startX) / this.containerScale)
        const hasT = /t/.test(point)
        const hasB = /b/.test(point)
        const hasL = /l/.test(point)
        const hasR = /r/.test(point)
        const newHeight = height + (hasT ? -disY : hasB ? disY : 0)
        const newWidth = width + (hasL ? -disX : hasR ? disX : 0)

        this.currentCpt.cptHeight = newHeight > 0 ? newHeight : 0
        this.currentCpt.cptWidth = newWidth > 0 ? newWidth : 0
        this.currentCpt.cptX = left + (hasL ? disX : 0)
        this.currentCpt.cptY = top + (hasT ? disY : 0)
        this.$nextTick(() => {
          // 触发元素移动事件，用于显示标线、吸附功能
          // 后面两个参数代表鼠标移动方向
          // currY - startY > 0 true 表示向下移动 false 表示向上移动
          // currX - startX > 0 true 表示向右移动 false 表示向左移动
          this.$bus.$emit('move', this.currentCpt.pid, currY - startY > 0, currX - startX > 0)
        })
      }

      const up = () => {
        document.removeEventListener('mousemove', move)
        document.removeEventListener('mouseup', up)

        // 只有在真正调整了大小才触发 resizestop 事件
        if (hasResized) {
          this.$emit('resizestop', this.element) // 通知父组件缩放结束
        }
      }

      document.addEventListener('mousemove', move)
      document.addEventListener('mouseup', up)
    }
  }
}
</script>

<style lang="less" scoped>
.shape {
  position: absolute;
  scroll-margin-top: 20px;
}
.active {
  outline: 1px dashed #70c0ff;
  outline-offset: 1px;
}
.selected {
  outline: 2px solid #409eff;
  outline-offset: 1px;
}
.multi-selected {
  outline: 2px solid #67c23a;
  outline-offset: 1px;
}
.shape-point {
  position: absolute;
  background: #fff;
  border: 1px solid #59c7f9;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
</style>
