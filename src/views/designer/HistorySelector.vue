<template>
  <div class="history-selector">
    <a-dropdown
      :trigger="['click']"
      placement="topLeft"
      :visible="dropdownVisible"
      @visibleChange="handleVisibleChange"
    >
      <a-button type="link" size="small" class="history-button">
        <i class="el-icon-time"></i>
        历史记录
        <i class="el-icon-caret-top"></i>
      </a-button>
      <template slot="overlay">
        <div class="history-menu">
          <div class="history-header">
            <span>历史记录</span>
            <span class="history-count">({{ formattedHistory.length }})</span>
          </div>
          <div class="history-divider"></div>
          <div class="history-list">
            <div
              v-for="item in reversedFormattedHistory"
              :key="item.originalIndex"
              :class="[
                'history-item',
                { 'current-history': item.originalIndex === currentHistoryIndex }
              ]"
              @click="handleHistoryItemClick(item.originalIndex)"
            >
              <div class="history-option">
                <div class="history-main">
                  <div class="history-description">{{ item.description }}</div>
                  <div v-if="item.componentInfo" class="history-component">
                    {{ item.componentInfo }}
                  </div>
                </div>
                <div class="history-time">{{ item.timeStr }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </a-dropdown>
    <a-tooltip title="历史记录最多保留10条，按时间倒序排列，最新的在上面" placement="top">
      <i class="el-icon-question help-icon"></i>
    </a-tooltip>
  </div>
</template>

<script>
import { mapState, mapActions } from 'pinia'
import { useDesignerStore } from '@/stores/designer'

export default {
  name: 'HistorySelector',
  data() {
    return {
      selectedHistoryIndex: null,
      dropdownVisible: false
    }
  },
  computed: {
    ...mapState(useDesignerStore, ['history', 'historyIndex']),
    currentHistoryIndex() {
      return this.historyIndex
    },
    formattedHistory() {
      return this.history.map((item, index) => {
        const date = new Date(item.timestamp)
        const timeStr = `${date.getHours().toString().padStart(2, '0')}:${date
          .getMinutes()
          .toString()
          .padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`

        // 获取组件信息
        let componentInfo = ''
        let enhancedDescription = item.description || '操作'

        // 优先使用 componentIds 数组（支持多组件）
        if (item.componentIds && item.componentIds.length > 0 && item.componentData) {
          if (item.componentIds.length === 1) {
            // 单个组件
            const targetComponent = item.componentData.find(
              (comp) => comp.id === item.componentIds[0]
            )
            if (targetComponent) {
              componentInfo = `${targetComponent.cptTitle}#${targetComponent.id}`
              enhancedDescription = `${item.description || '操作'}`
            }
          } else {
            // 多个组件
            const targetComponents = item.componentData.filter((comp) =>
              item.componentIds.includes(comp.id)
            )
            if (targetComponents.length > 0) {
              componentInfo = `${targetComponents.length}个组件: ${targetComponents
                .map((c) => c.cptTitle)
                .join(', ')}`
              enhancedDescription = `${item.description || '操作'}`
            }
          }
        } else if (item.componentId && item.componentData) {
          // 兼容旧的单组件ID格式
          const targetComponent = item.componentData.find((comp) => comp.id === item.componentId)
          if (targetComponent) {
            componentInfo = `${targetComponent.cptTitle}#${targetComponent.id}`
            enhancedDescription = `${item.description || '操作'}`
          }
        } else if (item.componentData && item.componentData.length > 0) {
          const componentCount = item.componentData.length
          componentInfo = `${componentCount}个组件`

          // 如果是单个组件操作，尝试获取具体组件信息
          if (index > 0 && this.history[index - 1]) {
            const prevComponentData = this.history[index - 1].componentData || []
            const currentComponentData = item.componentData || []

            if (currentComponentData.length > prevComponentData.length) {
              // 新增组件
              const newComponent = currentComponentData[currentComponentData.length - 1]
              if (newComponent && newComponent.cptTitle) {
                componentInfo = `${newComponent.cptTitle}#${newComponent.id}`
                enhancedDescription = `${item.description || '操作'}`
              }
            } else if (currentComponentData.length < prevComponentData.length) {
              // 删除组件 - 找出被删除的组件
              const deletedComponent = prevComponentData.find(
                (prev) => !currentComponentData.some((curr) => curr.id === prev.id)
              )
              if (deletedComponent) {
                componentInfo = `${deletedComponent.cptTitle}#${deletedComponent.id}`
                enhancedDescription = `${item.description || '操作'}`
              }
            } else {
              // 修改组件 - 尝试找到最近修改的组件
              const recentComponent = currentComponentData[currentComponentData.length - 1]
              if (recentComponent) {
                componentInfo = `${recentComponent.cptTitle}#${recentComponent.id}`
                enhancedDescription = `${item.description || '操作'}`
              }
            }
          }
        }

        return {
          description: enhancedDescription,
          componentInfo,
          timeStr,
          originalIndex: index
        }
      })
    },
    // 倒序展示历史记录（最新的在上面）
    reversedFormattedHistory() {
      return [...this.formattedHistory].reverse()
    }
  },
  watch: {
    historyIndex(newIndex) {
      this.selectedHistoryIndex = newIndex
    }
  },
  mounted() {
    this.selectedHistoryIndex = this.historyIndex
  },
  methods: {
    ...mapActions(useDesignerStore, ['jumpToHistory']),
    handleVisibleChange(visible) {
      this.dropdownVisible = visible
    },
    handleHistoryItemClick(index) {
      if (index !== this.historyIndex) {
        this.jumpToHistory(index)
      }
      this.dropdownVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
.history-selector {
  display: flex;
  align-items: center;
  gap: 6px;

  .history-button {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #666;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background-color: #f5f5f5;
      color: #1890ff;
    }

    .el-icon-time {
      font-size: 14px;
    }

    .el-icon-caret-top {
      font-size: 12px;
      transition: transform 0.2s;
    }
  }

  .help-icon {
    font-size: 14px;
    color: #999;
    cursor: help;
    transition: color 0.2s;

    &:hover {
      color: #1890ff;
    }
  }
}

.history-menu {
  min-width: 350px;
  max-width: 400px;
  max-height: 400px;
  overflow-y: auto;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  border: 1px solid #d9d9d9;

  .history-header {
    padding: 12px 16px 8px;
    font-size: 14px;
    font-weight: 600;
    color: #333;

    .history-count {
      color: #999;
      font-weight: normal;
      font-size: 12px;
    }
  }

  .history-divider {
    height: 1px;
    background: #f0f0f0;
    margin: 0;
  }

  .history-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .history-item {
    padding: 8px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f5f5f5;
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f8f9fa;
    }

    &.current-history {
      background-color: #e6f7ff;
      border-left: 3px solid #1890ff;

      .history-description {
        color: #1890ff;
        font-weight: 600;
      }

      .history-component {
        background: #d1f2ff;
        color: #1890ff;
      }
    }
  }
}

.history-option {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  gap: 8px;

  .history-main {
    flex: 1;
    min-width: 0;

    .history-description {
      font-size: 12px;
      color: #333;
      font-weight: 500;
      margin-bottom: 2px;
      word-break: break-word;
    }

    .history-component {
      font-size: 11px;
      color: #666;
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 3px;
      display: inline-block;
    }
  }

  .history-time {
    font-size: 11px;
    color: #999;
    white-space: nowrap;
    margin-top: 1px;
  }
}
</style>
