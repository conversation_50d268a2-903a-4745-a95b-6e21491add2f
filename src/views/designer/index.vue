<template>
  <div>
    <DesignerHeader
      @outBlur="outBlur"
      @preview="preview"
      @submitDesign="submitDesign"
      @clearDesign="clearDesign"
      @exportCommand="exportCommand"
      @importDesign="importDesign"
      @previewConfig="previewConfig"
    />
    <main @click.self="outBlur">
      <!--左侧组件栏-->
      <ComponentBar
        :style="{ width: cptBarWidth + 'px' }"
        @dragStart="dragStart"
        @dragStartNew="dragStartNew"
        @showConfigBar="showConfigBar"
        @hideCpt="hideCpt"
        @copyCpt="copyCpt"
        @delCpt="(item) => delCpt(item.id)"
      />
      <div class="canvas-container">
        <div
          id="chart-edit-layout"
          style="position: relative"
          :style="{ width: windowWidth - cptBarWidth - configBarWidth + 'px' }"
          @click.self="outBlur"
        >
          <!-- 新的标尺组件 - 固定定位，不随内容滚动 -->
          <DesignerRuler
            :canvas-width="designData.scaleX"
            :canvas-height="designData.scaleY"
            :scale="containerScale"
            :container-width="windowWidth - cptBarWidth - configBarWidth"
            :container-height="windowHeight - 100"
            :thick="15"
            :scroll-left="scrollLeft"
            :scroll-top="scrollTop"
          />
          <!-- 可滚动的内容区域 -->
          <div
            ref="scrollableContent"
            class="scrollable-content"
            style="position: absolute; top: 15px; left: 15px; right: 0; bottom: 0; overflow: auto"
            @scroll="handleScroll"
          >
            <div
              id="chart-edit-container"
              ref="webContainer"
              class="canvas"
              :style="{
                width: designData.scaleX + 'px',
                height: designData.scaleY + 'px',
                backgroundImage: designData.topicBackground
                  ? 'url(' + getFileAccessHttpUrl(designData.topicBackground) + ')'
                  : designData.defaultBgImage
                  ? 'url(' + getFileAccessHttpUrl(designData.defaultBgImage) + ')'
                  : 'none',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center center',
                backgroundSize: '100% 100%',
                backgroundColor: designData.bgColor,

                transform: 'scale(' + containerScale + ')'
              }"
              @dragover.prevent="allowDrop"
              @drop.prevent="drop"
              @click.self="outBlur"
              @mousedown="handleCanvasMouseDown"
              @contextmenu="handleCanvasContextMenu"
            >
              <Shape
                v-for="item in componentData"
                v-show="item.cptOption.attribute.visible"
                :key="item.id"
                class="cptDiv"
                :style="{
                  width: Math.round(item.cptWidth) + 'px',
                  height: Math.round(item.cptHeight) + 'px',
                  top: Math.round(item.cptY) + 'px',
                  left: Math.round(item.cptX) + 'px',
                  zIndex:
                    currentCptId === item.id || designerStore.selectedComponentIds.includes(item.id)
                      ? 1800
                      : item.cptZ
                }"
                :element="item"
                :containerScale="containerScale"
                :active="currentCptId === item.id"
                :isPreview="false"
                @dragstop="handleShapeOperationEnd(item)"
                @resizestop="handleShapeOperationEnd(item)"
                @contextmenu.native.prevent="handleComponentContextMenu($event, item)"
              >
                <div class="component-container" style="width: 100%; height: 100%">
                  <component
                    :is="item.cptKey"
                    :id="item.id"
                    :ref="item.id"
                    :children="item.children"
                    :indexId="item.cptIndexId"
                    :paramsForUnBindingComp="item.paramsToLoadDataWithoutBindingIndexSet"
                    :width="Math.round(item.cptWidth)"
                    :height="Math.round(item.cptHeight)"
                    :option="item.cptOption"
                    :containerScale="containerScale"
                    @showConfigBar="(cpt) => showConfigBar({}, cpt)"
                  />
                </div>
              </Shape>
              <MarkLine
                v-show="componentData.findIndex((item) => item.id === currentCptId) !== -1"
              />
              <!-- 框选矩形 -->
              <div
                v-show="designerStore.selectionBox.visible"
                class="selection-box"
                :style="{
                  left: designerStore.selectionBox.startX + 'px',
                  top: designerStore.selectionBox.startY + 'px',
                  width: designerStore.selectionBox.endX - designerStore.selectionBox.startX + 'px',
                  height: designerStore.selectionBox.endY - designerStore.selectionBox.startY + 'px'
                }"
              ></div>
              <!-- 框选数量提示 -->
              <div
                v-show="designerStore.selectionBox.visible && selectionPreviewCount > 0"
                class="selection-count-tip"
                :style="{
                  left: designerStore.selectionBox.endX + 10 + 'px',
                  top: designerStore.selectionBox.startY + 'px'
                }"
              >
                已选中 {{ selectionPreviewCount }} 个组件
              </div>
            </div>
          </div>
        </div>
        <!-- 关闭可滚动内容区域 -->
        <div class="bottom">
          <el-slider v-model="containerScale" :min="0.3" :max="2" :step="0.01" />
          <el-select v-model="containerScalePercent" size="mini">
            <el-option
              v-for="item in containerScaleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <a-button type="link" icon="undo" :disabled="!canUndo" @click="handleUndo">
            撤销
          </a-button>
          <a-button type="link" icon="redo" :disabled="!canRedo" @click="handleRedo">
            重做
          </a-button>
          <HistorySelector />
        </div>
      </div>
      <!--右侧属性栏-->
      <ConfigBar
        ref="configBar"
        :style="{ width: configBarWidth - 6 + 'px', marginLeft: 'auto' }"
        :designData.sync="designData"
        :height="windowHeight"
        @refreshCptData="refreshCptData"
        @changeTopicBg="changeTopicBg"
        @recordHistory="handleRecordHistory"
      />
    </main>
    <input v-show="false" id="files" ref="refFile" type="file" accept=".json" @change="fileLoad" />
    <ConfigPreviewDrawer ref="configPreviewDrawer" />
    <!-- 右键菜单 -->
    <Contextmenu
      :visible.sync="contextMenuVisible"
      :itemList="contextMenuItems"
      @select="handleContextMenuSelect"
      @update:visible="handleContextMenuVisibleChange"
    />
  </div>
</template>

<script>
import { mapWritableState, mapActions, mapState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import ComponentBar from './componentBar.vue'
import DesignerHeader from './DesignerHeader.vue'
import ConfigPreviewDrawer from './ConfigPreviewDrawer.vue'
import ConfigBar from './configBar.vue'
import html2canvas from 'html2canvas'
import { fileDownload, base64toFile } from '@/utils/FileUtil'
import { clearCptInterval } from '@/utils/refreshCptData'
import DesignerRuler from './components/DesignerRuler.vue'
import Shape from './Shape.vue'
import { routerTurnByPath } from '@/utils/router'
import { getAction, httpAction, postAction, uploadAction, getFileAccessHttpUrl } from '@/api/manage'
import { handleSpecialForm } from './components/handleSpecialForm'
import cptOptions from '@/components/options'
import { chartItem } from './components/constant.js'
import MarkLine from './MarkLine.vue'
import HistorySelector from './HistorySelector.vue'
import Contextmenu from '@/components/menu/Contextmenu.vue'
import { traverseTree } from '@/api/api'
import { cloneDeep } from 'lodash-es'
export default {
  name: 'DesignIndex',
  components: {
    DesignerHeader,
    DesignerRuler,
    ConfigBar,
    ComponentBar,
    ConfigPreviewDrawer,
    Shape,
    MarkLine,
    HistorySelector,
    Contextmenu
  },
  data() {
    return {
      isAdd: true,
      containerScale: 1,
      windowWidth: 0,
      windowHeight: 0,
      scrollLeft: 0,
      scrollTop: 0,
      cptBarWidth: 300,
      configBarWidth: 400,
      copyDom: null,
      dragNodeId: '',
      justFinishedSelection: false, // 标记是否刚完成框选
      defaultResourceUrl: {
        // 工作台配置的默认资源地址
        mapComponent: '',
        menuComponent: '',
        bgImage: ''
      },
      cacheChoices: {},
      cacheChoicesFixed: {}, //记录移动前选中组件的位置 自定义事件内部无法处理，放在了外面。
      cptOptions,
      chartItem,
      url: {
        previewData: '/index/scCockpitIndexGroupConfig/previewData',
        queryIndexSet: '/module/scCockpitModuleConfig/queryByIndexId',
        addTopic: '/topic/scCockpitTopicInfo/add',
        editTopic: '/topic/scCockpitTopicInfo/edit',
        addModule: '/module/scCockpitModuleConfig/add',
        editModule: '/module/scCockpitModuleConfig/edit',
        queryTopic: '/topic/scCockpitTopicInfo/queryById',
        queryModule: '/module/scCockpitModuleConfig/queryById',
        queryWorkBench: '/workbench/scCockpitWorkbench/queryById'
      },
      containerScaleOptions: [
        // { label: '自适应', value: 'auto-fit' },
        { label: '200%', value: 2 },
        { label: '150%', value: 1.5 },
        { label: '100%', value: 1 },
        { label: '50%', value: 0.5 }
      ],
      // 右键菜单相关
      contextMenuVisible: false,
      contextMenuTarget: null,
      containerMethods: null,
      contextMenuItems: [
        { key: 'toggle-visibility', icon: 'eye', text: '切换显示/隐藏' },
        { key: 'copy', icon: 'copy', text: '复制组件' },
        { key: 'delete', icon: 'delete', text: '删除组件' }
      ],
      // 框选事件监听器引用
      selectionMouseMoveHandler: null,
      selectionMouseUpHandler: null,
      // 框选预览数量
      selectionPreviewCount: 0
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, [
      'isModule',
      'componentData',
      'currentCpt',
      'currentCptId',
      'designData',
      'allRole',
      'indexSetTree'
    ]),
    ...mapState(useDesignerStore, ['canUndo', 'canRedo']),
    containerScalePercent: {
      get() {
        return Math.round(this.containerScale * 100) + '%'
      },
      set(value) {
        if (value === 'auto-fit') {
          this.initContainerSize()
        } else {
          this.containerScale = parseFloat(value)
        }
      }
    }
  },
  watch: {
    designData: {
      handler(newValue, oldValue) {
        if (this.designerStore.isRestoring) {
          return
        }

        if (this.designerStore.historyIndex === 0 && this.designerStore.history.length === 1) {
          return
        }

        if (oldValue && JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
          this.pushHistory('修改设计')
        } else if (!oldValue && this.designerStore.history.length > 0) {
          if (this.designerStore.historyIndex > 0) {
            this.pushHistory('修改设计')
          }
        }
      },
      deep: true
    }
  },
  created() {
    this.designerStore = useDesignerStore()
    console.log('Designer store instance:', this.designerStore)
    console.log('Initial selectedComponentIds:', this.designerStore.selectedComponentIds)
    this.loadCacheData()
    this.loadRoleList()
    this.loadIndexSetTree()
  },
  mounted() {
    const that = this
    this.$nextTick(() => {
      const canvasDom = document.getElementsByClassName('canvas-container')[0]
      if (canvasDom) {
        canvasDom.addEventListener('wheel', (event) => {
          if (event.ctrlKey) {
            event.preventDefault()
            const delta = Math.sign(event.deltaY)
            if (delta > 0) {
              this.containerScale -= 0.1
            } else {
              this.containerScale += 0.1
            }
          }
        })
      }
    })
    // window.onresize = () => {
    //   return (() => {
    //     that.initContainerSize()
    //   })()
    // }
    window.addEventListener('keydown', this.handleKeyDown)
    this.initContainerContextMenu()
  },
  beforeUnmount() {
    window.removeEventListener('keydown', this.handleKeyDown)
    // 移除右键菜单相关的事件监听器
    document.removeEventListener('container-component-contextmenu', this.handleContainerContextMenu)
    // 清理框选相关的事件监听器
    this.clearSelectionState()
  },
  methods: {
    ...mapActions(useDesignerStore, [
      'pushHistory',
      'undo',
      'redo',
      'jumpToHistory',
      'setInitialState',
      'setDesignData',
      'setComponentData',
      'deleteComponent',
      'addComponent',
      'updateComponent',
      'setSelectedComponentIds',
      'addSelectedComponentId',
      'removeSelectedComponentId',
      'clearSelectedComponents',
      'setMultiSelecting',
      'updateSelectionBox',
      'hideSelectionBox',
      'moveSelectedComponents'
    ]),
    // 处理滚动事件，更新标尺显示
    handleScroll(event) {
      // 暂时使用简单的滚动位置，不进行复杂计算
      const scrollLeft = event.target.scrollLeft
      const scrollTop = event.target.scrollTop

      // 直接使用滚动位置，不除以缩放比例
      this.scrollLeft = scrollLeft
      this.scrollTop = scrollTop
    },
    handleKeyDown(event) {
      // 检查当前焦点是否在输入框、文本域或可编辑元素内
      const activeElement = document.activeElement
      const isInputFocused =
        activeElement &&
        (activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.contentEditable === 'true' ||
          activeElement.classList.contains('el-input__inner') ||
          activeElement.classList.contains('ant-input'))

      if (event.ctrlKey || event.metaKey) {
        if (event.key === 'z' || event.key === 'Z') {
          event.preventDefault()
          if (event.shiftKey) {
            this.handleRedo()
          } else {
            this.handleUndo()
          }
        } else if (event.key === 'y' || event.key === 'Y') {
          event.preventDefault()
          this.handleRedo()
        } else if (event.key === 'a' || event.key === 'A') {
          // Ctrl+A 全选所有可见组件
          event.preventDefault()
          this.handleSelectAll()
        }
      } else if (event.key === 'Delete') {
        // 如果焦点在输入框内，不拦截Delete键
        if (!isInputFocused) {
          event.preventDefault()
          this.handleDeleteSelected()
        }
      } else if (event.key === 'Escape') {
        // Escape键取消选择
        event.preventDefault()
        this.handleCancelSelection()
      } else if (event.key === 'ArrowUp' && this.currentCpt.cptY) {
        // 如果焦点在输入框内，不拦截方向键
        if (!isInputFocused) {
          event.preventDefault()
          this.currentCpt.cptY -= 1
        }
      } else if (event.key === 'ArrowDown' && this.currentCpt.cptY) {
        // 如果焦点在输入框内，不拦截方向键
        if (!isInputFocused) {
          event.preventDefault()
          this.currentCpt.cptY += 1
        }
      } else if (event.key === 'ArrowLeft' && this.currentCpt.cptX) {
        // 如果焦点在输入框内，不拦截左右方向键
        if (!isInputFocused) {
          event.preventDefault()
          this.currentCpt.cptX -= 1
        }
      } else if (event.key === 'ArrowRight' && this.currentCpt.cptX) {
        // 如果焦点在输入框内，不拦截左右方向键
        if (!isInputFocused) {
          event.preventDefault()
          this.currentCpt.cptX += 1
        }
      }
    },
    handleUndo() {
      if (this.designerStore.canUndo) {
        this.undo()
      }
    },
    handleRedo() {
      if (this.designerStore.canRedo) {
        this.redo()
      }
    },
    initContainerSize() {
      this.windowWidth = document.documentElement.clientWidth
      this.windowHeight = document.documentElement.clientHeight
      const scaleMarkWidth = 20
      let tempWidth = this.windowWidth - this.cptBarWidth - this.configBarWidth - scaleMarkWidth
      this.containerScale = 1
    },
    previewConfig() {
      const data = JSON.stringify({ ...this.designData, components: this.componentData }, null, 2)
      this.$refs.configPreviewDrawer.showDrawer(data)
    },
    exportCommand(command) {
      if (command === 'img') {
        html2canvas(this.$refs.webContainer, { backgroundColor: '#49586e' }).then((canvas) => {
          const canvasData = canvas.toDataURL('image/jpeg')
          fileDownload(canvasData, this.designData.topicName + '.png')
        })
      } else if (command === 'json') {
        const data = JSON.stringify({ ...this.designData, components: this.componentData }, null, 2)
        let uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(data)
        fileDownload(uri, this.designData.topicName + '.json')
      }
    },
    importDesign() {
      this.$refs.refFile.dispatchEvent(new MouseEvent('click'))
    },
    fileLoad() {
      const that = this
      const selectedFile = this.$refs.refFile.files[0]
      const reader = new FileReader()
      reader.readAsText(selectedFile)
      reader.onload = function () {
        const fileJson = JSON.parse(this.result)
        fileJson.id = that.designData.id
        that.designData = fileJson
        that.componentData = fileJson.components
        that.$message.success('导入成功')
      }
      this.$refs.refFile.value = ''
    },
    clearDesign() {
      this.$confirm({
        title: '警告',
        content: '此操作将会清空图层，是否继续？',
        okText: '确定',
        cancelText: '取消',
        icon: 'warning',
        onOk: () => {
          const componentCount = this.componentData.length
          this.designerStore.setComponentData([])
          this.designerStore.setCurrentCpt({ cptOption: undefined })
          this.designerStore.setCurrentCptId('')
          localStorage.removeItem('designCache')
          clearCptInterval(null, true)
          this.$message.success(`已清空设计，删除了 ${componentCount} 个组件`)
          this.pushHistory('清空设计')
        }
      })
    },
    loadRoleList() {
      getAction('/sys/role/list').then((res) => {
        this.allRole = res.result.records
      })
    },
    loadIndexSetTree() {
      getAction('/group/scCockpitGroup/queryByType?groupType=index').then((res) => {
        if (res.success) {
          let treeDataInit = res.result
          treeDataInit.forEach((node) => {
            traverseTree(node, (node) => {
              node.disabled = !!node.groupType
              if (node.data) {
                // 将指标集data进行字段转换，并push到childList中，保留其type字段
                node.data.forEach((data) => {
                  let obj = data
                  obj.groupName = data.name
                  delete obj.name
                  if (!node.childList) node.childList = [obj]
                  else node.childList.push(obj)
                })
                delete node.data
              }
            })
          })
          this.indexSetTree = treeDataInit
        }
      })
    },
    async loadCacheData(topicId = '') {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const id = topicId || this.$route.params.topicId
      const workbenchId = this.$route.params.workbenchId
      this.designerStore.setIsModule(this.$route.params.type === 'module')

      let initialDesignData = {
        id: '',
        topicName: '我的大屏',
        topicDescription: '',
        scaleX: 1920,
        scaleY: 1080,
        topicBackground: '',
        topicIcon: '',
        topicUrl: '',
        topicCode: '',
        bgColor: '#DDEEFE',
        status: 1,
        defaultBgImage: '',
        defaultMapUrl: '',
        defaultMenuUrl: '',
        topicTemplate: 0
      }
      let initialComponentData = []

      if (this.isModule) {
        initialDesignData = {
          id: '',
          moduleDes: '',
          scaleX: 1920,
          scaleY: 1080,
          bgColor: 'rgba(255,255,255)',
          defaultBgImage: '',
          defaultMapUrl: '',
          defaultMenuUrl: '',
          topicBackground: ''
        }
      }

      try {
        // 先执行工作台查询
        if (workbenchId) {
          const workbenchRes = await getAction(this.url.queryWorkBench, { id: workbenchId })
          if (workbenchRes.success) {
            initialDesignData = Object.assign({}, initialDesignData, {
              defaultMapUrl: workbenchRes.result.mapComponent,
              defaultMenuUrl: workbenchRes.result.menuComponent,
              defaultBgImage: workbenchRes.result.bgImage,
              scaleX: workbenchRes.result.screenWidth,
              scaleY: workbenchRes.result.screenHeight
            })
          }
        }

        // 再执行专题/模块查询
        if (id) {
          const url = this.isModule ? this.url.queryModule : this.url.queryTopic
          const topicRes = await getAction(url, { id })
          if (topicRes.success) {
            if (!this.isModule) {
              const { topicJson, ...rest } = topicRes.result
              const topicInfo = JSON.parse(topicJson)
              initialDesignData = Object.assign({}, initialDesignData, rest)
              document.title = initialDesignData.topicName
              initialComponentData = topicInfo?.components || []
            } else {
              const { moduleData, moduleWidth, moduleLength, ...rest } = topicRes.result
              const moduleInfo = JSON.parse(moduleData)
              initialDesignData = Object.assign({}, initialDesignData, {
                ...rest,
                scaleX: moduleWidth,
                scaleY: moduleLength
              })
              document.title = initialDesignData.moduleName
              initialComponentData = moduleInfo?.components || []
            }
          } else if (id.length !== 32) {
            this.$message.warning('专题信息查询失败')
          }
        }
      } catch (error) {
        console.error('加载数据时发生错误:', error)
      } finally {
        this.designerStore.setDesignData(initialDesignData)
        this.designerStore.setComponentData(initialComponentData)

        if (workbenchId) {
          this.loadDefaultComp()
        }

        this.setInitialState(this.designData, this.componentData)

        loading.close()
        this.initContainerSize()
      }
    },
    loadDefaultComp() {
      if (
        this.designData.defaultMapUrl &&
        !this.componentData.some((component) => {
          return component.cptKey === 'cpt-map'
        })
      ) {
        const mapComponent = {
          cptTitle: '地图',
          icon: 'map',
          cptKey: 'cpt-map',
          cptOptionKey: 'cpt-map-option',
          cptOption: {
            attribute: {
              name: '地图',
              className: 'default-class',
              url: this.designData.defaultMapUrl,
              visible: true
            }
          },
          cptCssData: {
            width: 1000,
            height: 800
          },
          cptX: this.designData.scaleX / 2 - 500,
          cptY: this.designData.scaleY / 2 - 400,
          cptZ: 100,
          cptWidth: 1000,
          cptHeight: 800,
          cptIndexId: '',
          isEchartsComponent: false,
          isBindingComponent: false,
          id: `cpt-map-${require('nanoid').nanoid(12)}`
        }
        this.componentData.push(mapComponent)
        this.cacheChoices = {}
        handleSpecialForm(mapComponent)
      }
      if (
        this.designData.defaultMenuUrl &&
        !this.componentData.some((component) => {
          return component.cptKey === 'cpt-menu'
        })
      ) {
        const menuComponent = {
          cptTitle: '菜单',
          icon: 'menu',
          cptKey: 'cpt-menu',
          cptOptionKey: 'cpt-menu-option',
          cptOption: {
            attribute: {
              name: '菜单',
              className: 'default-class',
              visible: true,
              url: this.designData.defaultMenuUrl
            }
          },
          cptCssData: {
            width: 1920,
            height: 100
          },
          cptX: 0,
          cptY: 0,
          cptZ: 100,
          paramsToLoadDataWithoutBindingIndexSet: {
            tableName: '',
            indexList: [],
            dimensionList: [],
            dataSourceCode: '',
            unitList: []
          },
          cptWidth: 1920,
          cptHeight: 100,
          id: `cpt-menu-${require('nanoid').nanoid(12)}`,
          isEchartsComponent: false,
          isBindingComponent: false
        }
        this.componentData.push(menuComponent)
        this.cacheChoices = {}
        handleSpecialForm(menuComponent)
      }
    },
    copyCpt(item) {
      const generateNewIds = (component, pid) => {
        component.pid = pid
        component.id = `${component.cptKey}-${require('nanoid').nanoid(12)}`
        if (component.children && component.children.length > 0) {
          component.children.forEach((child) => generateNewIds(child, component.id))
        }
      }

      let copyItem = JSON.parse(JSON.stringify(item))
      copyItem.cptX = item.cptX + 30
      copyItem.cptY = item.cptY + 30
      generateNewIds(copyItem, copyItem.pid || '')
      this.designerStore.setCurrentCptId(copyItem.id)
      this.addComponent(copyItem)
      this.pushHistory(`复制组件 "${item.cptOption.attribute.name || item.cptTitle}"`, copyItem.id)
      this.$message.success(`已复制组件 "${item.cptOption.attribute.name || item.cptTitle}"`)
    },
    hideCpt(item) {
      const isVisible = item.cptOption.attribute.visible
      item.cptOption.attribute.visible = !isVisible
      const componentName = item.cptOption.attribute.name || item.cptTitle
      const action = isVisible ? '隐藏' : '显示'
      this.pushHistory(`${action}组件 "${componentName}"`, item.id)
      this.$message.success(`已${action}组件 "${componentName}"`)
    },
    refreshCptData(pid) {
      if (pid) {
        this.$bus.$emit('pid', pid)
        return
      }

      const currentId = this.designerStore.currentCptId
      const storeCurrentCpt = this.designerStore.currentCpt

      // 只有在真正更新组件时才记录历史，而不是仅仅选中组件时
      if (storeCurrentCpt && storeCurrentCpt.id === currentId) {
        this.designerStore.updateComponent(storeCurrentCpt)
        // 移除这里的 pushHistory 调用，因为仅仅选中组件不应该记录历史
        // this.pushHistory('修改组件', currentId)
      } else if (currentId && (!storeCurrentCpt || storeCurrentCpt.id !== currentId)) {
      }

      const refName = currentId
      const cptRef = this.$refs[refName]
      let componentInstance = Array.isArray(cptRef) ? cptRef[0] : cptRef
      if (componentInstance.$refs.chart) componentInstance = componentInstance.$refs.chart
      if (!componentInstance.refreshCptData) {
        this.$message.warning('当前图层还未实现refreshCptData方法')
      } else {
        componentInstance.refreshCptData() //刷新子组件数据，refs为组建名加index
      }
    },
    outBlur() {
      // 如果刚刚完成框选，不要清除选择状态
      if (this.justFinishedSelection) {
        this.justFinishedSelection = false
        return
      }
      this.currentCptId = ''
      this.currentCpt = {}
      this.cacheChoices = {}
      this.designerStore.clearSelectedComponents()
    },
    async submitDesign() {
      const loading = this.$loading({
        lock: true,
        text: '保存中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      localStorage.setItem(
        'designCache',
        JSON.stringify({ ...this.designData, components: this.componentData })
      )
      const { id, bgColor, scaleX, scaleY, ...rest } = this.designData
      const formData = this.isModule
        ? Object.assign({}, rest, {
            moduleData: JSON.stringify({
              components: this.componentData
            })
          })
        : Object.assign({}, rest, {
            relWorkbench: this.$router.currentRoute.params.workbenchId,
            topicJson: JSON.stringify({
              topicWidth: scaleX,
              topicHeight: scaleY,
              components: this.componentData
            })
          })
      formData.id = this.designData.id
      console.log('formData', formData)
      httpAction(this.isModule ? this.url.editModule : this.url.editTopic, formData, 'post')
        .then(async (res) => {
          if (res.success) {
            const componentCount = this.componentData.length
            const designName = this.designData.topicName || this.designData.moduleName || '设计'
            this.$message.success(`"${designName}" 保存成功，包含 ${componentCount} 个组件`)
            if (!this.designData.id) {
              const res = await getAction('/workbench/scCockpitWorkbench/allList', {
                id: this.$route.params.workbenchId
              })
              this.designData.id =
                res.result[0].childTopicList[res.result[0].childTopicList.length - 1].id
            }
          } else {
            this.$message.warning(`保存失败：${res.message || '未知错误'}`)
          }
        })
        .finally(() => {
          loading.close()
        })
    },
    preview() {
      localStorage.setItem(
        'designCache',
        JSON.stringify({ ...this.designData, components: this.componentData })
      )
      const { topicId, workbenchId, type } = this.$route.params
      const path = '/chart/preview'
      routerTurnByPath(path, [topicId, workbenchId, type], false, true)
    },
    delCpt(id) {
      if (this.currentCptId === id) {
        this.designerStore.setCurrentCpt({ cptOption: undefined })
        this.designerStore.setCurrentCptId('')
      }

      this.deleteComponent(id)

      if (this.$refs[id] && this.$refs[id][0] && this.$refs[id][0].uuid) {
        const childId = this.$refs[id][0].uuid
        clearCptInterval(childId)
      }
      this.pushHistory('删除组件', id)
    },
    showConfigBar(e, item) {
      this.currentCpt = item
      this.currentCptId = item.id
      if (!e.ctrlKey) {
        this.cacheChoices = {}
      }
      this.cacheChoices[item.id] = item
      this.cacheChoicesFixed[item.id] = JSON.parse(JSON.stringify(item))
    },
    dragStart(e) {
      this.copyDom = e.dom
      e.dom.draggable = false
    },
    dragStartNew(obj) {
      this.copyDom = null
      this.dragNodeId = obj.id
    },
    allowDrop(e) {
      if (e.target !== this.$refs.webContainer) {
        return
      }
    },
    async drop(e) {
      if (e.target !== this.$refs.webContainer) {
        return
      }
      if (this.copyDom) {
        let config = JSON.parse(this.copyDom.getAttribute('config'))
        const compTemplateInfoRes = await getAction('/component/scCockpitIndexComponent/list', {
          componentName: config.name
        })
        const compTemplateInfo = compTemplateInfoRes.result.records[0]
        let option = {}
        if (compTemplateInfo) {
          try {
            option = new Function('return ' + compTemplateInfo.jsData)()
          } catch (error) {}
        }
        config.option = Object.assign(config.option, option)
        if (config.option.cptDataForm) {
          if (!config.option.cptDataForm.apiUrl) {
            config.option.cptDataForm.apiUrl = '/design/test'
          }
          if (!config.option.cptDataForm.sql) {
            config.option.cptDataForm.sql = 'select username from sys_user limit 1'
          }
        }
        if (config.option.attribute) {
          config.option.attribute.name = config.name
          if (['菜单'].includes(config.name)) {
            config.option.attribute.url = ''
          }
        }
        let cpt = {
          cptTitle: config.name,
          icon: config.icon,
          cptKey: config.cptKey,
          cptOptionKey: config.cptOptionKey ? config.cptOptionKey : config.cptKey + '-option',
          cptOption: config.option,
          cptCssData: {
            width: config.width ? config.width : 400,
            height: config.height ? config.height : 300
          },
          cptX: config.name === '菜单' ? 0 : Math.round(e.offsetX),
          cptY: config.name === '菜单' ? 0 : Math.round(e.offsetY),
          cptZ: 100,
          paramsToLoadDataWithoutBindingIndexSet: {
            tableName: '',
            indexList: [],
            dimensionList: [],
            dataSourceCode: '',
            unitList: []
          },
          cptWidth: config.width ? config.width : 400,
          cptHeight: config.height ? config.height : 300,
          id: `${config.cptKey}-${require('nanoid').nanoid(12)}`,
          isEchartsComponent: !!this.isEchartsComponent,
          isBindingComponent: false
        }
        this.addComponent(cpt)
        this.cacheChoices = {}
        this.showConfigBar({}, cpt)
        this.$refs['configBar'].showCptConfig()
        handleSpecialForm(cpt)
        this.pushHistory('添加组件', cpt.id)
      } else {
        getAction(this.url.queryIndexSet, { indexId: this.dragNodeId }).then((res) => {
          if (res.success) {
            const moduleData = JSON.parse(res.result.moduleData)
            const componentData = moduleData.componentList[0]
            let option = {}
            try {
              option = new Function('return ' + componentData.jsData)()
            } catch (error) {}
            const attribute = this.getAttribute(componentData.componentType)
            let cpt = {
              cptTitle: componentData.titleName,
              cptKey: componentData.componentType,
              cptOptionKey: componentData.componentType + '-option',
              cptOption: {
                ...option,
                attribute: {
                  ...attribute,
                  bgImage: componentData.bgImage || ''
                }
              },
              cptCssData: JSON.parse(componentData.cssData || '{}'),
              cptX: Math.round(e.offsetX),
              cptY: Math.round(e.offsetY),
              cptZ: 100,
              cptWidth: JSON.parse(componentData.cssData).width,
              cptHeight: JSON.parse(componentData.cssData).height,
              cptIndexId: componentData.indexId,
              isBindingComponent: true,
              id: `${componentData.componentType}-${require('nanoid').nanoid(12)}`
            }
            cpt.cptOption.attribute.name = cpt.cptTitle
            this.addComponent(cpt)
            this.cacheChoices = {}
            this.showConfigBar({}, cpt)
            this.$refs['configBar'].showCptConfig()
            handleSpecialForm(cpt)
            this.pushHistory('添加绑定组件', cpt.id)
          } else {
            this.$message.warning('组件信息查询失败')
          }
        })
      }
    },
    getAttribute(componentType) {
      for (const { children } of cptOptions) {
        if (!children) continue
        const found = children.find((child) => child.cptKey === componentType)
        if (found) return cloneDeep(found.option.attribute)
      }
      for (const { content } of chartItem) {
        if (!content) continue
        const found = content.find((child) => child.cptKey === componentType)
        if (found) return cloneDeep(found.option.attribute)
      }
    },
    changeTopicBg(img) {
      this.designData.topicBackground = img
      this.pushHistory('修改背景')
    },
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    handleShapeOperationEnd(item) {
      // 检查是否有多个组件被选中
      const selectedIds = this.designerStore.selectedComponentIds
      if (selectedIds.length > 1 && selectedIds.includes(item.id)) {
        // 多个组件一起移动，记录所有选中的组件
        this.pushHistory('移动/调整组件', selectedIds)
      } else {
        // 单个组件操作
        this.pushHistory('移动/调整组件', item.id)
      }
    },
    handleRecordHistory(description, componentId) {
      this.pushHistory(description, componentId)
    },
    // 右键菜单相关方法
    handleComponentContextMenu(event, component) {
      event.preventDefault()
      this.contextMenuTarget = component

      // 检查是否是多选状态
      const selectedIds = this.designerStore.selectedComponentIds
      const isMultiSelected = selectedIds.length > 1 && selectedIds.includes(component.id)

      if (isMultiSelected) {
        // 多选状态的菜单
        this.contextMenuItems = [
          { key: 'copy-multi', icon: 'copy', text: `复制 ${selectedIds.length} 个组件` },
          { key: 'delete-multi', icon: 'delete', text: `删除 ${selectedIds.length} 个组件` }
        ]
      } else {
        // 单选状态的菜单
        const visibilityText = component.cptOption.attribute.visible ? '隐藏组件' : '显示组件'
        this.contextMenuItems = [
          { key: 'toggle-visibility', icon: 'eye', text: visibilityText },
          { key: 'copy', icon: 'copy', text: '复制组件' },
          { key: 'delete', icon: 'delete', text: '删除组件' }
        ]
      }

      this.contextMenuVisible = true
    },
    handleContextMenuSelect(key, target) {
      if (!this.contextMenuTarget) return

      const component = this.contextMenuTarget
      const selectedIds = this.designerStore.selectedComponentIds

      // 检查是否是容器组件的右键菜单
      const isContainerMenu = !!this.containerMethods

      switch (key) {
        case 'toggle-visibility':
          if (isContainerMenu) {
            this.containerMethods.hideCpt(component)
          } else {
            this.hideCpt(component)
          }
          break
        case 'copy':
          if (isContainerMenu) {
            this.containerMethods.copyCpt(component)
          } else {
            this.copyCpt(component)
          }
          break
        case 'delete':
          if (isContainerMenu) {
            this.containerMethods.delCpt(component.id)
          } else {
            this.delCpt(component.id)
          }
          break
        case 'copy-multi':
          // 复制多个选中的组件
          selectedIds.forEach((id) => {
            const comp = this.componentData.find((c) => c.id === id)
            if (comp) {
              this.copyCpt(comp)
            }
          })
          break
        case 'delete-multi':
          // 删除多个选中的组件
          selectedIds.forEach((id) => {
            this.delCpt(id)
          })
          // 清除选择状态
          this.designerStore.clearSelectedComponents()
          break
      }

      this.contextMenuTarget = null
      this.containerMethods = null

      // 确保菜单关闭
      this.contextMenuVisible = false
    },
    // 初始化容器组件右键菜单监听
    initContainerContextMenu() {
      // 保存事件处理函数的引用，以便后续移除
      this.handleContainerContextMenu = (event) => {
        const { clientX, clientY, component, containerMethods } = event.detail

        // 设置菜单目标和方法
        this.contextMenuTarget = component
        this.containerMethods = containerMethods

        // 手动设置菜单位置并触发模拟的 contextmenu 事件
        this.$nextTick(() => {
          const contextmenuComponent = this.$children.find(
            (child) => child.$options.name === 'Contextmenu'
          )
          if (contextmenuComponent) {
            contextmenuComponent.left = clientX
            contextmenuComponent.top = clientY

            // 模拟 contextmenu 事件来确保 Contextmenu 组件的 setPosition 被调用
            const mockEvent = new MouseEvent('contextmenu', {
              clientX: clientX,
              clientY: clientY,
              bubbles: true,
              cancelable: true
            })
            window.dispatchEvent(mockEvent)
          }
        })

        // 设置菜单项
        const visibilityText = component.cptOption.attribute.visible ? '隐藏组件' : '显示组件'
        this.contextMenuItems = [
          { key: 'toggle-visibility', icon: 'eye', text: visibilityText },
          { key: 'copy', icon: 'copy', text: '复制组件' },
          { key: 'delete', icon: 'delete', text: '删除组件' }
        ]

        this.contextMenuVisible = true
      }

      // 监听容器组件的自定义右键菜单事件
      document.addEventListener('container-component-contextmenu', this.handleContainerContextMenu)

      // 监听关闭右键菜单事件
      document.addEventListener('close-context-menu', () => {
        this.contextMenuVisible = false
        this.contextMenuTarget = null
        this.containerMethods = null
      })
    },
    // 处理右键菜单可见性变化
    handleContextMenuVisibleChange(visible) {
      this.contextMenuVisible = visible
      if (!visible) {
        // 菜单关闭时清理状态
        this.contextMenuTarget = null
        this.containerMethods = null

        // 如果当前处于框选状态，清除框选状态
        if (this.designerStore.isMultiSelecting || this.designerStore.selectionBox.visible) {
          this.clearSelectionState()
        }
      }
    },
    // 清除框选状态的辅助方法
    clearSelectionState() {
      // 清除框选状态
      this.designerStore.setMultiSelecting(false)
      this.designerStore.hideSelectionBox()

      // 移除事件监听器
      if (this.selectionMouseMoveHandler) {
        document.removeEventListener('mousemove', this.selectionMouseMoveHandler)
        this.selectionMouseMoveHandler = null
      }
      if (this.selectionMouseUpHandler) {
        document.removeEventListener('mouseup', this.selectionMouseUpHandler)
        this.selectionMouseUpHandler = null
      }
    },
    // 处理画布右键菜单事件
    handleCanvasContextMenu(e) {
      // 如果当前处于框选状态，阻止原生右键菜单
      if (this.designerStore.isMultiSelecting || this.designerStore.selectionBox.visible) {
        e.preventDefault()
        // 清除框选状态
        this.clearSelectionState()
        return
      }

      // 如果点击的不是画布本身，允许默认行为
      if (e.target !== this.$refs.webContainer) {
        return
      }

      // 在空白画布区域右键，可以选择是否阻止原生菜单
      // 这里我们允许原生菜单，但也可以根据需要阻止
      // e.preventDefault()
    },
    // 框选相关方法
    handleCanvasMouseDown(e) {
      // 如果点击的是组件，不进行框选
      if (e.target !== this.$refs.webContainer) {
        return
      }

      // 清除之前的选择
      if (!e.ctrlKey && !e.metaKey) {
        this.designerStore.clearSelectedComponents()
        this.currentCptId = ''
        this.currentCpt = {}
      }

      // 清除之前可能存在的事件监听器
      this.clearSelectionState()

      // 开始框选
      const rect = this.$refs.webContainer.getBoundingClientRect()
      const startX = (e.clientX - rect.left) / this.containerScale
      const startY = (e.clientY - rect.top) / this.containerScale

      this.designerStore.setMultiSelecting(true)
      this.designerStore.updateSelectionBox(startX, startY, startX, startY)

      // 创建事件处理函数并保存引用
      this.selectionMouseMoveHandler = (moveEvent) => {
        const currentX = (moveEvent.clientX - rect.left) / this.containerScale
        const currentY = (moveEvent.clientY - rect.top) / this.containerScale
        this.designerStore.updateSelectionBox(startX, startY, currentX, currentY)

        // 实时计算框选范围内的组件数量
        this.updateSelectionPreviewCount()
      }

      this.selectionMouseUpHandler = () => {
        this.designerStore.setMultiSelecting(false)
        this.designerStore.hideSelectionBox()
        this.selectComponentsInBox()

        // 清除预览数量
        this.selectionPreviewCount = 0

        // 清除事件监听器
        document.removeEventListener('mousemove', this.selectionMouseMoveHandler)
        document.removeEventListener('mouseup', this.selectionMouseUpHandler)
        this.selectionMouseMoveHandler = null
        this.selectionMouseUpHandler = null
      }

      // 添加事件监听器
      document.addEventListener('mousemove', this.selectionMouseMoveHandler)
      document.addEventListener('mouseup', this.selectionMouseUpHandler)
    },
    selectComponentsInBox() {
      const box = this.designerStore.selectionBox
      const selectedIds = []

      this.componentData.forEach((component) => {
        if (this.isComponentInBox(component, box)) {
          selectedIds.push(component.id)
        }
      })

      if (selectedIds.length > 0) {
        // 尝试直接修改状态
        this.designerStore.setSelectedComponentIds(selectedIds)

        // 设置标志，防止 outBlur 清除选择状态
        this.justFinishedSelection = true

        // 如果只选中一个组件，设置为当前组件
        if (selectedIds.length === 1) {
          this.currentCptId = selectedIds[0]
          this.currentCpt = this.componentData.find((c) => c.id === selectedIds[0])
        }
      }
    },
    isComponentInBox(component, box) {
      const compLeft = component.cptX
      const compTop = component.cptY
      const compRight = component.cptX + component.cptWidth
      const compBottom = component.cptY + component.cptHeight

      // 检查组件是否与选择框有交集
      return !(
        compRight < box.startX ||
        compLeft > box.endX ||
        compBottom < box.startY ||
        compTop > box.endY
      )
    },
    // 新增的快捷键处理方法
    handleSelectAll() {
      // 全选所有可见组件
      const visibleComponents = this.componentData.filter(
        (component) => component.cptOption.attribute.visible
      )
      const allIds = visibleComponents.map((component) => component.id)

      if (allIds.length > 0) {
        this.designerStore.setSelectedComponentIds(allIds)
        this.$message.success(`已选中 ${allIds.length} 个组件`)

        // 如果只有一个组件，设置为当前组件
        if (allIds.length === 1) {
          this.currentCptId = allIds[0]
          this.currentCpt = visibleComponents[0]
        }
      } else {
        this.$message.info('没有可选择的组件')
      }
    },
    handleDeleteSelected() {
      // 删除选中的组件
      const selectedIds = this.designerStore.selectedComponentIds

      if (selectedIds.length === 0) {
        // 如果没有多选组件，但有当前选中的组件，删除当前组件
        if (this.currentCptId) {
          this.delCpt(this.currentCptId)
        } else {
          this.$message.info('请先选择要删除的组件')
        }
        return
      }

      // 确认删除多个组件
      this.$confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${selectedIds.length} 个组件吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          selectedIds.forEach((id) => {
            this.delCpt(id)
          })
          this.designerStore.clearSelectedComponents()
          this.$message.success(`已删除 ${selectedIds.length} 个组件`)
        }
      })
    },
    handleCancelSelection() {
      // 取消当前选择
      const selectedCount = this.designerStore.selectedComponentIds.length
      const hasCurrentSelection = !!this.currentCptId

      if (selectedCount > 0 || hasCurrentSelection) {
        this.designerStore.clearSelectedComponents()
        this.currentCptId = ''
        this.currentCpt = {}
        this.cacheChoices = {}

        const message = selectedCount > 0 ? `已取消选择 ${selectedCount} 个组件` : '已取消选择'
        this.$message.info(message)
      }
    },
    // 更新框选预览数量
    updateSelectionPreviewCount() {
      const box = this.designerStore.selectionBox
      let count = 0

      this.componentData.forEach((component) => {
        if (this.isComponentInBox(component, box)) {
          count++
        }
      })

      this.selectionPreviewCount = count
    }
  }
}
</script>

<style lang="less" scoped>
main {
  height: calc(100vh - 60px);
  display: flex;
  background-image: linear-gradient(#fafafc 14px, transparent 0),
    linear-gradient(90deg, transparent 14px, #373739 0);
  background-size: 15px 15px, 15px 15px;
  background-repeat: repeat;

  > div {
    height: 100%;
    overflow: hidden;
  }

  .canvas-container {
    position: relative;
    display: flex;
    flex-direction: column;
    .ant-tabs {
      height: calc(100% - 40px);
    }

    #chart-edit-layout {
      flex-grow: 1;
    }

    .bottom {
      display: flex;
      gap: 20px;
      flex-wrap: nowrap;
      align-items: center;
      height: 40px;
      padding: 0 10px;
      background-color: #f2f3f5;
      flex-shrink: 0;

      > div {
        width: 100px;
        max-width: 100px;
      }
      .ant-btn {
        margin-right: 8px;
      }
    }
  }
}
.canvas {
  position: relative;
  user-select: none;
  background-size: 100% 100%;
  transform-origin: 0 0;
}

.selection-box {
  position: absolute;
  border: 2px dashed #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  pointer-events: none;
  z-index: 1000;
}

.selection-count-tip {
  position: absolute;
  background-color: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
::v-deep .ant-tabs-bar {
  margin: revert;
}
</style>
