<template>
  <div>
    <div class="jsEditorContainer">
      <div class="title">JS代码</div>
      <monaco-editor
        ref="jsCodeEditor"
        v-model="jsDataCopy"
        language="javascript"
        width="100%"
        height="400px"
        :options="editorOptions"
        @input="$emit('customCode', { jsData: jsDataCopy, cssData: cssDataCopy })"
      ></monaco-editor>
    </div>
    <!-- <div class="cssEditorContainer">
      <div class="title">CSS代码<span>优先级高于属性配置表单</span></div>
      <editor
        ref="cssCodeEditor"
        v-model="cssDataCopy"
        width="100%"
        height="200px"
        :options="editorOptions"
        @init="editorInit"
        @input="$emit('customCode', { jsData: jsDataCopy, cssData: cssDataCopy })"
      ></editor>
    </div> -->
  </div>
</template>
<script>
import { chartItem } from './components/constant.js'
import MonacoEditor from '@/components/MonacoEditor.vue'

export default {
  name: 'CustomCodeForm',
  components: {
    MonacoEditor
  },
  props: {
    componentType: {
      type: String,
      default: ''
    },
    cssData: {
      type: String,
      default: ''
    },
    jsData: {
      type: String,
      default: ''
    },
    pid: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chartItem,
      jsDataCopy: '',
      cssDataCopy: '',
      editorOptions: {
        tabSize: 2,
        fontSize: 14,
        showPrintMargin: false
        // enableBasicAutocompletion: true,
        // enableLiveAutocompletion: true,
        // enableSnippets: true
      }
    }
  },
  watch: {
    cssData(val) {
      this.cssDataCopy = val
    },
    jsData(val) {
      this.jsDataCopy = val
    }
  },
  mounted() {
    this.jsDataCopy = this.jsData
    this.cssDataCopy = this.cssData

    // 延迟调用 resize 确保编辑器已经初始化
    this.$nextTick(() => {
      if (this.$refs.jsCodeEditor) {
        this.$refs.jsCodeEditor.resize()
      }
    })
  },
  methods: {
    refreshCptData() {
      this.$emit('refreshCptData', this.pid)
    }
  }
}
</script>
<style lang="less" scoped>
.jsEditorContainer {
  margin-bottom: 20px;
}
.cssEditorContainer {
  margin-bottom: 20px;
}
.title {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 16px;
  padding-left: 10px;
  position: relative;
  span {
    font-size: 10px;
    color: #666;
    font-style: italic;
    margin-left: 10px;
  }
  &::before {
    content: '';
    position: absolute;
    left: 0;
    height: 16px;
    width: 4px;
    border-radius: 4px;
    background-color: #1890ff;
  }
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    height: 2px;
    width: 100%;
    background-color: #e8e8e8;
  }
}
</style>
