<template>
  <div class="config-preview-drawer">
    <a-drawer
      width="800"
      placement="right"
      :closable="false"
      :visible="visible"
      style="height: 100%; overflow: auto"
      @close="onClose"
    >
      <div class="config-preview-drawer-content">
        <monaco-editor
          v-model="config"
          language="json"
          width="100%"
          height="calc(100vh - 48px)"
          :options="editorOptions"
        ></monaco-editor>
      </div>
    </a-drawer>
  </div>
</template>

<script>
import MonacoEditor from '@/components/MonacoEditor.vue'

export default {
  components: {
    MonacoEditor
  },
  data() {
    return {
      visible: false,
      config: '',
      editorOptions: {
        tabSize: 2,
        fontSize: 14,
        rulers: [],
        readOnly: true,
        readOnlyMessage: '无法在只读编辑器中编辑'
      }
    }
  },
  methods: {
    showDrawer(data) {
      this.config = data
      this.visible = true
    },
    onClose() {
      this.visible = false
    }
  }
}
</script>
