<template>
  <div class="bar">
    <a-tabs v-model="currentTab" type="card">
      <a-tab-pane :key="0" tab="组件" forceRender>
        <a-collapse v-model="cptGroupKeys" expandIconPosition="right">
          <a-collapse-panel
            v-for="group in options"
            :key="group.name"
            :header="group.name"
            forceRender
          >
            <a-col v-for="item in group.children" :key="item.name + item.id" :span="12">
              <div
                draggable="true"
                :config="JSON.stringify(item)"
                style="
                  box-shadow: 0 2px 10px #ddd;
                  height: 70px;
                  text-align: center;
                  margin-top: 2px;
                "
                @dragstart="(e) => dragStart(e, 0, JSON.stringify(item))"
              >
                <div style="line-height: 40px">
                  <img
                    v-if="item.icon"
                    style="width: 20px; filter: invert(80%)"
                    :src="require('@/assets/icon/' + item.icon + '.svg')"
                  />
                  <i v-else style="font-size: 20px" class="el-icon-question"></i>
                </div>
                <div style="color: #333; font-size: 13px">{{ item.name }}</div>
              </div>
            </a-col>
          </a-collapse-panel>
          <a-collapse-panel key="图表" header="图表" forceRender>
            <a-col v-for="item in chartList" :key="item.id" :span="12">
              <div
                draggable="true"
                :config="convertConfig(item)"
                style="
                  box-shadow: 0 2px 10px #ddd;
                  height: 70px;
                  text-align: center;
                  margin-top: 2px;
                "
                @dragstart="(e) => dragStart(e, 0, convertConfig(item))"
              >
                <div style="line-height: 40px">
                  <img
                    v-if="item.previewImage"
                    style="width: 30px"
                    :src="getFileAccessHttpUrl(item.previewImage)"
                  />
                  <i v-else style="font-size: 20px" class="el-icon-question"></i>
                </div>
                <div style="color: #333; font-size: 13px">{{ item.componentName }}</div>
              </div>
            </a-col>
          </a-collapse-panel>
        </a-collapse>
      </a-tab-pane>
      <a-tab-pane :key="1" tab="图层" forceRender>
        <div v-show="componentData.length === 0" style="text-align: center; line-height: 50px">
          无图层
        </div>
        <a-tree
          v-show="componentData.length > 0"
          :tree-data="formattedComponentData"
          :draggable="true"
          :selectedKeys="[currentCptId]"
          :expandedKeys="expandedKeys"
          @drop="onLayerDrop"
          @expand="onExpand"
        >
        </a-tree>
      </a-tab-pane>
      <!-- <a-tab-pane :key="2" tab="已绑定组件" forceRender>
        <a-button style="width: 100%" :loading="treeLoading" @click="getComponentTree"
          >刷新</a-button
        >
        <a-tree
          :tree-data="componentTree"
          :replaceFields="replaceFields"
          :draggable="true"
          show-icon
          default-expand-all
          @dragstart="({ event, node }) => dragStartNew(event, node, 1)"
        >
          <a-icon slot="group" type="folder-open"></a-icon>
          <a-icon slot="component" type="bar-chart"></a-icon>
        </a-tree>
      </a-tab-pane> -->
    </a-tabs>
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import options from '@/components/options'
import { getAction, getFileAccessHttpUrl } from '@/api/manage.js'
import { getChartData, traverseTree } from '@/api/api'
import echartsCommonOption from '@/components/echarts/attr/cpt-echart-common-option.js'
import { nonEchartsComp } from '../index/constant/nonEchartsComp'
export default {
  name: 'ComponentBar',
  data() {
    return {
      options,
      cptGroupKeys: [],
      echartsCommonOption,
      nonEchartsComp,
      currentTab: 0, // 0组件，1图层
      componentTree: [],
      bindingComponents: [],
      chartList: [],
      replaceFields: {
        children: 'childList',
        key: 'id',
        value: 'id',
        title: 'name'
      },
      url: {
        listModules: '/group/scCockpitGroup/queryByType?groupType=index'
      },
      layerReplaceFields: {
        children: 'children',
        key: 'id',
        title: 'name'
      },
      expandedKeys: [],
      treeLoading: false
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['componentData', 'currentCpt', 'currentCptId']),
    formattedComponentData() {
      return this.formatComponentsForTree(this.componentData)
    }
  },
  watch: {
    formattedComponentData: {
      handler(newVal) {
        // 当数据变化时，重新计算所有需要展开的keys
        // this.expandedKeys = this.getAllKeys(this.formattedComponentData)
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.cptGroupKeys = [
      ...this.options.filter((item) => item.opened === true).map((item) => item.name),
      '图表'
    ]
    this.getComponentTree()
    this.getChartList()
  },
  methods: {
    async getComponentTree() {
      this.treeLoading = true
      const treeRes = await getAction(this.url.listModules)
      if (treeRes.success) {
        this.componentTree = treeRes.result
        this.bindingComponents = []
        for (const node of this.componentTree) {
          traverseTree(node, (node) => {
            if (node.groupName) {
              node.name = node.groupName
              node.slots = { icon: 'group' }
            }
            if (node.data) {
              this.bindingComponents.push(
                ...node.data.map((item) => {
                  return item.id
                })
              )
              if (!node.childList) node.childList = []
              node.childList = node.childList.concat(
                ...node.data.map((item) => {
                  return {
                    ...item,
                    slots: { icon: 'component' }
                  }
                })
              )
            }
          })
        }
      } else {
        this.$message.warning('已绑定组件信息获取失败')
      }
      this.treeLoading = false
    },
    getChartList() {
      getAction('/component/scCockpitIndexComponent/list?componentClass=index', {
        pageSize: 100
      }).then((res) => {
        if (res.success) {
          this.chartList = res.result.records.filter((item) => item.componentType !== 'extra')
        }
      })
    },
    dragStart(e, flag, config) {
      e.dataTransfer.setData('config', config)
      let copyDom = e.currentTarget.cloneNode(true)
      this.$emit('dragStart', { dom: copyDom, flag })
    },
    dragStartNew(e, node, flag) {
      e.dataTransfer.setData('indicatorId', node.dataRef.id)
      this.$emit('dragStartNew', { id: node.dataRef.id, flag })
    },
    showConfigBar(e, item, id) {
      this.$emit('showConfigBar', e, item, id)
    },
    copyCpt(item) {
      this.$emit('copyCpt', item)
    },
    hideCpt(item) {
      this.$emit('hideCpt', item)
    },
    delCpt(item, id) {
      this.$emit('delCpt', item, id)
    },
    formatComponentsForTree(components, level = 0) {
      return components.map((item) => {
        const treeNode = {
          id: item.id,
          key: item.id,
          title: () => {
            return this.$createElement('span', { class: 'layer-item' }, [
              this.$createElement(
                'span',
                {
                  class: 'layer-item__name',
                  on: {
                    click: (event) => this.onLayerSelect(event, item, level)
                  }
                },
                item.cptOption.attribute.name || item.cptTitle
              ),
              this.$createElement('span', { class: 'layer-actions' }, [
                this.$createElement('i', {
                  class: 'el-icon-view',
                  on: {
                    click: (e) => {
                      e.stopPropagation()
                      this.hideCpt(item)
                    }
                  }
                }),
                this.$createElement('i', {
                  class: 'el-icon-copy-document',
                  on: {
                    click: (e) => {
                      e.stopPropagation()
                      this.copyCpt(item)
                    }
                  }
                }),
                this.$createElement('i', {
                  class: 'el-icon-delete',
                  on: {
                    click: (e) => {
                      e.stopPropagation()
                      this.delCpt(item, item.id)
                    }
                  }
                })
              ])
            ])
          },
          icon: () => {
            if (item.icon || item.cptKey) {
              return this.$createElement('img', {
                style: {
                  width: '20px',
                  filter: item.icon ? 'invert(80%)' : 'none'
                },
                attrs: {
                  src: item.icon
                    ? require('@/assets/icon/' + item.icon + '.svg')
                    : item.cptKey
                    ? require('@/assets/img/' + item.cptKey + '.png')
                    : ''
                }
              })
            } else {
              return this.$createElement('i', {
                class: 'el-icon-question',
                style: { fontSize: '20px' }
              })
            }
          },
          item: item
        }

        if (item.children && item.children.length > 0) {
          treeNode.children = this.formatComponentsForTree(item.children, level + 1)
        }

        return treeNode
      })
    },
    getComponentById(id) {
      const findInComponents = (components, targetId) => {
        for (const comp of components) {
          if (comp.id === targetId) return comp
          if (comp.children && comp.children.length > 0) {
            const found = findInComponents(comp.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      return findInComponents(this.componentData, id)
    },
    onLayerSelect(e, item, level) {
      const name = item.cptOption.attribute.name
      // 只有最顶层的组件才滚动到视图中
      if (name && level === 0) {
        const ele = document.getElementById(name)
        if (ele) ele.scrollIntoView({ behavior: 'instant' })
      }
      this.showConfigBar(e, item, item.id)
    },
    onLayerDrop({ node: dropNode, dragNode, dropPosition }) {
      const dragId = dragNode.dataRef.id
      const dropId = dropNode.dataRef.id
      const dropNodeData = dropNode.dataRef.item
      this.currentCpt = dragNode.dataRef.item
      this.currentCptId = dragId
      this.currentCpt.pid = dropNodeData.pid
      this.currentCpt.pIndexSetId = dropNodeData.pIndexSetId
      // 从原位置移除组件
      const dragComponent = this.getComponentById(dragId)
      const parentOfDrag = this.findParentComponent(dragId)

      if (!dragComponent) return

      // 如果有父组件，从父组件的children中移除
      if (parentOfDrag) {
        const index = parentOfDrag.children.findIndex((item) => item.id === dragId)
        if (index > -1) {
          parentOfDrag.children.splice(index, 1)
        }
      } else {
        // 如果是根级组件，从componentData移除
        const index = this.componentData.findIndex((item) => item.id === dragId)
        if (index > -1) {
          this.componentData.splice(index, 1)
        }
      }

      // 放置到新位置
      if (dropPosition === 0) {
        // 成为子节点
        const dropComponent = this.getComponentById(dropId)

        if (!dropComponent.children) {
          dropComponent.children = []
        }
        dropComponent.children.push(dragComponent)
      } else {
        // 放在前面或后面
        const parentOfDrop = this.findParentComponent(dropId)
        let targetArray = parentOfDrop ? parentOfDrop.children : this.componentData
        let dropIndex = targetArray.findIndex((item) => item.id === dropId)

        if (dropPosition === 1) {
          // 放在后面
          dropIndex += 1
        }

        targetArray.splice(dropIndex, 0, dragComponent)
      }
    },
    // 查找组件的父组件
    findParentComponent(id) {
      const findParent = (components, targetId) => {
        for (const comp of components) {
          if (comp.children && comp.children.length > 0) {
            if (comp.children.some((child) => child.id === targetId)) {
              return comp
            }
            const found = findParent(comp.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      return findParent(this.componentData, id)
    },
    getAllKeys(data) {
      let keys = []
      const getKeys = (items) => {
        items.forEach((item) => {
          keys.push(item.key)
          if (item.children && item.children.length) {
            getKeys(item.children)
          }
        })
      }
      getKeys(data)
      return keys
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    convertConfig(cpt) {
      return JSON.stringify({
        name: cpt.componentName,
        icon: this.getFileAccessHttpUrl(cpt.previewImage),
        cptKey: cpt.componentCode,
        option: {
          ...new Function('return ' + cpt.jsData)(),
          attribute: {
            visible: true,
            className: 'default-class',
            name: '未命名元素',
            authorizeRole: [],
            ...(!this.nonEchartsComp.some((comp) => comp.name === cpt.componentName)
              ? this.echartsCommonOption.attribute
              : {})
          }
        }
      })
    },
    getFileAccessHttpUrl(path) {
      return getFileAccessHttpUrl(path)
    }
  }
}
</script>

<style lang="less" scoped>
.bar {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f2f3f5;
  color: #333;
  overflow-x: hidden;
  overflow-y: auto;
}
:deep(.ant-tabs) {
  .ant-tabs-bar {
    margin: 8px;
  }
  .ant-tabs-nav {
    display: block;

    > div:first-of-type {
      display: flex;
      gap: 4px;
      padding: 0 2px;
    }

    .ant-tabs-tab {
      flex: 1;
      margin-right: 0;
      text-align: center;
      border-color: transparent;
      background: transparent;
    }
    .ant-tabs-tab-active {
      border-color: #fff;
      background: #fff;
    }
  }
}
:deep(.ant-collapse) {
  .ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }
}
.selectedItem {
  padding: 0 10px;
  line-height: 45px;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) inset;
}
.drag-icon {
  width: 20px;
  &:hover {
    cursor: move;
  }
}
.layer-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-right: 10px;

  .layer-item__name {
    flex: 1;
  }
}

.layer-actions {
  visibility: hidden;
}

.layer-item:hover .layer-actions {
  visibility: visible;
}

.layer-actions i {
  margin-left: 4px;
  cursor: pointer;
}

:deep(.ant-tree-node-content-wrapper) {
  width: calc(100% - 24px);
}
</style>
