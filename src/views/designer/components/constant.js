import { attribute } from 'dom-helpers'
import { property } from 'lodash-es'
import commonAttribute from './commonAttribute'
import cptEchartCommonOption from '@/components/echarts/attr/cpt-echart-common-option.js'
export const tabItem = [
  {
    name: '图表',
    icon: 'pie-chart'
  },
  {
    name: '查询组件',
    icon: 'filter'
  },
  {
    name: '富文本',
    icon: 'font-size'
  },
  {
    name: '媒体',
    icon: 'picture'
  },
  {
    name: 'tab',
    icon: 'layout'
  },
  {
    name: '更多',
    icon: 'appstore'
  },
  {
    name: '复用',
    icon: 'copy'
  }
]

export const chartItem = [
  {
    catogary: '线图',
    content: [
      {
        name: '基础折线图',
        pic: require('../../../assets/img/basicLineChart.png'),
        cptKey: 'basicLineChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '面积图',
        pic: require('../../../assets/img/areaChart.png'),
        cptKey: 'areaChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '堆叠折线图',
        pic: require('../../../assets/img/stackedLineChart.png'),
        cptKey: 'stackedLineChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '堆叠面积图',
        pic: require('../../../assets/img/stackedAreaChart.png'),
        cptKey: 'stackedAreaChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      }
    ]
  },
  {
    catogary: '柱状图',
    content: [
      {
        name: '基础柱状图',
        pic: require('../../../assets/img/basicHistogramChart.png'),
        cptKey: 'basicHistogramChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '堆叠柱状图',
        pic: require('../../../assets/img/stackedHistogramChart.png'),
        cptKey: 'stackedHistogramChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '分组柱状图',
        pic: require('../../../assets/img/groupedHistogramChart.png'),
        cptKey: 'groupedHistogramChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '基础条形图',
        pic: require('../../../assets/img/basicBarChart.png'),
        cptKey: 'basicBarChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '堆叠条形图',
        pic: require('../../../assets/img/stackedBarChart.png'),
        cptKey: 'stackedBarChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '分组条形图',
        pic: require('../../../assets/img/groupedBarChart.png'),
        cptKey: 'groupedBarChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      }
    ]
  },
  {
    catogary: '分布图',
    content: [
      {
        name: '饼图',
        pic: require('../../../assets/img/pieChart.png'),
        cptKey: 'pieChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '环形图',
        pic: require('../../../assets/img/ringChart.png'),
        cptKey: 'ringChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '玫瑰图',
        pic: require('../../../assets/img/roseChart.png'),
        cptKey: 'roseChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      },
      {
        name: '雷达图',
        pic: require('../../../assets/img/radarChart.png'),
        cptKey: 'radarChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      }
    ]
  },
  {
    catogary: '双轴图',
    content: [
      {
        name: '柱线组合图',
        pic: require('../../../assets/img/BarLineChart.png'),
        cptKey: 'BarLineChart',
        option: {
          attribute: { ...commonAttribute.attribute, ...cptEchartCommonOption.attribute }
        }
      }
    ]
  }
  // {
  //   catogary: '富文本',
  //   content: [{
  //     name: '富文本',
  //     pic: '',
  //     option: {}
  //   }]
  // },
  // {
  //   catogary: '媒体',
  //   content: [  {
  //     name: '图片',
  //     pic: '',
  //     option: {}
  //   },
  //   {
  //     name: '视频',
  //     pic: '',
  //     option: {}
  //   },
  //   {
  //     name: '流媒体',
  //     pic: '',
  //     option: {}
  //   },
  //   {
  //     name: '图片组',
  //     pic: '',
  //     option: {}
  //   }]
  // }
]
export const searchComp = [
  {
    name: '查询组件',
    pic: ''
  }
]
export const richText = [
  {
    name: '富文本',
    pic: ''
  }
]
export const media = [
  {
    name: '图片',
    pic: ''
  },
  {
    name: '视频',
    pic: ''
  },
  {
    name: '流媒体',
    pic: ''
  },
  {
    name: '图片组',
    pic: ''
  }
]
export const tab = [
  {
    name: 'tab',
    pic: ''
  }
]
export const more = [
  {
    name: '网页',
    pic: ''
  }
]
