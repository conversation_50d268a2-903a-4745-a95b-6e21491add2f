export function handleSpecialForm(cpt) {
  if ('rowGap' in cpt.cptOption.attribute) {
    cpt.cptOption.attribute.rowGap = 0
  }
  if ('columnGap' in cpt.cptOption.attribute) cpt.cptOption.attribute.columnGap = 0
  if ('dimenssionStyle' in cpt.cptOption.attribute)
    cpt.cptOption.attribute.dimenssionStyle = 'style = {\n\n}'
  if ('indexStyle' in cpt.cptOption.attribute) cpt.cptOption.attribute.indexStyle = 'style = {\n\n}'
  if ('unitStyle' in cpt.cptOption.attribute) cpt.cptOption.attribute.unitStyle = 'style = {\n\n}'
  if ('tabStyle' in cpt.cptOption.attribute) {
    cpt.cptOption.attribute.activeTabStyle = `style = {
  width: '54px',
  height: '28px',
  backgroundColor:'#1A2D4E',
  borderRadius: '4px',
  border: '2px solid #3E8BF2',
  fontFamily: 'Microsoft YaHei',
  fontWeight: 'bold',
  fontSize: '14px',
  color: '#FFFFFF',
  lineHeight: '41px',
  display: 'flex',
  justifyContent:'center',
  alignItems:'center',
  cursor:'pointer'
  }`
    cpt.cptOption.attribute.tabStyle = `style = {
  width: '54px',
  height: '28px',
  backgroundColor: 'rgba(26,45,78,0.8)',
  borderRadius: '4px',
  border: '1px solid #6A90B4',
  fontFamily: 'Microsoft YaHei',
  fontWeight: 'bold',
  fontSize: '14px',
  color: '#ADC3D7',
  lineHeight: '41px',
  display: 'flex',
  justifyContent:'center',
  alignItems:'center',
  cursor:'pointer'
  }`
    cpt.cptOption.attribute.tabContent = ['tab1', 'tab2']
  }

  if ('barColumns' in cpt.cptOption.attribute) {
    // 分组和堆叠柱线组合图柱和线的数量
    cpt.cptOption.attribute.barColumns = cpt.cptOption.series.filter((item) => {
      return item.type === 'bar'
    }).length
    cpt.cptOption.attribute.lineColumns =
      cpt.cptOption.series.length - cpt.cptOption.attribute.barColumns
  }
}
