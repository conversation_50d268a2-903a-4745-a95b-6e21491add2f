<template>
  <div class="designer-ruler-container">
    <SketchRule
      :key="rulerScale"
      :width="rulerWidth"
      :height="rulerHeight"
      :scale="rulerScale"
      :thick="thick"
      :startX="rulerStartX"
      :startY="rulerStartY"
      :shadow="shadowConfig"
      :horLineArr="horizontalLines"
      :verLineArr="verticalLines"
      :cornerActive="cornerActive"
      :lang="lang"
      :palette="palette"
      @handleLine="handleLineChange"
      @onCornerClick="handleCornerClick"
    />
  </div>
</template>

<script>
// 重新引入 vue-sketch-ruler
import SketchRule from 'vue-sketch-ruler'

export default {
  name: 'DesignerRuler',
  components: {
    SketchRule
  },
  props: {
    // 画布尺寸
    canvasWidth: {
      type: Number,
      required: true
    },
    canvasHeight: {
      type: Number,
      required: true
    },
    // 缩放比例
    scale: {
      type: Number,
      default: 1
    },
    // 容器尺寸
    containerWidth: {
      type: Number,
      required: true
    },
    containerHeight: {
      type: Number,
      required: true
    },
    // 标尺厚度
    thick: {
      type: Number,
      default: 16
    },
    // 滚动偏移量
    scrollLeft: {
      type: Number,
      default: 0
    },
    scrollTop: {
      type: Number,
      default: 0
    },
    // 参考线
    horizontalLines: {
      type: Array,
      default: () => []
    },
    verticalLines: {
      type: Array,
      default: () => []
    },
    // 功能开关
    cornerActive: {
      type: Boolean,
      default: true
    },
    showReferenceLines: {
      type: Boolean,
      default: true
    },
    // 语言
    lang: {
      type: String,
      default: 'zh-CN'
    }
  },
  data() {
    return {
      palette: {
        bgColor: 'rgba(225,225,225, 0)',
        longfgColor: '#BABBBC',
        shortfgColor: '#C8CDD0',
        fontColor: '#7D8694',
        shadowColor: '#E8E8E8',
        lineColor: '#EB5648',
        borderColor: '#DADADC',
        cornerActiveColor: 'rgb(235, 86, 72, 0.6)'
      }
    }
  },
  computed: {
    // 标尺显示区域尺寸
    rulerWidth() {
      return this.containerWidth - this.thick
    },
    rulerHeight() {
      return this.containerHeight - this.thick
    },
    // 计算正确的标尺缩放比例
    rulerScale() {
      return this.scale
    },
    // 计算标尺起始位置
    rulerStartX() {
      return this.scrollLeft / this.rulerScale
    },
    rulerStartY() {
      return this.scrollTop / this.rulerScale
    },
    // 阴影区域配置（对应画布区域）
    shadowConfig() {
      // 按照官方示例，shadow 的 x,y 应该是 0,0
      return {
        x: 0,
        y: 0,
        width: this.canvasWidth,
        height: this.canvasHeight
      }
    }
  },

  methods: {
    // 处理参考线变化
    handleLineChange(lines) {
      this.$emit('line-change', lines)
    },
    // 处理角落点击
    handleCornerClick() {
      this.$emit('corner-click')
    }
  }
}
</script>

<style scoped>
.designer-ruler-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
}

/* 确保标尺组件有足够的空间 */
.designer-ruler-container >>> * {
  box-sizing: border-box;
}

/* 确保标尺区域可以接收事件 */
.designer-ruler-container >>> .sketch-ruler {
  pointer-events: auto;
}
</style>

