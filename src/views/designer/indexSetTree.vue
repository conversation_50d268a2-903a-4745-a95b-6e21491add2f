<template>
  <div class="tree-wrapper" style="display: flex">
    <el-form label-width="80px" size="mini">
      <el-form-item label="数据集">
        <el-select v-model="form.table" placeholder="请选择数据集" @change="handleTableChange">
          <el-option
            v-for="item in tableList"
            :key="item.id"
            :label="item.tableName + `(${item.tableComment})`"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度">
        <el-select
          v-model="form.dimensionList"
          multiple
          placeholder="请选择维度字段"
          @change="handleEmit"
        >
          <el-option
            v-for="item in columnList"
            :key="item.id"
            :label="`${item.columnName}(${item.columnComment})`"
            :value="item.columnName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="指标">
        <el-select
          v-model="form.indexList"
          multiple
          placeholder="请选择指标字段"
          @change="handleEmit"
        >
          <el-option
            v-for="item in columnList"
            :key="item.id"
            :label="`${item.columnName}(${item.columnComment})`"
            :value="item.columnName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="单位">
        <el-select
          v-model="form.unitList"
          multiple
          placeholder="请选择单位字段"
          @change="handleEmit"
        >
          <el-option
            v-for="item in columnList"
            :key="item.id"
            :label="`${item.columnName}(${item.columnComment})`"
            :value="item.columnName"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-button style="float: right" type="primary">添加查询控件</el-button> -->
    </el-form>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
export default {
  name: 'IndexSetTree',
  data() {
    return {
      form: {
        table: undefined,
        dimensionList: undefined,
        indexList: undefined,
        unitList: []
      },
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      },
      dataSourceCode: '',
      tableList: [],
      columnList: [],
      timer: null,
      url: {
        listTable: '/data/scCockpitDataSource/list',
        queryTableById: '/data/scCockpitDataSource/queryById',
        queryIndexSetInfo: '/index/scCockpitIndexGroupConfig/queryById'
      }
    }
  },
  created() {
    this.initOptions()
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCpt', 'currentCptId', 'componentData'])
  },
  // eslint-disable-next-line vue/order-in-components
  watch: {
    currentCptId(val) {
      if (!val) return
      const data = this.currentCpt?.paramsToLoadDataWithoutBindingIndexSet
      if (!data?.tableName) {
        this.form = {
          table: undefined,
          dimensionList: undefined,
          indexList: undefined
        }
        this.dataSourceCode = ''
        return
      }
      this.form.table = this.tableList.filter((table) => {
        return table.tableName === data.tableName
      })[0].id
      this.handleTableChange(this.form.table)
      this.form.dimensionList = data.dimensionList.map((dimension) => {
        return dimension.columnName
      })
      this.form.indexList = data.indexList.map((index) => {
        return index.columnName
      })
      this.dataSourceCode = data.dataSourceCode
    }
  },
  methods: {
    async initOptions() {
      const tableRes = await getAction(this.url.listTable)
      if (tableRes.success) {
        this.tableList = tableRes.result.records
      } else {
        this.$message.warning('数据集获取失败')
      }
    },
    async handleTableChange(id) {
      this.form.indexList = undefined
      this.form.dimensionList = undefined
      const columnListRes = await getAction(this.url.queryTableById, { id })
      if (columnListRes.success) {
        this.columnList = columnListRes.result.dataColumnList
        this.dataSourceCode = columnListRes.result.tableSourceCode
      } else {
        this.$message.warning('表信息获取失败')
      }
    },
    handleEmit() {
      if (
        this.form.indexList &&
        this.form.indexList.length &&
        this.form.dimensionList &&
        this.form.dimensionList.length
      ) {
        const tableName = this.tableList.filter((table) => {
          return table.id === this.form.table
        })[0].tableName
        const indexList = this.form.indexList.map((index) => {
          return this.columnList.filter((column) => {
            return column.columnName === index
          })[0]
        })
        const dimensionList = this.form.dimensionList.map((dimension) => {
          return this.columnList.filter((column) => {
            return column.columnName === dimension
          })[0]
        })
        this.$emit('tableColumn', {
          tableName,
          dataSourceCode: this.dataSourceCode,
          indexList,
          dimensionList,
          unitList: this.form.unitList
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.tree-wrapper {
  margin-top: 5px;
  width: 100%;
  ::v-deep .ant-tree-title {
    user-select: none;
  }
}
</style>

