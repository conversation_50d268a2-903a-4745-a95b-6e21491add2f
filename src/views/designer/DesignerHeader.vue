<template>
  <header class="top">
    <div class="top__left">
      <img src="@/assets/logo.png" alt="logo" />
    </div>
    <div class="top__right" @click.self="$emit('outBlur')">
      <a-button type="link" @click="$emit('previewConfig')"> JSON </a-button>
      <a-button type="link" @click="$emit('importDesign')"> 导入 </a-button>
      <a-dropdown>
        <a-button type="link"> 导出 <a-icon type="down" /> </a-button>
        <a-menu slot="overlay">
          <a-menu-item @click="$emit('exportCommand', 'img')">
            <span><a-icon type="file-image" />&nbsp;图片</span>
          </a-menu-item>
          <a-menu-item @click="$emit('exportCommand', 'json')">
            <span><a-icon type="file-text" />&nbsp;JSON文件</span>
          </a-menu-item>
        </a-menu>
      </a-dropdown>
      <a-button shape="circle" icon="delete" @click="$emit('clearDesign')" />
      <a-button type="primary" @click="$emit('submitDesign')"> 保存 </a-button>
      <a-button @click="$emit('preview')"> 预览 </a-button>
    </div>
  </header>
</template>

<script>
export default {
  name: 'DesignerHeader'
}
</script>

<style lang="less" scoped>
.top {
  --header-height: 60px;
  padding: 0px 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: var(--header-height);
  overflow: hidden;
  margin: 0;
  font-size: 18px;
  line-height: var(--header-height);
  color: rgb(51, 54, 57);
  background: #ffffff;

  .top__left {
    height: 100%;
    overflow: hidden;

    img {
      width: 40px;
      height: 40px;
      padding: 0;
      margin: 0;
      object-fit: cover;
    }
  }
  .top__right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 15px;
  }
}
.ant-btn.ant-btn-link {
  padding: 0;
}
</style>
