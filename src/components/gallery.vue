<template>
  <div>
    <el-dialog
      title="图片素材库"
      :visible.sync="modelShow"
      width="75%"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="pic-grid">
        <div v-for="item in picList" :key="item.name" class="pic-item" @click="confirmCheck(item)">
          <img
            style="height: 100px; width: 100px; object-fit: contain"
            :src="getImgView(item.path)"
            alt=""
          />
          <div>{{ item.name }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAction, getFileAccessHttpUrl } from '@/api/manage'
import { fileUrl } from '/env'
export default {
  name: 'Gallery',
  data() {
    return {
      imgUrl: fileUrl,
      imgData: [],
      modelShow: false,
      picList: [],
      checkedItem: {}
    }
  },
  methods: {
    opened() {
      this.modelShow = true
      this.loadData()
    },

    loadData() {
      getAction('/file/scCockpitFile/list', { pageSize: 100 }).then((res) => {
        if (res.success)
          this.picList = res.result.records.map((item) => {
            return {
              name: item.fileName,
              path: item.filePath
            }
          })
      })
    },
    confirmCheck(val) {
      this.$emit('confirmCheck', val.path)
      this.modelShow = false
    },
    getImgView(text) {
      if (text && text.indexOf(',') > 0) {
        text = text.substring(0, text.indexOf(','))
      }
      return getFileAccessHttpUrl(text)
    }
  }
}
</script>

<style lang="less" scoped>
.pic-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  row-gap: 20px;
}
.pic-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 5px 0;
  &:hover {
    background-color: #ddeefe;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1), 0 15px 40px rgba(0, 0, 0, 0.2);
    transform: translateY(-5px);
    transition: all 0.3s ease;
  }
}
</style>
