<template>
  <div v-if="option.attribute.url" class="menu-container">
    <component
      :is="option.attribute.url"
      style="width: 100%; height: 100%"
      :activeId="activeId"
      @changeActiveId="(id) => $emit('changeActiveId', id)"
    ></component>
  </div>
  <div v-else class="default-menu-container" :draggable="false">
    <div>
      <div
        v-for="item in menuItemListLeft"
        :key="item.id"
        class="item"
        :class="activeId === item.id ? 'item-left-active' : 'item-left'"
        @click="() => $emit('changeActiveId', item.id)"
      >
        {{ item.topicName }}
      </div>
    </div>
    <div>
      <div
        v-for="item in menuItemListRight"
        :key="item.id"
        class="item"
        :class="activeId === item.id ? 'item-right-active' : 'item-right'"
        @click="() => $emit('changeActiveId', item.id)"
      >
        {{ item.topicName }}
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  name: 'CptMenu',
  components: {},
  props: {
    width: Number,
    height: Number,
    activeId: String,
    option: {
      type: Object,
      default: () => ({ url: '' })
    }
  },
  data() {
    return {
      menuItemList: [],
      menuItemListLeft: [],
      menuItemListRight: [],
      itemNum: 3
    }
  },
  watch: {
    'option.attribute.url'(url) {
      if (url) {
        this.$options.components[url] = () => import(`@/components/custom/${url}.vue`)
      }
    }
  },
  created() {
    if (this.option.attribute.url) {
      this.$options.components[this.option.attribute.url] = () =>
        import(`@/components/custom/${this.option.attribute.url}.vue`)
      return
    }
    getAction('/workbench/scCockpitWorkbench/allList', {
      id: this.$router.currentRoute.params.workbenchId
    }).then((res) => {
      if (res.success) {
        this.menuItemList = res.result[0].childTopicList
        this.menuItemListLeft = this.menuItemList.slice(0, this.itemNum)
        this.menuItemListRight = this.menuItemList.slice(this.itemNum)
      } else {
        this.$message.warning('菜单专题目录获取失败')
      }
    })
  }
}
</script>
<style lang="less" scoped>
.menu-container {
  width: 100%;
  height: 100%;
}
.default-menu-container {
  width: 100%;
  height: 100%;
  background: url('@/assets/img/header.png') no-repeat center center / 100% 100%;
  position: relative;
  > div {
    display: flex;
    justify-content: start;
    align-items: center;
    position: absolute;
    &:nth-child(1) {
      left: 50px;
      top: 50px;
    }
    &:nth-child(2) {
      right: 50px;
      top: 50px;
    }
  }
  .item {
    height: 38px;
    width: 126px;
    text-align: center;
    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    margin-right: 20px;
    &.item-left {
      background: url('@/assets/img/menuItem-bg-left.png') no-repeat center center / 100% 100%;
    }
    &.item-left-active {
      background: url('@/assets/img/menuItem-bg-left-active.png') no-repeat center center / 100%
        100%;
    }
    &.item-right {
      background: url('@/assets/img/menuItem-bg-right.png') no-repeat center center / 100% 100%;
    }
    &.item-right-active {
      background: url('@/assets/img/menuItem-bg-right-active.png') no-repeat center center / 100%
        100%;
    }
  }
}
</style>
