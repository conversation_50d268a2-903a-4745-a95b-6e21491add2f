<template>
  <div class="title" :class="{ 'sub-title': !!option.attribute.type }" :style="computedStyle">
    <img v-if="!option.attribute.type" :src="mainTitleIcon" alt="" class="mainTitleIcon" />
    {{ option.attribute.content }}
  </div>
</template>
<script>
import { getFileAccessHttpUrl } from '@/api/manage'
export default {
  name: 'CptTitle',
  props: {
    id: String,
    option: Object
  },
  data() {
    return {}
  },
  computed: {
    computedStyle() {
      return this.$getStyle(this.option.attribute.style)
    },
    mainTitleIcon() {
      return getFileAccessHttpUrl(this.option.attribute.iconBefore)
    }
  },
  created() {}
}
</script>
<style lang="less" scoped>
.sub-title {
  padding-left: 22px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    height: 31px;
    width: 12px;
    background: url('@/assets/img/title-before.png') no-repeat center center / 100% 100%;
  }
}
.mainTitleIcon {
  height: 56px;
  width: 56px;
  position: absolute;
  left: -72px;
}
</style>
