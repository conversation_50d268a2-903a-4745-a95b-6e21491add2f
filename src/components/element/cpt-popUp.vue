<!-- 弹窗 -->
<template>
  <div
    class="popUp-container"
    :style="{
      width: $attrs.width + 'px',
      height: $attrs.height + 'px',
      border: $attrs.bgImg ? 'none' : '1px solid #a6a6a6'
    }"
  >
    <button class="close-btn" @click="close">关闭</button>
    <div style="height: 100%; width: 100%; position: relative">
      <div
        v-for="item in children"
        :key="item.id"
        :style="{
          position: 'absolute',
          width: `${item.cptWidth}px`,
          height: `${item.cptHeight}px`,
          top: Math.round(item.cptY) + 'px',
          left: Math.round(item.cptX) + 'px'
        }"
      >
        <component
          :is="item.cptKey"
          :id="item.id"
          :ref="item.id"
          :width="Math.round(item.cptWidth)"
          :height="Math.round(item.cptHeight)"
          :option="item.cptOption"
          :indexId="item.cptIndexId"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getFileAccessHttpUrl } from '@/api/manage'

export default {
  name: 'CptPopUp',
  props: {
    option: Object,
    children: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      src: require('@/assets/img/popUp-default.png')
    }
  },
  methods: {
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    open() {
      this.$emit('popup', true)
    },
    close() {
      this.$emit('popup', false)
    }
  }
}
</script>

<style scoped>
.popUp-container {
  position: relative;
  isolation: isolate;
}
.close-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10000;
}
</style>
