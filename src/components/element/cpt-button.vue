<template>
  <button
    style="border: none; cursor: pointer"
    :style="{
      width: $attrs.width + 'px',
      height: $attrs.height + 'px',
      background: option.attribute.bgColor,
      color: option.attribute.textColor,
      borderRadius: option.attribute.radius + 'px'
    }"
    @click="redirect()"
  >
    {{ cptData.value }}
  </button>
</template>

<script>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData'
export default {
  name: 'CptButton',
  props: { option: Object },
  data() {
    return {
      cptData: {},
      uuid: null
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
    this.refreshCptData()
  },
  methods: {
    refreshCptData() {
      pollingRefresh(this.uuid, this.option.cptDataForm, this.loadData)
    },
    loadData() {
      getDataJson(this.option.cptDataForm).then((res) => {
        this.cptData = res
      })
    },
    redirect() {
      if (this.option.attribute.url) {
        if (this.option.attribute.url.startsWith('view')) {
          this.$router.push(this.option.attribute.url)
          this.$emit('reload')
        } else {
          window.open(this.option.attribute.url)
        }
      }
    }
  }
}
</script>

<style scoped></style>
