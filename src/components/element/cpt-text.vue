<template>
  <div
    :style="{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'baseline',
      background: option.attribute.background + '!important',
      backgroundImage: option.attribute.backgroundImage
        ? `url('${getFileAccessHttpUrl(option.attribute.backgroundImage)}')`
        : 'none',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center center',
      backgroundSize: '100% 100%',
      height: '100%'
    }"
  >
    <span :style="$getStyle(option.attribute.prefixStyle)">{{ option.attribute.prefix }}</span>
    <img
      v-if="option.attribute.hasArrow"
      :src="
        require(computedVal >= 0 || isNaN(Number(computedVal))
          ? '@/assets/img/arrow-up.png'
          : '@/assets/img/arrow-down.png')
      "
      alt=""
      :style="{ height: valueStyle.fontSize || valueStyle.height || '20px' }"
    />
    <span
      :style="{
        ...valueStyle,
        color: option.attribute.changeColor
          ? computedVal >= 0 || isNaN(Number(computedVal))
            ? option.attribute.positiveColor
            : option.attribute.negativeColor
          : valueStyle?.color
      }"
      >{{ option.attribute.changeColor ? Math.abs(computedVal) || computedVal : computedVal }}</span
    >
    <span
      :style="{
        ...$getStyle(option.attribute.suffixStyle),
        color: option.attribute.changeColor
          ? computedVal >= 0 || isNaN(Number(computedVal))
            ? option.attribute.positiveColor
            : option.attribute.negativeColor
          : $getStyle(option.attribute.suffixStyle)?.color
      }"
      >{{ option.attribute.suffix }}</span
    >
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { pollingRefresh } from '@/utils/refreshCptData'
import { postAction } from '@/api/manage'
import { getFileAccessHttpUrl } from '@/api/manage'
export default {
  name: 'CptText',
  props: {
    id: String,
    option: Object,
    pIndexSetData: Array,
    pIndexSetId: String
  },
  data() {
    return {
      cptData: {},
      uuid: null,
      directQueryOption: [] // 直接由本组件绑定的查询控件，不经过父容器传递
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCpt']),
    computedVal() {
      return this.option.attribute.indexSet && this.option.attribute.column
        ? this.option.attribute.cptData?.value
        : this.cptData.value
    },
    valueStyle() {
      return this.$getStyle(this.option.attribute.style)
    }
  },
  watch: {
    'option.attribute.column'() {
      this.loadData()
    },
    directQueryOption: {
      handler() {
        this.refreshCptData()
      },
      deep: true
    },
    pIndexSetData: {
      handler: 'refreshCptData',
      deep: true
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
    this.refreshCptData()
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const foundIndex = this.directQueryOption.findIndex(
          (item) => item.column === columnInfo.columns[index].name
        )
        if (foundIndex < 0)
          this.directQueryOption.push({
            value: columnInfo.option,
            column: columnInfo.columns[index].name
          })
        else this.directQueryOption[foundIndex].value = columnInfo.option
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off('onTabClick')
  },

  methods: {
    refreshCptData() {
      pollingRefresh(this.uuid, this.option.cptDataForm, this.loadData)
    },
    async loadData() {
      if (this.option.attribute.inheritPIndexSet) this.option.attribute.indexSet = this.pIndexSetId
      if (this.option.attribute.indexSet && this.option.attribute.column) {
        let data = []
        const queryOption = this.directQueryOption
        const { attribute } = this.option
        const { column, dataIndex } = attribute
        if (this.pIndexSetId === this.option.attribute.indexSet) {
          // 绑定父容器指标集，无需重新获取数据
          if (!this.pIndexSetData) return
          data = this.pIndexSetData
        } else {
          const where = queryOption.length
            ? queryOption.map((item) => {
                return {
                  column: item.column,
                  value: item.value,
                  operator: 'eq'
                }
              })
            : []
          const dataRes = await postAction(
            'index/scCockpitIndexGroupConfig/previewData' + '?id=' + this.option.attribute.indexSet,
            {
              pageNo: 1,
              pageSize: 1000,
              connType: 'And',
              where
            }
          )
          if (dataRes.success && dataRes.result.records) data = dataRes.result.records
          else return
        }
        // 确保data存在且非空
        if (Array.isArray(data)) {
          // 情况1：获取单个匹配项
          if (dataIndex === undefined) {
            const targetValue = data[0][column]
            if (targetValue !== undefined) {
              attribute.cptData = { value: targetValue }
            }
          }
          // 情况2：获取多个匹配项中的指定数据
          else {
            const targetItem = data[dataIndex]
            const targetValue = targetItem[column]
            if (targetValue !== undefined) {
              attribute.cptData = { value: targetValue }
            }
          }
        }
        // 数据格式非数组
        else {
          // 直接尝试从数据根对象中获取属性
          const fallbackValue = data[column]
          if (fallbackValue !== undefined) {
            attribute.cptData = { value: fallbackValue }
          }
        }
        if (!this.option.attribute.cptData.value) {
          this.$message.warning('文本的绑定字段不存在')
        }
      } else {
        this.cptData = JSON.parse(this.option.cptDataForm.dataText)
      }
    },
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    }
  }
}
</script>

<style scoped></style>
