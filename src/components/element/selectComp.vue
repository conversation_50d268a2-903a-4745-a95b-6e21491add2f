<template>
  <div class="select-container">
    <el-select :id="uuid" v-model="currentOption" input-class="el-select-input" @change="onSelect">
      <el-option
        v-for="item in customOptions"
        :key="item.id"
        :label="item.text"
        :value="item.text"
        >{{ item.text }}</el-option
      >
      <el-option
        v-for="item in indexSetOptions"
        :key="item.id"
        :label="item.text"
        :value="item.text"
        >{{ item.text }}</el-option
      >
    </el-select>
  </div>
</template>
<script>
import { postAction } from '@/api/manage'

export default {
  name: 'SelectComp',
  props: {
    option: Object,
    width: Number,
    height: Number,
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    }
  },
  data() {
    return {
      uuid: '',
      indexSetOptions: [],
      currentOption: ''
    }
  },
  computed: {
    customOptions() {
      // 自定义tab内容
      return this.option.attribute.customOptions
        ? this.option.attribute.customOptions.split('*').map((item) => {
            return {
              text: item,
              id: require('nanoid').nanoid(6)
            }
          })
        : []
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
  },
  mounted() {
    this.refreshCptData()
  },
  methods: {
    refreshCptData() {
      this.$nextTick(() => {
        const inputInner = document.getElementById(this.uuid)
        const selectStyle = this.$getStyle(this.option.attribute.selectStyle)
        if (selectStyle) {
          for (const key of Object.keys(selectStyle)) {
            inputInner.style[key] = selectStyle[key]
          }
        }
      })

      this.indexSetOptions = []
      this.option.attribute.ownColumn && this.getOptionsFromIndexSet()
      if (!this.option.attribute.customOptions && !this.option.attribute.ownColumn) {
        this.option.attribute.bindingElements &&
          this.option.attribute.bindingElements[0] &&
          this.getOptionsFromBindingElements()
      }
    },
    async getOptionsFromIndexSet() {
      const res = await postAction(
        `/index/scCockpitIndexGroupConfig/previewData?id=${this.option.attribute.ownIndexSet}`,
        {
          connType: 'And',
          pageNo: 1,
          pageSize: 1000
        }
      )
      if (res.success && res.result.records.length) {
        const records = Array.from(
          new Set(
            res.result.records.map((record) => {
              return record[this.option.attribute.ownColumn]
            })
          )
        )
        this.indexSetOptions = records.map((text) => {
          return {
            text,
            id: require('nanoid').nanoid(6)
          }
        })
        this.currentOption = this.indexSetOptions[0].text
        this.onSelect(this.currentOption)
      } else if (!res.result.records.length) {
        this.$message.warning('该字段数据为空')
      }
    },
    async getOptionsFromBindingElements() {
      let arr = []
      this.option.attribute.bindingElements.map((element) => {
        arr.push(
          postAction(`/index/scCockpitIndexGroupConfig/previewData?id=${element.indexId}`, {
            connType: 'And',
            pageNo: 1,
            pageSize: 1000
          })
        )
      })
      const dataRes = await Promise.all(arr)
      let records = []
      // 选项取绑定元素的交集
      dataRes.map((item, index) => {
        if (!item.success) return
        const record = item.result.records
        records.push(
          record.map((recordItem) => {
            return recordItem[this.option.attribute.columns[index].name]
          })
        )
      })
      records = Array.from(
        new Set(records[0].filter((item) => records.every((arr) => arr.includes(item))))
      )
      this.indexSetOptions = records.map((text) => {
        return {
          text,
          id: require('nanoid').nanoid(6)
        }
      })
      this.currentOption = this.indexSetOptions[0].text
      this.onSelect(this.currentOption)
    },
    onSelect(text) {
      this.$bus.$emit('onTabClick', {
        elementsId: this.option.attribute.bindingElements,
        columns: this.option.attribute.columns,
        option: text
      })
    }
  }
}
</script>
<style lang="less" scoped>
.select-container {
  height: 100%;
  width: 100%;

  .el-select {
    height: inherit;
    width: inherit;

    ::v-deep .el-input,
    ::v-deep .el-input__inner {
      height: inherit;
      width: inherit;
    }
  }
}
</style>
