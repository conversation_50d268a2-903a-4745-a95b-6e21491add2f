<template>
  <div
    :id="uuid"
    class="container-group"
    :style="{
      width: width + 'px',
      height: height + 'px',
      backgroundImage: overAllBackgroundImage ? `url('${overAllBackgroundImage}')` : 'none',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center center',
      backgroundSize: '100% 100%'
    }"
  >
    <!-- 外层行循环 -->
    <div
      v-for="(row, rowIndex) in computedLayoutIndex"
      :key="rowIndex"
      class="container-row"
      :style="getRowStyle(rowIndex)"
    >
      <!-- 行内容器循环 -->
      <div
        v-for="(container, containerIndex) in row"
        :key="'container' + containerIndex + 1"
        class="container"
        :style="{
          ...getContainerStyle(row),
          backgroundImage: backgroundImage ? `url('${backgroundImage}')` : 'none',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center center',
          backgroundSize: '100% 100%'
        }"
      >
        <!-- 容器内元素 -->
        <span
          v-for="(item, index) in computedIndexLayout"
          :key="index"
          :style="{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'baseline',
            marginBottom: computedInnerRowGap[item] + 'px'
          }"
        >
          <!-- 一行内的指标 -->
          <span v-for="(element, i) in item" :key="i">
            <!-- 单个指标 -->
            <span
              v-show="
                !option.attribute.valueColumn[element] ||
                !!chartData[containerIndex + getSum(rowIndex)][element]
              "
            >
              <span
                class="prefix"
                :style="
                  getStyle(computedPrefix.prefixStyle ? computedPrefix.prefixStyle[element] : '')
                "
                >{{ computedPrefix.prefix[element] }}</span
              >
              <span
                class="value"
                :style="{
                  color: option.attribute.addArrow.includes(
                    computedValueColumn.valueColumn[element]
                  )
                    ? +chartData[containerIndex + getSum(rowIndex)][element] > 0
                      ? option.attribute.positiveColor
                      : option.attribute.negativeColor
                    : '',
                  ...getStyle(
                    computedValueColumn.valueStyle ? computedValueColumn.valueStyle[element] : '',
                    option.attribute.addArrow.includes(computedValueColumn.valueColumn[element])
                  )
                }"
                >{{
                  Math.abs(chartData[containerIndex + getSum(rowIndex)][element]) ||
                  chartData[containerIndex + getSum(rowIndex)][element]
                }}</span
              >
              <span
                class="suffix"
                :style="
                  getStyle(computedSuffix.suffixStyle ? computedSuffix.suffixStyle[element] : '')
                "
                >{{ computedSuffix.suffix[element] }}</span
              >
            </span>
          </span>
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import { getFileAccessHttpUrl, getAction, postAction } from '@/api/manage'
export default {
  name: 'CustomTextGroup',
  props: {
    option: Object,
    width: Number,
    height: Number,
    indexId: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    }
  },
  data() {
    return {
      chartData: [],
      querys: []
    }
  },
  computed: {
    computedLayoutIndex() {
      if (!this.option?.attribute?.layout) {
        return this.chartData.map((_, index) => [index])
      }

      return this.option.attribute.layout.split('*').reduce((acc, curr) => {
        const start = acc.flat().length || 0
        const parsedValue = +curr // 转换为数字
        // 子数组长度不超过数据个数，且至少为0（处理无效值）
        const maxLength = Math.min(Math.max(parsedValue, 0), this.chartData.length)
        acc.push(Array.from({ length: maxLength }, (_, i) => start + i))
        return acc
      }, [])
    },
    computedIndexLayout() {
      if (!this.option?.attribute?.indexLayout) {
        return this.chartData.map((_, index) => [index])
      }
      return this.option.attribute.indexLayout.split('*').reduce((acc, curr) => {
        const start = acc.flat().length || 0
        const parsedValue = +curr // 转换为数字
        // 子数组长度不超过指标个数，且至少为0（处理无效值）
        const maxLength = Math.min(
          Math.max(parsedValue, 0),
          this.option.attribute.valueColumn.length
        )
        acc.push(Array.from({ length: maxLength }, (_, i) => start + i))
        return acc
      }, [])
    },
    computedInnerRowGap() {
      return this.option?.attribute?.innerRowGap?.split('*')
    },
    computedPrefix() {
      return {
        prefix: this.option?.attribute?.prefix,
        prefixStyle: this.option?.attribute?.prefixStyle?.split('$').map((item) => {
          return item.trim().replace(/^(\n+)|(\n+)$/g, '')
        })
      }
    },
    computedValueColumn() {
      return {
        valueColumn: this.option?.attribute?.valueColumn,
        valueStyle: this.option?.attribute?.valueStyle?.split('$').map((item) => {
          return item.trim().replace(/^(\n+)|(\n+)$/g, '')
        })
      }
    },
    computedSuffix() {
      return {
        suffix: this.option?.attribute?.suffix,
        suffixStyle: this.option?.attribute?.suffixStyle?.split('$').map((item) => {
          return item.trim().replace(/^(\n+)|(\n+)$/g, '')
        })
      }
    },
    computedContainerStyle() {
      return this.getStyle(this.option.attribute.containerStyle)
    },
    overAllBackgroundImage() {
      return this.option.attribute.overAllBgImage
        ? getFileAccessHttpUrl(this.option.attribute.overAllBgImage)
        : ''
    },
    backgroundImage() {
      return this.option.attribute.bgImage
        ? getFileAccessHttpUrl(this.option.attribute.bgImage)
        : ''
    }
  },
  watch: {
    // 'option.attribute': {
    //   handler: 'refreshCptData',
    //   deep: true,
    //   immediate: true
    // },
    paramsForUnBindingComp: {
      handler(val) {
        val && this.loadData()
      },
      deep: true
    }
  },

  created() {
    this.uuid = require('nanoid').nanoid(12)
  },
  mounted() {
    this.refreshCptData()
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const query = {
          value: columnInfo.option,
          column: columnInfo.columns[index].name
        }
        const foundIndex = this.querys.findIndex((item) => item.column === query.column)
        if (foundIndex < 0) this.querys.push(query)
        else this.querys[foundIndex].value = query.value
        this.loadData(this.querys)
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off('onTabClick')
  },
  methods: {
    refreshCptData() {
      this.loadData()

      // pollingRefresh(this.uuid, this.option.cptDataForm, this.loadData)
    },
    async loadData(querys = []) {
      if (this.option.attribute.indexSet) {
        // 已绑定组件
        const dataRes = await postAction(
          `/index/scCockpitIndexGroupConfig/previewData?id=${this.option.attribute.indexSet}`,
          {
            connType: '',
            pageNo: 1,
            pageSize: 1000,
            where: querys.length
              ? querys.map((item) => {
                  return { column: item.column, value: item.value, operator: 'eq' }
                })
              : []
          }
        )
        let data = dataRes.result.records
        this.chartData = data.map((item) => {
          return this.computedValueColumn.valueColumn.map((column) => {
            return item[column]
          })
        })
      }
    },
    getRowStyle(rowIndex) {
      const { rowGap } = this.option.attribute
      const totalRows = this.computedLayoutIndex.length
      return {
        marginBottom: rowIndex < totalRows - 1 ? `${rowGap}px` : 0,
        height: `calc((100% - ${(totalRows - 1) * rowGap}px) / ${totalRows})`
      }
    },
    getContainerStyle(row) {
      const { columnGap } = this.option.attribute
      return {
        background: this.backgroundImage
          ? `url(${this.backgroundImage}) no-repeat center center / 100% 100%`
          : 'none',
        width: `calc((100% - ${(row.length - 1) * columnGap}px) / ${row.length})`,
        ...this.computedContainerStyle
      }
    },
    getSum(rowIndex) {
      return this.computedLayoutIndex.slice(0, rowIndex).reduce((sum, item) => sum + item.length, 0)
    },
    getStyle(styleStr, hasPresetColor = false) {
      if (!styleStr) return
      let style = {}
      try {
        style = new Function('return ' + styleStr)() // 字符串转对象，并去掉了'option = ''
      } catch (error) {
        // console.error('解析字符串代码时出错:', error)
      }
      if (hasPresetColor && style.color) {
        delete style.color
      }
      return style
    }
  }
}
</script>
<style lang="less" scoped>
.container-group {
  height: 100%;
  width: 100%;
}
.container-row {
  display: flex;
  justify-content: space-between;
  text-align: center;
  position: relative;
}
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
</style>
