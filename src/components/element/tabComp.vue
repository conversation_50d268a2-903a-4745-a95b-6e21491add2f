<template>
  <div :id="uuid" class="tabs-container">
    <div
      v-if="!option.attribute.type"
      :style="{
        display: 'flex',
        justifyContent: 'space-between',
        gap: option.attribute.tabGap + 'px',
        overflowX: 'auto'
      }"
    >
      <div
        v-for="item in customOptions"
        :key="item.id"
        class="tab"
        :style="{
          ...$getStyle(
            activeId === item.id ? option.attribute.activeStyle : option.attribute.inActiveStyle
          ),
          cursor: 'pointer'
        }"
        @click="onTabOptionClick(item.id, item.text)"
      >
        {{ item.text }}
      </div>
      <div
        v-for="item in indexSetOptions"
        :key="item.id"
        class="tab"
        :style="{
          ...$getStyle(
            activeId === item.id ? option.attribute.activeStyle : option.attribute.inActiveStyle
          ),
          cursor: 'pointer'
        }"
        @click="onTabOptionClick(item.id, item.text)"
      >
        {{ item.text }}
      </div>
    </div>

    <el-tabs v-if="option.attribute.type === 1" v-model="activeName" @tab-click="onTabPageClick">
      <el-tab-pane
        v-for="item in customOptions"
        :key="item.id"
        class="tab"
        :label="item.text"
      ></el-tab-pane>
      <el-tab-pane
        v-for="item in indexSetOptions"
        :key="item.id"
        class="tab"
        :label="item.text"
      ></el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'TabComp',
  props: {
    option: Object,
    width: Number,
    height: Number,
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    }
  },
  data() {
    return {
      activeId: '',
      activeName: '',
      uuid: '',
      indexSetOptions: []
    }
  },
  computed: {
    customOptions() {
      // 自定义tab内容
      return this.option.attribute.customOptions
        ? this.option.attribute.customOptions.split('*').map((item) => {
            return {
              text: item,
              id: require('nanoid').nanoid(6)
            }
          })
        : []
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
  },
  mounted() {
    this.refreshCptData()
  },
  beforeDestroy() {
    this.activeId = ''
  },
  methods: {
    refreshCptData() {
      this.indexSetOptions = []
      this.option.attribute.ownColumn && this.getOptionsFromIndexSet()
      if (!this.option.attribute.customOptions && !this.option.attribute.ownColumn) {
        this.option.attribute.bindingElements &&
          this.option.attribute.bindingElements[0] &&
          this.getOptionsFromBindingElements()
      }
    },
    async getOptionsFromIndexSet() {
      const res = await postAction(
        `/index/scCockpitIndexGroupConfig/previewData?id=${this.option.attribute.ownIndexSet}`,
        {
          connType: 'And',
          pageNo: 1,
          pageSize: 1000
        }
      )
      if (res.success && res.result.records.length) {
        const records = Array.from(
          new Set(
            res.result.records.map((record) => {
              return record[this.option.attribute.ownColumn]
            })
          )
        )
        this.indexSetOptions = records.map((text) => {
          return {
            text,
            id: require('nanoid').nanoid(6)
          }
        })
        this.onTabOptionClick(this.indexSetOptions[0].id, this.indexSetOptions[0].text)
      } else if (!res.result.records.length) {
        this.$message.warning('该字段数据为空')
      }
    },
    async getOptionsFromBindingElements() {
      let arr = []
      this.option.attribute.bindingElements.map((element) => {
        arr.push(
          postAction(`/index/scCockpitIndexGroupConfig/previewData?id=${element.indexId}`, {
            connType: 'And',
            pageNo: 1,
            pageSize: 1000
          })
        )
      })
      const dataRes = await Promise.all(arr)
      let records = []
      // 选项取绑定元素的交集
      dataRes.map((item, index) => {
        if (!item.success) return
        const record = item.result.records
        records.push(
          record.map((recordItem) => {
            return recordItem[this.option.attribute.columns[index]?.name]
          })
        )
      })
      records = Array.from(
        new Set(records[0].filter((item) => records.every((arr) => arr.includes(item))))
      )
      this.indexSetOptions = records.map((text) => {
        return {
          text,
          id: require('nanoid').nanoid(6)
        }
      })
      this.onTabOptionClick(this.indexSetOptions[0].id, this.indexSetOptions[0].text)
    },
    onTabOptionClick(id, text) {
      this.activeId = id
      this.$bus.$emit('onTabClick', {
        elementsId: this.option.attribute.bindingElements,
        columns: this.option.attribute.columns,
        option: text
      })
    },
    onTabPageClick(tab) {
      const item = this.indexSetOptions[tab.index] || this.customOptions[tab.index]
      this.activeId = item.id
      this.$bus.$emit('onTabClick', {
        elementsId: this.option.attribute.bindingElements,
        columns: this.option.attribute.columns,
        option: item.text
      })
    }
  }
}
</script>
<style lang="less" scoped>
::-webkit-scrollbar {
  display: none;
}
</style>
