<template>
  <div :style="{ width: $attrs.width + 'px', height: $attrs.height + 'px' }">
    <el-image
      style="width: 100%; height: 100%"
      :preview-src-list="option.attribute.preview ? [option.attribute.url] : []"
      :src="option.attribute.url ? fileUrl + option.attribute.url : require('@/assets/logo.png')"
      :fit="option.attribute.fit"
      @dragstart.prevent
    />
  </div>
</template>

<script>
import { fileUrl } from '/env'
export default {
  name: 'CptImage',
  props: { option: Object },
  data() {
    return {
      fileUrl: window._CONFIG['staticDomainURL'] + '/'
    }
  }
}
</script>

<style scoped></style>
