<template>
  <div>
    <el-form label-width="80px" size="mini">
      <el-form-item label="组件地址">
        <el-input v-model="attribute.url">
          <template slot="prepend">@/components/custom/</template>
          <template slot="append">.vue</template>
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'CustomComponentOption',
  props: {
    attribute: Object
  },
  data() {
    return {}
  }
}
</script>

<style scoped></style>
