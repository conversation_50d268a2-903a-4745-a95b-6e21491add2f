<template>
  <div>
    <el-form label-width="80px" size="mini">
      <el-form-item label="背景图">
        <j-image-upload v-model="attribute.bgImage" />
      </el-form-item>
      <el-form-item label="排列方式">
        <el-input v-model="attribute.c" placeholder="请输入排列方式(x*y*z*...)" />
      </el-form-item>
      <el-form-item label="行间距">
        <el-input-number v-model="attribute.rowGap" placeholder="请输入行间距" />
      </el-form-item>
      <el-form-item label="列间距">
        <el-input-number v-model="attribute.columnGap" placeholder="请输入列间距" />
      </el-form-item>
      <div style="font-size: 10px; color: #666; font-style: italic; margin-left: 80px">
        例：{"height": "100px", "width":"100px"}
      </div>
      <el-form-item label="维度样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('dimenssionStyle', attribute.dimenssionStyle)"
          >css转js</el-button
        >
        <editor
          ref="dimenssionStyle"
          v-model="attribute.dimenssionStyle"
          width="100%"
          height="200px"
          :options="editorOptions"
          @init="editorInit"
        ></editor>
      </el-form-item>
      <el-form-item label="指标样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('indexStyle', attribute.indexStyle)"
          >css转js</el-button
        >
        <editor
          ref="indexStyle"
          v-model="attribute.indexStyle"
          width="100%"
          height="200px"
          :options="editorOptions"
          @init="editorInit"
        ></editor>
      </el-form-item>
      <el-form-item label="单位样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('unitStyle', attribute.unitStyle)"
          >css转js</el-button
        >
        <editor
          ref="unitStyle"
          v-model="attribute.unitStyle"
          width="100%"
          height="200px"
          :options="editorOptions"
          @init="editorInit"
        ></editor>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'TextContainerGroupOption',
  props: { attribute: Object },
  data() {
    return {
      editorOptions: {
        tabSize: 4,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      }
    }
  },
  mounted() {
    this.changeTheme()
    this.changeLanguage()
    this.editorInit()
    this.$refs.dimenssionStyle.editor.resize()
    this.$refs.indexStyle.editor.resize()
    this.$refs.unitStyle.editor.resize()
  },
  methods: {
    changeLanguage() {
      this.$refs.dimenssionStyle.editor.getSession().setMode('ace/mode/javascript')
      this.$refs.indexStyle.editor.getSession().setMode('ace/mode/javascript')
      this.$refs.unitStyle.editor.getSession().setMode('ace/mode/javascript')
    },
    changeTheme() {
      this.$refs.dimenssionStyle.editor.setTheme('ace/theme/chrome')
      this.$refs.indexStyle.editor.setTheme('ace/theme/chrome')
      this.$refs.unitStyle.editor.setTheme('ace/theme/chrome')
    },
    editorInit() {
      // language
      require('brace/mode/javascript')
      // theme
      require('brace/theme/chrome')
    },
    convertCssToJs(column, cssStr) {
      try {
        this.attribute[column] = this.$convertCssToJs(cssStr)
      } catch (e) {}
    }
  }
}
</script>

<style scoped></style>
