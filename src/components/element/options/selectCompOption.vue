<template>
  <div>
    <el-form label-width="80px" size="mini">
      <el-form-item label="绑定元素">
        <el-select
          v-model="attribute.bindingElements"
          multiple
          value-key="id"
          @change="onBindingElementsChange"
        >
          <el-option
            v-for="item in elements"
            :key="item.id"
            :label="item.cptOption.attribute.name"
            :value="{
              id: item.id,
              indexId: item.cptIndexId || item.cptOption.attribute.indexSet,
              name: item.cptOption.attribute.name
            }"
            >{{ item.cptOption.attribute.name }}</el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item label="绑定字段">
        <el-select
          v-for="(bindingElement, bindingElementOrder) in attribute.bindingElements"
          :key="bindingElement.id"
          v-model="attribute.columns[bindingElementOrder]"
          :placeholder="'请选择绑定元素“' + bindingElement.name + '”的字段'"
          value-key="id"
          clearable
          style="margin-bottom: 10px; width: 100%"
        >
          <el-option
            v-for="item in columns[bindingElementOrder]"
            :key="item.id"
            :value="{ name: item.sourceColumn, id: item.id }"
            :label="item.sourceColumn"
            >{{ item.sourceColumn }}——{{ item.indexGroupName }}</el-option
          >
        </el-select>
        <el-alert
          v-show="!attribute.bindingElements.length"
          :closable="false"
          style="padding: 0"
          title="请先选择绑定元素"
          type="info"
          effect="dark"
        >
        </el-alert>
      </el-form-item>
      <el-form-item label="选项来源指标集">
        <a-tree-select
          v-model="attribute.ownIndexSet"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :replaceFields="replaceFields"
          :treeData="indexSetTree"
          :disabled="!!currentCpt.cptIndexId"
          placeholder="请选择绑定指标集"
          allow-clear
          tree-default-expand-all
          treeNodeFilterProp="title"
          @change="onOwnIndexSetChange"
        ></a-tree-select>
      </el-form-item>
      <el-form-item v-if="!!attribute.ownIndexSet" label="选项来源字段">
        <el-select v-model="attribute.ownColumn" value-key="id">
          <el-option
            v-for="item in ownColumnList"
            :key="item.id"
            :value="item.sourceColumn"
            :label="item.sourceColumn"
            >{{ item.sourceColumn }}</el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item v-if="!attribute.ownIndexSet" label="自定义选项内容(*号隔开)">
        <el-input v-model="attribute.customOptions"></el-input>
      </el-form-item>
      <el-form-item label="整体样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('selectStyle', attribute.selectStyle)"
          >css转js</el-button
        >
        <monaco-editor
          ref="selectStyle"
          v-model="attribute.selectStyle"
          language="javascript"
          width="100%"
          height="200px"
          :options="editorOptions"
        ></monaco-editor>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { traverseTree } from '@/api/api'
import { getAction } from '@/api/manage'
export default {
  name: 'SelectCompOption',
  props: {
    attribute: Object
  },
  data() {
    return {
      editorOptions: {
        tabSize: 4,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      },
      elements: [],
      columns: [],
      ownColumnList: [],
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      }
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['componentData', 'currentCpt', 'indexSetTree'])
  },
  mounted() {
    this.getElementsWithIndexId() // 获取所有绑定了指标集的组件或模块
    this.onBindingElementsChange(this.attribute.bindingElements, false)
    this.$nextTick(() => {
      this.$refs.selectStyle && this.$refs.selectStyle.resize()
    })
  },
  methods: {
    getElementsWithIndexId() {
      for (const node of this.componentData) {
        traverseTree(node, (node) => {
          if (node.cptIndexId || node.cptOption.attribute.indexSet) {
            //  node.cptOption.attribute.indexSet 针对画布容器等手动绑定指标集的组件
            this.elements.push(node)
          }
        })
      }
    },
    onBindingElementsChange(keys, doClearColumns = true) {
      // 获取选中指标集的字段
      if (doClearColumns) this.attribute.columns = []
      const promises = keys.map((key) => {
        return new Promise((resolve) => {
          getAction('/index/scCockpitIndexGroupConfig/queryById', { id: key.indexId })
            .then((res) =>
              resolve(
                res.result.groupColumnList
                  .filter((item) => item.indexType === 'dimenssion')
                  .concat(res.result.queryColumnList)
                  .map((groupColumn) => {
                    return {
                      ...groupColumn,
                      indexGroupName: res.result.indexGroupName
                    }
                  })
              )
            )
            .catch((error) => {
              console.error('获取指标集数据失败:', error)
              resolve([])
            })
        })
      })
      Promise.all(promises).then((results) => {
        this.columns = []
        for (let i = 0; i < results.length; i++) {
          const seen = new Set()
          this.columns[i] = []
          for (const obj of results[i]) {
            if (!seen.has(obj.sourceColumn)) {
              seen.add(obj.sourceColumn)
              this.columns[i].push(obj)
            } else continue
          }
        }
      })
    },
    onOwnIndexSetChange(id) {
      this.attribute.customOptions = ''
      this.attribute.ownColumn = ''
      getAction('/index/scCockpitIndexGroupConfig/queryById', { id }).then((res) => {
        if (res.success) {
          this.ownColumnList = res.result.groupColumnList
        } else {
          this.ownColumnList = []
          this.$message.warning('指标集信息查询失败')
        }
      })
    },
    convertCssToJs(column, cssStr) {
      try {
        this.attribute[column] = this.$convertCssToJs(cssStr)
      } catch (e) {}
    }
  }
}
</script>

<style scoped></style>
