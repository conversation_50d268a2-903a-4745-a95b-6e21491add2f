<template>
  <div>
    <el-form label-width="80px" size="mini">
      <el-form-item label="绑定指标集">
        <a-tree-select
          v-model="attribute.indexSet"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :replaceFields="replaceFields"
          :treeData="indexSetTree"
          :disabled="!!currentCpt.cptIndexId"
          placeholder="请选择绑定指标集"
          allow-clear
          tree-default-expand-all
          treeNodeFilterProp="title"
          @change="(val) => onIndexSetChange(val, false)"
        ></a-tree-select>
      </el-form-item>
      <el-form-item label="使用字段">
        <el-select v-model="attribute.columnUsed" multiple>
          <el-option
            v-for="column in columns"
            :key="column.id"
            :value="column.sourceColumn"
            :label="column.sourceColumn"
            >{{ column.sourceColumn }}</el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item label="整体样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('overallStyle', attribute.overallStyle)"
          >css转js</el-button
        >
        <monaco-editor
          ref="overallStyle"
          v-model="attribute.overallStyle"
          language="javascript"
          width="100%"
          height="200px"
          :options="editorOptions"
        ></monaco-editor>
      </el-form-item>
      <el-form-item label="表头样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('theadStyle', attribute.theadStyle)"
          >css转js</el-button
        >
        <monaco-editor
          ref="theadStyle"
          v-model="attribute.theadStyle"
          language="javascript"
          width="100%"
          height="200px"
          :options="editorOptions"
        ></monaco-editor>
      </el-form-item>
      <el-form-item label="行样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('trowStyle', attribute.trowStyle)"
          >css转js</el-button
        >
        <monaco-editor
          ref="trowStyle"
          v-model="attribute.trowStyle"
          language="javascript"
          width="100%"
          height="200px"
          :options="editorOptions"
        ></monaco-editor>
      </el-form-item>

      <el-form-item label="单元格样式" style="position: relative">
        <el-button
          style="position: absolute; top: 60px; left: -75px"
          @click="convertCssToJs('tdStyle', attribute.tdStyle)"
          >css转js</el-button
        >
        <monaco-editor
          ref="tdStyle"
          v-model="attribute.tdStyle"
          language="javascript"
          width="100%"
          height="200px"
          :options="editorOptions"
        ></monaco-editor>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { getAction } from '@/api/manage'
export default {
  name: 'TableCompOption',
  props: {
    attribute: Object
  },
  data() {
    return {
      editorOptions: {
        tabSize: 4,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      },
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      },
      columns: []
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCpt', 'indexSetTree'])
  },
  mounted() {
    this.getIndexSetInfo(null, false)
    this.$nextTick(() => {
      this.$refs.overallStyle && this.$refs.overallStyle.resize()
      this.$refs.theadStyle && this.$refs.theadStyle.resize()
      this.$refs.trowStyle && this.$refs.trowStyle.resize()
      this.$refs.tdStyle && this.$refs.tdStyle.resize()
    })
  },
  methods: {
    onIndexSetChange(val, fill) {
      this.attribute.columnUsed = []
      this.getIndexSetInfo(fill)
    },
    getIndexSetInfo(fill = true) {
      if (this.currentCpt.cptIndexId) this.attribute.indexSet = this.currentCpt.cptIndexId
      else if (!this.attribute.indexSet && !this.currentCpt.cptIndexId)
        this.attribute.indexSet = this.currentCpt.pIndexSetId
      getAction('/index/scCockpitIndexGroupConfig/queryById', {
        id: this.attribute.indexSet
      }).then((res) => {
        if (res.success) {
          this.$nextTick(() => {
            this.columns = res.result.groupColumnList
            if (fill)
              this.attribute.columnUsed = this.columns
                .map((column) => column.sourceColumn)
                .reverse()
          })
        }
      })
    },
    convertCssToJs(column, cssStr) {
      try {
        this.attribute[column] = this.$convertCssToJs(cssStr)
      } catch (e) {}
    }
  }
}
</script>

<style scoped></style>
