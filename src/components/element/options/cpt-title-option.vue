<template>
  <el-form label-width="80px" size="mini">
    <el-form-item label="标题类型">
      <el-radio-group v-model="attribute.type" @input="handleChangeStyle">
        <el-radio :label="0">一级标题</el-radio>
        <el-radio :label="1">二级标题</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="attribute.type === 0" label="背景图片">
      <j-image-upload v-model="attribute.iconBefore"></j-image-upload>
      <el-button @click="showGallery">图库选择</el-button>
    </el-form-item>
    <el-form-item label="文本内容">
      <el-input v-model="attribute.content"></el-input>
    </el-form-item>
    <el-form-item label="文本样式">
      <monaco-editor
        ref="style"
        v-model="attribute.style"
        language="javascript"
        width="100%"
        height="200px"
        :options="editorOptions"
      ></monaco-editor>
    </el-form-item>
    <gallery ref="gallery" @confirmCheck="confirmCheck" />
  </el-form>
</template>
<script>
export default {
  name: 'CptTitleOption',
  props: {
    attribute: Object
  },
  data() {
    return {
      editorOptions: {
        tabSize: 4,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      },
      style1: `{
    fontFamily:'SourceHanSans',
    fontSize: '40px',
    fontWeight: 'bold',
    lineHeight:'59px',
    color:'#876af2',
    fontStyle:'italic'
}`,
      style2: `{
    fontFamily: 'PingFangSC, PingFang SC',
    fontWeight: '500',
    fontSize: '36px',
    color: '#212121',
    lineHeight: '50px',
    textAlign: 'left',
    fontStyle: 'normal'
}`
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.style && this.$refs.style.resize()
    })
  },
  methods: {
    handleChangeStyle(type) {
      this.attribute.style = type ? this.style2 : this.style1
    },
    showGallery() {
      this.$refs.gallery.opened()
    },
    confirmCheck(fileUrl) {
      this.attribute.iconBefore = fileUrl
    }
  }
}
</script>
