<template>
  <div>
    <el-form label-width="80px" size="mini">
      <el-form-item label="进度条类型">
        <el-radio-group v-model="attribute.barType">
          <el-radio :label="0">条形</el-radio>
          <el-radio :label="1">环形</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="外层颜色">
        <el-color-picker v-model="attribute.outterColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item label="内层颜色">
        <el-color-picker v-model="attribute.innerColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item label="进度条弧度">
        <el-input-number v-model="attribute.borderRadius" />
      </el-form-item>
      <el-form-item label="默认数值">
        <el-input-number v-model="attribute.defaultVal"
      /></el-form-item>
      <el-form-item label="是否继承父容器指标集">
        <el-radio-group v-model="attribute.inheritPIndexSet">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="绑定指标集">
        <a-tree-select
          v-model="attribute.indexSet"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :replaceFields="replaceFields"
          :treeData="indexSetTree"
          :disabled="!!currentCpt.cptIndexId"
          placeholder="请选择绑定指标集"
          allow-clear
          tree-default-expand-all
          treeNodeFilterProp="title"
          @change="onIndexSetChange"
        ></a-tree-select>
      </el-form-item>
      <el-form-item label="外层字段">
        <el-select v-model="attribute.outterColumn" clearable
          ><el-option
            v-for="item in columns"
            :key="item.id"
            :value="item.sourceColumn"
            :label="item.sourceColumn"
            >{{ item.sourceColumn }}</el-option
          ></el-select
        >
      </el-form-item>
      <el-form-item label="内层字段">
        <el-select v-model="attribute.innerColumn" clearable
          ><el-option
            v-for="item in columns"
            :key="item.id"
            :value="item.sourceColumn"
            :label="item.sourceColumn"
            >{{ item.sourceColumn }}</el-option
          ></el-select
        >
      </el-form-item>
      <el-form-item label="数据索引">
        <el-input-number v-model="attribute.dataIndex" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { getAction } from '@/api/manage'
export default {
  name: 'ProgressBarOption',
  props: { attribute: Object },
  data: function () {
    return {
      columns: [],
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      }
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCpt', 'indexSetTree'])
  },
  mounted() {
    if (this.attribute.inheritPIndexSet) {
      console.log(this.currentCpt.pIndexSetId)

      this.attribute.indexSet = this.currentCpt.pIndexSetId
    }
    // 获取可选字段
    getAction('/index/scCockpitIndexGroupConfig/queryById', {
      id: this.attribute.indexSet
    }).then((res) => {
      if (res.success) {
        this.columns = res.result.groupColumnList
      }
    })
  },
  methods: {
    onIndexSetChange(val) {
      getAction('/index/scCockpitIndexGroupConfig/queryById', {
        id: val
      }).then((res) => {
        if (res.success) {
          this.columns = res.result.groupColumnList
        }
      })
    }
  }
}
</script>

<style scoped></style>
