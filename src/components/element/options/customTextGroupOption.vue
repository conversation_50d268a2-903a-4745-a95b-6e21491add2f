<template>
  <div>
    <el-form label-width="80px" size="mini">
      <el-form-item label="外层背景图">
        <j-image-upload v-model="attribute.overAllBgImage" />
        <el-button @click="showGallery(1)">图库选择</el-button>
      </el-form-item>
      <el-form-item label="容器背景图">
        <j-image-upload v-model="attribute.bgImage" />
        <el-button @click="showGallery(2)">图库选择</el-button>
      </el-form-item>
      <el-form-item label="绑定指标集">
        <a-tree-select
          v-model="attribute.indexSet"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :replaceFields="replaceFields"
          :treeData="indexSetTree"
          :disabled="!!currentCpt.cptIndexId"
          placeholder="请选择绑定指标集"
          allow-clear
          tree-default-expand-all
          treeNodeFilterProp="title"
        ></a-tree-select>
      </el-form-item>
      <el-form-item label="容器排列方式">
        <el-input v-model="attribute.layout" placeholder="请输入排列方式(x*y*z*...)" />
      </el-form-item>
      <el-form-item label="段落数">
        <el-input-number v-model="attribute.rowNum" placeholder="请输入段落数" />
      </el-form-item>
      <el-form-item label="段落排列方式">
        <el-input v-model="attribute.indexLayout" placeholder="请输入段落排列方式(x*y*z*...)" />
      </el-form-item>
      <el-form-item label="行间距">
        <el-input-number v-model="attribute.rowGap" placeholder="请输入行间距" />
      </el-form-item>
      <el-form-item label="列间距">
        <el-input-number v-model="attribute.columnGap" placeholder="请输入列间距" />
      </el-form-item>
      <el-form-item label="内部行间距">
        <el-input v-model="attribute.innerRowGap" placeholder="请输入内部行间距" />
      </el-form-item>
      <el-form-item label="数值字段">
        <el-select
          v-for="item in attribute.rowNum"
          v-model="attribute.valueColumn[item - 1]"
          style="width: 100%; margin-bottom: 10px"
          placeholder="请选择数值字段"
          clearable
        >
          <el-option v-for="item in valueColumns" :value="item.sourceColumn"
            >{{ item.sourceColumn }}（{{ item.indexName }}）</el-option
          ></el-select
        >
        <el-alert
          v-show="!attribute.rowNum"
          :closable="false"
          style="padding: 0"
          title="请先输入段落数"
          type="info"
          effect="dark"
        >
        </el-alert>
      </el-form-item>
      <el-form-item label="可变色数值">
        <el-select v-model="attribute.addArrow" multiple placeholder="请选择可变色数值">
          <el-option v-for="item in attribute.valueColumn" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="attribute.addArrow.length" label="正值颜色">
        <el-color-picker v-model="attribute.positiveColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item v-if="attribute.addArrow.length" label="负值颜色">
        <el-color-picker v-model="attribute.negativeColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item label="数值前缀">
        <el-input
          v-for="item in attribute.rowNum || attribute.valueColumn.length"
          v-model="attribute.prefix[item - 1]"
          style="width: 100%; margin-bottom: 10px"
        />
        <el-alert
          v-show="!attribute.rowNum && !attribute.valueColumn.length"
          :closable="false"
          style="padding: 0"
          title="请先选择数值字段"
          type="info"
          effect="dark"
        >
        </el-alert>
      </el-form-item>
      <el-form-item label="数值后缀">
        <el-input
          v-for="item in attribute.rowNum || attribute.valueColumn.length"
          v-model="attribute.suffix[item - 1]"
          style="width: 100%; margin-bottom: 10px" />
        <el-alert
          v-show="!attribute.rowNum && !attribute.valueColumn.length"
          :closable="false"
          style="padding: 0"
          title="请先选择数值字段"
          type="info"
          effect="dark"
        >
        </el-alert
      ></el-form-item>
      <div style="font-size: 10px; color: #666; font-style: italic; margin-left: 80px">
        例：<br />
        {height: "100px", width:"100px"}<br />
        $<br />
        {height: "200px", width:"200px"}<br />
        $<br />
        ......
      </div>
      <el-form-item label="容器样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('containerStyle', attribute.containerStyle)"
          >css转js</el-button
        >
        <editor
          ref="containerStyle"
          v-model="attribute.containerStyle"
          width="100%"
          height="200px"
          :options="editorOptions"
          @init="editorInit"
        ></editor>
      </el-form-item>
      <el-form-item label="前缀样式($隔开)" style="position: relative">
        <el-button
          style="position: absolute; top: 60px; left: -75px"
          @click="convertCssToJs('prefixStyle', attribute.prefixStyle)"
          >css转js</el-button
        >
        <editor
          ref="prefixStyle"
          v-model="attribute.prefixStyle"
          width="100%"
          height="200px"
          :options="editorOptions"
          @init="editorInit"
        ></editor>
      </el-form-item>
      <el-form-item label="数值样式($隔开)" style="position: relative">
        <el-button
          style="position: absolute; top: 60px; left: -75px"
          @click="convertCssToJs('valueStyle', attribute.valueStyle)"
          >css转js</el-button
        >
        <editor
          ref="valueStyle"
          v-model="attribute.valueStyle"
          width="100%"
          height="200px"
          :options="editorOptions"
          @init="editorInit"
        ></editor>
      </el-form-item>
      <el-form-item label="后缀样式($隔开)" style="position: relative">
        <el-button
          style="position: absolute; top: 60px; left: -75px"
          @click="convertCssToJs('suffixStyle', attribute.suffixStyle)"
          >css转js</el-button
        >
        <editor
          ref="suffixStyle"
          v-model="attribute.suffixStyle"
          width="100%"
          height="200px"
          :options="editorOptions"
          @init="editorInit"
        ></editor>
      </el-form-item>
    </el-form>
    <gallery ref="gallery1" @confirmCheck="(path) => confirmCheck(path, 1)" />
    <gallery ref="gallery2" @confirmCheck="(path) => confirmCheck(path, 2)" />
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { getAction } from '@/api/manage'
export default {
  name: 'CustomTextGroupOption',
  props: { attribute: Object },
  data() {
    return {
      editorOptions: {
        tabSize: 4,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      },
      valueColumns: [],
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      }
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCpt', 'currentCptId', 'indexSetTree'])
  },
  watch: {
    currentCptId() {
      this.getValueColumns()
    }
  },
  mounted() {
    this.changeTheme()
    this.changeLanguage()
    this.editorInit()
    this.getValueColumns()
    this.$refs.prefixStyle.editor.resize()
    this.$refs.valueStyle.editor.resize()
    this.$refs.suffixStyle.editor.resize()
    this.$refs.containerStyle.editor.resize()
  },
  methods: {
    getValueColumns() {
      if (this.currentCpt.cptIndexId) this.attribute.indexSet = this.currentCpt.cptIndexId
      else if (!this.attribute.indexSet && !this.currentCpt.cptIndexId)
        this.attribute.indexSet = this.currentCpt.pIndexSetId
      getAction('/index/scCockpitIndexGroupConfig/queryById', {
        id: this.attribute.indexSet
      }).then((res) => {
        if (res.success) {
          this.valueColumns = res.result.groupColumnList
        }
      })
    },
    changeLanguage() {
      this.$refs.prefixStyle.editor.getSession().setMode('ace/mode/javascript')
      this.$refs.valueStyle.editor.getSession().setMode('ace/mode/javascript')
      this.$refs.suffixStyle.editor.getSession().setMode('ace/mode/javascript')
      this.$refs.suffixStyle.editor.getSession().setMode('ace/mode/javascript')
      this.$refs.containerStyle.editor.getSession().setMode('ace/mode/javascript')
    },
    changeTheme() {
      this.$refs.prefixStyle.editor.setTheme('ace/theme/chrome')
      this.$refs.valueStyle.editor.setTheme('ace/theme/chrome')
      this.$refs.suffixStyle.editor.setTheme('ace/theme/chrome')
      this.$refs.containerStyle.editor.setTheme('ace/theme/chrome')
    },
    editorInit() {
      // language
      require('brace/mode/javascript')
      // theme
      require('brace/theme/chrome')
    },
    convertCssToJs(column, cssStr) {
      try {
        this.attribute[column] = this.$convertCssToJs(cssStr)
      } catch (e) {}
    },
    showGallery(flag) {
      flag === 1 && this.$refs.gallery1.opened()
      flag === 2 && this.$refs.gallery2.opened()
    },
    confirmCheck(fileUrl, flag) {
      if (flag === 1) this.attribute.overAllBgImage = fileUrl
      else this.attribute.bgImage = fileUrl
    }
  }
}
</script>

<style scoped></style>
