<template>
  <div>
    <el-form label-width="90px" size="mini">
      <el-form-item label="是否继承父容器指标集">
        <el-radio-group v-model="attribute.inheritPIndexSet">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="绑定指标集">
        <a-tree-select
          v-model="attribute.indexSet"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :replaceFields="replaceFields"
          :treeData="indexSetTree"
          placeholder="请选择绑定指标集"
          allow-clear
          tree-default-expand-all
          treeNodeFilterProp="title"
          @change="onIndexSetChange"
        ></a-tree-select>
      </el-form-item>
      <el-form-item label="绑定字段">
        <el-select v-model="attribute.column" clearable>
          <el-option
            v-for="item in columns"
            :key="item.id"
            :value="item.sourceColumn"
            :label="item.sourceColumn"
            >{{ item.sourceColumn }}</el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item label="指定数据">
        <el-input-number
          v-model="attribute.dataIndex"
          :min="-1"
          style="width: 100%"
          placeholder="数据所在行在表中的索引"
        />
      </el-form-item>
      <el-form-item label="前缀">
        <el-input v-model="attribute.prefix"></el-input>
      </el-form-item>
      <el-form-item label="后缀">
        <el-input v-model="attribute.suffix"></el-input>
      </el-form-item>
      <el-form-item label="是否有箭头">
        <el-radio-group v-model="attribute.hasArrow">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否变色">
        <el-radio-group v-model="attribute.changeColor">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="attribute.changeColor" label="正值颜色">
        <el-color-picker v-model="attribute.positiveColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item v-if="attribute.changeColor" label="负值颜色">
        <el-color-picker v-model="attribute.negativeColor" show-alpha></el-color-picker>
      </el-form-item>
      <!-- hasArrow: 0,
    positiveColor: '',
    negativeColor: '', -->
      <el-form-item label="背景">
        <el-input v-model="attribute.background"></el-input>
      </el-form-item>
      <el-form-item label="背景图片">
        <j-image-upload v-model="attribute.backgroundImage"></j-image-upload>
      </el-form-item>
      <el-form-item label="样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('style', attribute.style)"
          >css转js</el-button
        >
        <monaco-editor
          ref="jsCodeEditor1"
          v-model="attribute.style"
          language="javascript"
          width="100%"
          height="200px"
          :options="editorOptions"
        ></monaco-editor>
      </el-form-item>
      <el-form-item label="前缀样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('prefixStyle', attribute.prefixStyle)"
          >css转js</el-button
        >
        <monaco-editor
          ref="jsCodeEditor2"
          v-model="attribute.prefixStyle"
          language="javascript"
          width="100%"
          height="200px"
          :options="editorOptions"
        ></monaco-editor>
      </el-form-item>
      <el-form-item label="后缀样式" style="position: relative">
        <el-button
          style="position: absolute; top: 30px; left: -75px"
          @click="convertCssToJs('suffixStyle', attribute.suffixStyle)"
          >css转js</el-button
        >
        <monaco-editor
          ref="jsCodeEditor3"
          v-model="attribute.suffixStyle"
          language="javascript"
          width="100%"
          height="200px"
          :options="editorOptions"
        ></monaco-editor>
      </el-form-item>
      <el-form-item>
        <span slot="label">
          点击跳转
          <el-tooltip
            effect="light"
            content="跳转外链请写全路径如：https://www.baidu.com，大屏间跳转请写view?id=大屏ID，如：view?id=6580439c70e1d2c2ff2b98"
            placement="bottom-end"
          >
            <i class="el-icon-warning" />
          </el-tooltip>
        </span>
        <el-input v-model="attribute.url" type="textarea" :rows="3"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { getAction } from '@/api/manage'
export default {
  name: 'CptTextOption',
  props: {
    attribute: Object
  },
  data() {
    return {
      columns: [],
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      },
      editorOptions: {
        tabSize: 4,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      }
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCpt', 'indexSetTree'])
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.jsCodeEditor1) {
        this.$refs.jsCodeEditor1.resize()
      }
      if (this.$refs.jsCodeEditor2) {
        this.$refs.jsCodeEditor2.resize()
      }
      if (this.$refs.jsCodeEditor3) {
        this.$refs.jsCodeEditor3.resize()
      }
    })
    if (this.attribute.inheritPIndexSet) {
      this.attribute.indexSet = this.currentCpt.pIndexSetId
    }
    // 获取可选字段
    getAction('/index/scCockpitIndexGroupConfig/queryById', {
      id: this.attribute.indexSet
    }).then((res) => {
      if (res.success) {
        this.columns = res.result.groupColumnList
      }
    })
  },
  methods: {
    convertCssToJs(column, cssStr) {
      try {
        this.attribute[column] = this.$convertCssToJs(cssStr)
      } catch (e) {}
    },
    onIndexSetChange(val) {
      getAction('/index/scCockpitIndexGroupConfig/queryById', {
        id: val
      }).then((res) => {
        if (res.success) {
          this.columns = res.result.groupColumnList
        }
      })
    }
  }
}
</script>

<style scoped></style>
