<template>
  <div
    :id="uuid"
    :style="{
      width: width + 'px',
      height: height + 'px',
      border: '1px solid black',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }"
  >
    自定义组件
  </div>
</template>
<script>
export default {
  name: 'CustomComponent',
  props: {
    width: Number,
    height: Number
  },
  data() {
    return {
      uuid: ''
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
  }
}
</script>
