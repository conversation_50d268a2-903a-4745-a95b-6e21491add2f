<template>
  <div
    v-if="option.attribute.barType === 0"
    class="outterBar"
    :style="{
      width: width + 'px',
      height: height + 'px',
      backgroundColor: option.attribute.outterColor,
      borderRadius: option.attribute.borderRadius + 'px'
    }"
  >
    <div
      class="innerBar"
      :style="{
        height: height + 'px',
        width: Math.min(1, Math.abs(computedProgress?.value)) * width + 'px',
        backgroundColor: option.attribute.innerColor,
        borderRadius: option.attribute.borderRadius + 'px'
      }"
    ></div>
  </div>
  <div v-else>
    <div
      ref="barChart"
      :style="{
        width: width + 'px',
        height: height + 'px'
      }"
    ></div>
  </div>
</template>
<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { postAction } from '@/api/manage'
export default {
  name: 'ProgressBar',
  props: {
    id: String,
    option: Object,
    pIndexSetData: Array,
    pIndexSetId: String,
    width: Number,
    height: Number
  },
  data() {
    return {
      computedProgress: { value: 0 },
      chartInstance: null,
      directQueryOption: [] // 直接由本组件绑定的查询控件，不经过父容器传递
    }
  },
  watch: {
    'option.attribute.innerColumn'() {
      this.loadData()
    },
    'option.attribute.outterColumn'() {
      this.loadData()
    },
    'option.attribute.barType'() {
      this.loadData()
    },
    pIndexSetData: {
      handler: function (data) {
        this.refreshCptData()
      },
      deep: true
    },
    directQueryOption: {
      handler() {
        this.refreshCptData()
      },
      deep: true
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCpt'])
  },
  created() {
    this.refreshCptData()
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const foundIndex = this.directQueryOption.findIndex(
          (item) => item.column === columnInfo.columns[index].name
        )
        if (foundIndex < 0)
          this.directQueryOption.push({
            value: columnInfo.option,
            column: columnInfo.columns[index].name
          })
        else this.directQueryOption[foundIndex].value = columnInfo.option
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off('onTabClick')
  },
  methods: {
    refreshCptData() {
      this.loadData()
    },
    async loadData() {
      if (this.option.attribute.inheritPIndexSet) this.option.attribute.indexSet = this.pIndexSetId
      if (this.option.attribute.indexSet && this.option.attribute.innerColumn) {
        let data = []
        const queryOption = this.directQueryOption
        if (this.pIndexSetId === this.option.attribute.indexSet) {
          // 绑定父容器指标集，无需重新获取数据
          if (!this.pIndexSetData) return
          data = this.pIndexSetData
        } else {
          const where = queryOption.length
            ? queryOption.map((item) => {
                return {
                  column: item.column,
                  value: item.value,
                  operator: 'eq'
                }
              })
            : []
          const dataRes = await postAction(
            'index/scCockpitIndexGroupConfig/previewData' + '?id=' + this.option.attribute.indexSet,
            {
              pageNo: 1,
              pageSize: 1000,
              connType: 'And',
              where
            }
          )
          if (dataRes.success && dataRes.result.records) data = dataRes.result.records
          else return
        }
        getBarValue(data, this)
      } else if (!this.option.attribute.innerColumn && this.option.attribute.defaultVal >= 0) {
        this.computedProgress = { value: this.option.attribute.defaultVal / 100 }
        this.option.attribute.barType === 1 && this.loadRingChart()
      } else {
        this.computedProgress = { value: 0 }
        this.option.attribute.barType === 1 && this.loadRingChart()
      }
      function getBarValue(data, _this) {
        let outterTargetValue = 100
        if (Array.isArray(data)) {
          if (_this.option.attribute.dataIndex === undefined) {
            const innerTargetValue = data[0][_this.option.attribute.innerColumn]
            if (_this.option.attribute.outterColumn)
              outterTargetValue = data[0][_this.option.attribute.outterColumn]
            if (innerTargetValue !== undefined && outterTargetValue !== undefined) {
              _this.computedProgress = { value: innerTargetValue / outterTargetValue }
            }
          } else {
            if (_this.option.attribute.outterColumn)
              outterTargetValue =
                data[_this.option.attribute.dataIndex][_this.option.attribute.outterColumn]
            _this.computedProgress = {
              value:
                data[_this.option.attribute.dataIndex][_this.option.attribute.innerColumn] /
                outterTargetValue
            }
          }
        } else {
          if (_this.option.attribute.outterColumn)
            outterTargetValue = data[_this.option.attribute.outterColumn]
          _this.computedProgress = {
            value: data[_this.option.attribute.innerColumn] / outterTargetValue
          }
        }
        if (_this.option.attribute.barType === 1) {
          _this.loadRingChart()
        }
      }
    },
    loadRingChart() {
      this.$refs.barChart && this.$echarts.dispose(this.$refs.barChart)
      this.chartInstance = null
      const chartOption = {
        polar: {
          radius: ['80%', '100%'],
          center: ['50%', '50%']
        },
        angleAxis: {
          max: 100,
          show: false
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '',
            type: 'bar',
            roundCap: true,
            barWidth: 60,
            showBackground: true,
            backgroundStyle: {
              color: this.option.attribute.outterColor
            },
            data: [this.computedProgress?.value * 100],
            coordinateSystem: 'polar',
            itemStyle: {
              color: this.option.attribute.innerColor
            }
          }
        ]
      }
      this.$nextTick(() => {
        this.chartInstance = this.$echarts.init(this.$refs.barChart)
        this.chartInstance.setOption(chartOption)
      })
    }
  }
}
</script>
