<template>
  <div
    class="iframe-container"
    :style="{ width: $attrs.width + 'px', height: $attrs.height + 'px' }"
  >
    <iframe
      loading="lazy"
      width="100%"
      height="100%"
      :src="option.attribute.url"
      style="pointer-events: none"
      @dragstart.prevent
    ></iframe>
  </div>
</template>

<script>
export default {
  name: 'CptIframe',
  props: {
    option: Object
  }
}
</script>

<style scoped>
.iframe-container {
  position: relative;
  isolation: isolate;
  padding: 10px;
}
</style>
