<template>
  <div class="map-wrapper">
    <div v-if="option.attribute.url">
      <component :is="option.attribute.url" :width="width" :height="height"></component>
    </div>
    <div v-else class="map-container" style="width: 100%; height: 100%">
      <sdMap :width="width" :height="height" />
    </div>
  </div>
</template>
<script>
import sdMap from './sd-map.vue'
export default {
  name: 'CptMap',
  components: { sdMap },
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({ url: '' })
    }
  },
  watch: {
    'option.attribute.url'(url) {
      if (url) {
        this.$options.components[url] = () => import(`@/components/custom/${url}.vue`)
      }
    }
  },
  created() {
    if (this.option.attribute.url) {
      this.$options.components[this.option.attribute.url] = () =>
        import(`@/components/custom/${this.option.attribute.url}.vue`)
      return
    }
  }
}
</script>
