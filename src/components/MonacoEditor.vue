<template>
  <div ref="container" :style="containerStyle" class="monaco-editor-container"></div>
</template>

<script>
import * as monaco from 'monaco-editor'

export default {
  name: 'MonacoEditor',
  props: {
    // 编辑器内容
    value: {
      type: String,
      default: ''
    },
    // 编程语言
    language: {
      type: String,
      default: 'javascript'
    },
    // 编辑器宽度
    width: {
      type: String,
      default: '100%'
    },
    // 编辑器高度
    height: {
      type: String,
      default: '400px'
    },
    // 编辑器主题
    theme: {
      type: String,
      default: 'vs'
    },
    // 编辑器选项
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      editor: null,
      internalValue: '',
      isInitialized: false
    }
  },
  computed: {
    containerStyle() {
      return {
        width: this.width,
        height: this.height
      }
    },
    // 合并默认选项和用户选项
    editorOptions() {
      const defaultOptions = {
        automaticLayout: true,
        scrollBeyondLastLine: false,
        minimap: { enabled: false },
        fontSize: 14,
        tabSize: 2,
        insertSpaces: true,
        wordWrap: 'on',
        lineNumbers: 'on',
        glyphMargin: false,
        folding: true,
        lineDecorationsWidth: 10,
        lineNumbersMinChars: 3
      }

      return {
        ...defaultOptions,
        ...this.options,
        language: this.language,
        theme: this.theme,
        value: this.value
      }
    }
  },
  watch: {
    value(newValue) {
      if (this.editor && newValue !== this.internalValue) {
        this.internalValue = newValue
        this.editor.setValue(newValue || '')
      }
    },
    language(newLanguage) {
      if (this.editor) {
        monaco.editor.setModelLanguage(this.editor.getModel(), newLanguage)
      }
    },
    theme(newTheme) {
      if (this.editor) {
        monaco.editor.setTheme(newTheme)
      }
    },
    options: {
      handler(newOptions) {
        if (this.editor) {
          this.editor.updateOptions(newOptions)
        }
      },
      deep: true
    }
  },
  mounted() {
    this.initEditor()
  },
  beforeDestroy() {
    this.destroyEditor()
  },
  methods: {
    // 初始化编辑器
    initEditor() {
      if (this.isInitialized) return

      try {
        this.editor = monaco.editor.create(this.$refs.container, this.editorOptions)
        this.internalValue = this.value || ''
        this.isInitialized = true

        // 监听内容变化
        this.editor.onDidChangeModelContent(() => {
          const newValue = this.editor.getValue()
          this.internalValue = newValue
          this.$emit('input', newValue)
        })

        // 监听编辑器焦点事件
        this.editor.onDidFocusEditorWidget(() => {
          this.$emit('focus')
        })

        this.editor.onDidBlurEditorWidget(() => {
          this.$emit('blur')
        })

        // 触发初始化完成事件
        this.$emit('init', this.editor)
        this.$emit('ready', this.editor)
      } catch (error) {
        console.error('Monaco Editor initialization failed:', error)
        this.$emit('error', error)
      }
    },

    // 销毁编辑器
    destroyEditor() {
      if (this.editor) {
        this.editor.dispose()
        this.editor = null
        this.isInitialized = false
      }
    },

    // 公共方法：调整编辑器大小
    resize() {
      if (this.editor) {
        this.editor.layout()
      }
    },

    // 公共方法：获取编辑器实例
    getEditor() {
      return this.editor
    },

    // 公共方法：设置焦点
    focus() {
      if (this.editor) {
        this.editor.focus()
      }
    },

    // 公共方法：获取内容
    getValue() {
      return this.editor ? this.editor.getValue() : ''
    },

    // 公共方法：设置内容
    setValue(value) {
      if (this.editor) {
        this.editor.setValue(value || '')
      }
    }
  }
}
</script>

<style scoped>
.monaco-editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.monaco-editor-container:focus-within {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>
