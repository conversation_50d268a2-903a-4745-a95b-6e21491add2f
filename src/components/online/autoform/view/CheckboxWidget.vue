<template>
  <formitem-wapper :formitem="formitem">
    <j-checkbox
      v-decorator="[formitem.key,formitem.fieldDecoratorOptions]"
      :trigger-change="true"
      :options="formitem.listSource"
      v-bind="widgetAttrs">
    </j-checkbox>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'CheckboxWidget',
    components:{
      FormitemWapper
    },
    mixins:[FormItemMixin],
    methods:{
      getAttrs(){
        let curWidgetAttr = this.widgetAttrs;
        return {
          ...curWidgetAttr
        }
      }
    }
  }
</script>

<style scoped>

</style>