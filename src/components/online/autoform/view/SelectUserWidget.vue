<template>
  <formitem-wapper :formitem="formitem">
    <j-select-user-by-dep
      v-decorator="[formitem.key,formitem.fieldDecoratorOptions]"
      v-bind="widgetAttrs"
      :trigger-change="true"
      :placeholder="placeholder">
    </j-select-user-by-dep>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'SelectUserWidget',
    components:{
      FormitemWapper,
    },
    mixins:[FormItemMixin]
  }
</script>
