<template>
  <formitem-wapper :formitem="formitem">
    <j-multi-select-tag
      v-decorator="[formitem.key,formitem.fieldDecoratorOptions]"
      v-bind="widgetAttrs"
      :trigger-change="true"
      :placeholder="placeholder"
      :popContainer="popContainerString"
      :options="formitem.listSource">
    </j-multi-select-tag>
  </formitem-wapper>
</template>

<script>
  import { FormItemMixin } from './../FormItenMixin'
  import FormitemWapper from './../FormitemWapper.vue'

  export default {
    name: 'SelectMultiWidget',
    components:{
      FormitemWapper,
    },
    mixins:[FormItemMixin]
  }
</script>

<style scoped>

</style>