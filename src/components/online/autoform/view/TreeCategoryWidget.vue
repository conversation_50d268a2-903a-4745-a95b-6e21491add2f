<template>
  <formitem-wapper :formitem="formitem">
    <j-tree-select
      v-decorator="[formitem.key,formitem.fieldDecoratorOptions]"
      :pidValue="formitem.pidValue"
      :placeholder="placeholder"
      v-bind="widgetAttrs"
      dict="SYS_CATEGORY,NAME,ID"
      pidField="PID"
      hasChildField="HAS_CHILD"
      load-triggle-change
      @change="handCatTreeChange">
    </j-tree-select>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'TreeCategoryWidget',
    components:{
      FormitemWapper,
    },
    mixins:[FormItemMixin],
    methods:{
      handCatTreeChange(value,label){
        let res = {}

        res[this.formitem.textField] = label
        //console.log("eee",res)
        this.$bus.$emit('popupCallbackMinitor', res);
      }

    }
  }
</script>
