<template>
  <formitem-wapper :formitem="formitem">
    <j-date
      v-decorator="[formitem.key,formitem.fieldDecoratorOptions]"
      :placeholder="placeholder"
      v-bind="getAttrs()"
      :trigger-change="true"
      :show-time="true"
      date-format="YYYY-MM-DD HH:mm:ss"
      :getCalendarContainer="getModalAsContainer">
    </j-date>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'DatetimeWidget',
    components:{
      FormitemWapper
    },
    mixins:[FormItemMixin],
    methods:{
      getAttrs(){
        let curWidgetAttr = this.widgetAttrs;
        if(!curWidgetAttr){
          return {
            style:{
              width:"100%"
            }
          }
        }else{
          return {
            style:{
              width:"100%"
            },
            readOnly:(!curWidgetAttr.disabled)?false:true,
            ...curWidgetAttr
          }
        }

      }
    }
  }
</script>

<style scoped>

</style>