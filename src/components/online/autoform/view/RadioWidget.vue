<template>
  <formitem-wapper :formitem="formitem">
    <a-radio-group v-decorator="[formitem.key,formitem.fieldDecoratorOptions]" v-bind="widgetAttrs">
      <a-radio
        v-for="(item,index) in formitem.listSource"
        v-if="!formitem.ui.button"
        :key="index"
        :value="item.value">
        {{ item.label }}
      </a-radio>
      <a-radio-button
        v-for="(item,index) in formitem.listSource"
        v-if="formitem.ui.button"
        :key="index"
        :value="item.value">
        {{ item.label }}
      </a-radio-button>
    </a-radio-group>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'RadioWidget',
    components:{
      FormitemWapper
    },
    mixins:[FormItemMixin],

  }
</script>

<style scoped>

</style>