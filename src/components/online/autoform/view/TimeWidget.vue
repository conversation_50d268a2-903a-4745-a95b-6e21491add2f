<template>
  <formitem-wapper :formitem="formitem">
    <j-time
      v-decorator="[formitem.key,formitem.fieldDecoratorOptions]"
      :placeholder="placeholder"
      v-bind="getAttrs()"
      :getCalendarContainer="getModalAsContainer">
    </j-time>
  </formitem-wapper>
</template>

<script>
  import { FormItemMixin } from './../FormItenMixin'
  import FormitemWapper from './../FormitemWapper.vue'

  export default {
    name: 'TimeWidget',
    components:{
      FormitemWapper,
    },
    mixins:[FormItemMixin],
    methods:{
      getAttrs(){
        let curWidgetAttr = this.widgetAttrs;
        if(!curWidgetAttr){
          return {
            style:{
              width:"100%"
            }
          }
        }else{
          return {
            style:{
              width:"100%"
            },
            readOnly:(!curWidgetAttr.disabled)?false:true,
            ...curWidgetAttr
          }
        }

      }
    }
  }
</script>

<style scoped>

</style>