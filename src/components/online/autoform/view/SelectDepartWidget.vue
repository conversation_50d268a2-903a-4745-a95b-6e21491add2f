<template>
  <formitem-wapper :formitem="formitem">
    <j-select-depart
      v-decorator="[formitem.key,formitem.fieldDecoratorOptions]"
      :multi="true"
      v-bind="widgetAttrs"
      :trigger-change="true"
      :placeholder="placeholder">
    </j-select-depart>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'SelectDepartWidget',
    components:{
      FormitemWapper,
    },
    mixins:[FormItemMixin]
  }
</script>
