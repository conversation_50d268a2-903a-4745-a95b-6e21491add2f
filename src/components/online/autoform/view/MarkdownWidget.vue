<template>
  <formitem-wapper :formitem="formitem">
    <j-markdown-editor :id="formitem.key" v-decorator="[formitem.key,{initialValue:''}]"></j-markdown-editor>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'MarkdownWidget',
    components:{
      FormitemWapper,
    },
    mixins:[FormItemMixin]
  }
</script>
