<template>
  <formitem-wapper :formitem="formitem">
    <a-textarea
      v-decorator="[formitem.key,formitem.fieldDecoratorOptions]"
      :placeholder="placeholder"
      v-bind="getAttrs()"/>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'TextareaWidget',
    components:{
      FormitemWapper
    },
    mixins:[FormItemMixin],
    methods:{
      getAttrs(){
        let curWidgetAttr = this.widgetAttrs;
        return {
          rows:4,
          ...curWidgetAttr
        }
      }
    }


  }
</script>

<style scoped>

</style>