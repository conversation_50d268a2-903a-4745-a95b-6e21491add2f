<template>
  <formitem-wapper :formitem="formitem">
    <a-rate v-decorator="[formitem.key,formitem.fieldDecoratorOptions]" v-bind="widgetAttrs"/>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'RateWidget',
    components:{
      FormitemWapper
    },
    mixins:[FormItemMixin],

  }
</script>

<style scoped>

</style>