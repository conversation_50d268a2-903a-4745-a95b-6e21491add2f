<template>
  <formitem-wapper :formitem="formitem">
    <a-input
      v-decorator="[formitem.key,formitem.fieldDecoratorOptions]"
      :placeholder="placeholder"
      v-bind="widgetAttrs"/>
  </formitem-wapper>
</template>

<script>
  import FormitemWapper from './../FormitemWapper.vue'
  import {FormItemMixin} from './../FormItenMixin'

  export default {
    name: 'InputWidget',
    components:{
      FormitemWapper
    },
    mixins:[FormItemMixin]

  }
</script>

<style scoped>

</style>