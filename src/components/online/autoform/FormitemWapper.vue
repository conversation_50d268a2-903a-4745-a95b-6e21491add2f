<template>
  <a-col v-show="showItem" v-if="loadItem" v-bind="colAttrs">
    <a-form-item v-bind="itemAttrs" :label="label">
      <slot></slot>
    </a-form-item>
  </a-col>
</template>
<script>

import {FormItemMixin} from './FormItenMixin'
export default {
    name: 'FormitemWapper',
    mixins:[FormItemMixin],
    inject: ['sh'],
    data(){
      return {

      }
    },
    computed: {
      showItem:{
        get(){
          if(this.formitem.type=='hidden'){
            return false
          }else{
            if(!this.formitem.subKey){
              return this.sh[this.formitem.key]
            }else{
              return this.sh[this.formitem.subKey][this.formitem.key]
            }
          }
        }
      },
      loadItem:{
        get(){
          if(!this.formitem.subKey){
            return this.sh[this.formitem.key+"_load"]
          }else{
            return this.sh[this.formitem.subKey][this.formitem.key+"_load"]
          }

        }
      }
    }
}
</script>

<style scoped>

</style>