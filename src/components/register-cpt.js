import Vue from 'vue'
//import cpt_input from '@/components/element/cpt-input'
import cpt_text from '@/components/element/cpt-text'
import cpt_carousel from '@/components/element/cpt-carousel'
import cpt_button from '@/components/element/cpt-button'
import cpt_image from '@/components/element/cpt-image'
import cpt_chart_column from '@/components/echarts/cpt-chart-column'
import cpt_chart_pie from '@/components/echarts/cpt-chart-pie'
import cpt_chart_clock from '@/components/echarts/cpt-chart-clock'
import cpt_dataV_border from '@/components/dataV/cpt-dataV-border'
import cpt_dataV_scrollTable from '@/components/dataV/cpt-dataV-scrollTable'
import cpt_dataV_scrollList from '@/components/dataV/cpt-dataV-scrollList'
import cpt_chart_map_gc from '@/components/echarts/cpt-chart-map-gc'
import cpt_chart_map_migrate from '@/components/echarts/cpt-chart-map-migrate'
import cpt_dataV_waterLevel from '@/components/dataV/cpt-dataV-waterLevel'
import cpt_dataV_decoration from '@/components/dataV/cpt-dataV-decoration'
import cpt_chart_line from '@/components/echarts/cpt-chart-line'
import cpt_dataV_digitalFlop from '@/components/dataV/cpt-dataV-digitalFlop'
import cpt_dataV_percentPond from '@/components/dataV/cpt-dataV-percentPond'
import cpt_iframe from '@/components/element/cpt-iframe'
import cpt_chart_td_column from '@/components/echarts/cpt-chart-td-column'
import cpt_dataV_activeRing from '@/components/dataV/cpt-dataV-activeRing'
import cpt_chart_gauge from '@/components/echarts/cpt-chart-gauge'
import cpt_num from '@/components/element/cpt-num'
import cpt_rect_num from '@/components/element/cpt-rect-num'
import cpt_map from '@/components/element/cpt-map.vue'
import cpt_menu from '@/components/element/cpt-menu.vue'
import cpt_popUp from '@/components/element/cpt-popUp.vue'
import cpt_scroll_table from '@/components/element/cpt-scroll-table'
import cpt_container_empty from '@/components/container/cpt-container-empty'
import cpt_container_flex from '@/components/container/cpt-container-flex'
import cpt_container_grid from '@/components/container/cpt-container-grid'
import cpt_container_draggable from '@/components/container/cpt-container-draggable'
import cpt_containers_draggable_tab from '@/components/container/cpt-containers-draggable-tab'
import text_container_group from '@/components/element/text-container-group.vue'
import customTextGroup from '@/components/element/customTextGroup.vue'
import basicLineChart from '@/components/echarts/basicLineChart'
import areaChart from '@/components/echarts/areaChart'
import stackedLineChart from '@/components/echarts/stackedLineChart'
import stackedAreaChart from '@/components/echarts/stackedAreaChart'
import basicHistogramChart from '@/components/echarts/basicHistogramChart'
import stackedHistogramChart from '@/components/echarts/stackedHistogramChart'
import groupedHistogramChart from '@/components/echarts/groupedHistogramChart'
import basicBarChart from '@/components/echarts/basicBarChart'
import stackedBarChart from '@/components/echarts/stackedBarChart'
import groupedBarChart from '@/components/echarts/groupedBarChart'
import groupedBarLineChart from '@/components/echarts/groupedBarLineChart'
import stackedBarLineChart from '@/components/echarts/stackedBarLineChart'
import pieChart from '@/components/echarts/pieChart'
import ringChart from '@/components/echarts/ringChart'
import radarChart from '@/components/echarts/radarChart'
import roseChart from '@/components/echarts/roseChart'
import BarLineChart from '@/components/echarts/BarLineChart'
import customComponent from '@/components/element/customComponent'
import tableComp from '@/components/element/tableComp.vue'
import tabComp from '@/components/element/tabComp.vue'
import selectComp from '@/components/element/selectComp.vue'
import progressBar from '@/components/element/progressBar.vue'
import cptTitle from './element/cpt-title.vue'
let cptList = [
  cpt_dataV_border,
  cpt_text,
  cpt_carousel,
  cpt_button,
  cpt_image,
  cpt_chart_column,
  cpt_chart_td_column,
  cpt_chart_pie,
  cpt_chart_clock,
  cpt_dataV_scrollTable,
  cpt_scroll_table,
  cpt_dataV_scrollList,
  cpt_chart_map_gc,
  cpt_chart_map_migrate,
  cpt_dataV_waterLevel,
  cpt_dataV_decoration,
  cpt_chart_line,
  cpt_dataV_digitalFlop,
  cpt_dataV_percentPond,
  cpt_iframe,
  cpt_dataV_activeRing,
  cpt_chart_gauge,
  cpt_num,
  cpt_rect_num,
  cpt_map,
  cpt_menu,
  cpt_popUp,
  cpt_container_empty,
  cpt_container_flex,
  text_container_group,
  customTextGroup,
  basicLineChart,
  areaChart,
  stackedLineChart,
  stackedAreaChart,
  basicHistogramChart,
  stackedHistogramChart,
  groupedHistogramChart,
  basicBarChart,
  stackedBarChart,
  groupedBarChart,
  groupedBarLineChart,
  stackedBarLineChart,
  pieChart,
  ringChart,
  radarChart,
  roseChart,
  BarLineChart,
  cpt_container_grid,
  cpt_container_draggable,
  cpt_containers_draggable_tab,
  customComponent,
  tableComp,
  tabComp,
  selectComp,
  progressBar,
  cptTitle
]

cptList.forEach((ele) => {
  Vue.component(ele.name, ele)
})
