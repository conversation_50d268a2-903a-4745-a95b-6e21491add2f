import Vue from 'vue'

import cpt_button_option from '@/components/element/options/cpt-button-option'
import cpt_text_option from '@/components/element/options/cpt-text-option'

//import cpt_input_option from '@/components/element/options/cpt-input-option'
import cpt_image_option from '@/components/element/options/cpt-image-option'
import cpt_carousel_option from '@/components/element/options/cpt-carousel-option'
import cpt_chart_column_option from '@/components/echarts/options/cpt-chart-column-option'
import cpt_chart_pie_option from '@/components/echarts/options/cpt-chart-pie-option'
import cpt_chart_clock_option from '@/components/echarts/options/cpt-chart-clock-option'
import cpt_dataV_border_option from '@/components/dataV/options/cpt-dataV-border-option'
import cpt_dataV_scrollTable_option from '@/components/dataV/options/cpt-dataV-scrollTable-option'
import cpt_dataV_scrollList_option from '@/components/dataV/options/cpt-dataV-scrollList-option'
import cpt_chart_mapGc_option from '@/components/echarts/options/cpt-chart-map-gc-option'
import cpt_chart_mapMigrate_option from '@/components/echarts/options/cpt-chart-map-migrate-option'
import cpt_dataV_waterLevel_option from '@/components/dataV/options/cpt-dataV-waterLevel-option'
import cpt_dataV_decoration_option from '@/components/dataV/options/cpt-dataV-decoration-option'
import cpt_chart_line_option from '@/components/echarts/options/cpt-chart-line-option'
import cpt_dataV_digitalFlop_option from '@/components/dataV/options/cpt-dataV-digitalFlop-option'
import cpt_dataV_percentPond_option from '@/components/dataV/options/cpt-dataV-percentPond-option'
import cpt_iframe_option from '@/components/element/options/cpt-iframe-option'
import cpt_chart_tdColumn_option from '@/components/echarts/options/cpt-chart-td-column-option'
import cpt_dataV_activeRing_option from '@/components/dataV/options/cpt-dataV-activeRing-option'
import cpt_chart_gauge_option from '@/components/echarts/options/cpt-chart-gauge-option'
import cpt_num_option from '@/components/element/options/cpt-num-option'
import cpt_rect_num_option from '@/components/element/options/cpt-rect-num-option'
import cpt_scroll_table_option from '@/components/element/options/cpt-scroll-table-option'
import cpt_map_option from '@/components/element/options/cpt-map-option'
import cpt_menu_option from '@/components/element/options/cpt-menu-option'
import cpt_popUp_option from '@/components/element/options/cpt-popUp-option'
import text_container_group_option from '@/components/element/options/text-container-group-option.vue'
import customTextGroupOption from './element/options/customTextGroupOption.vue'
import cpt_container_flex_option from '@/components/container/options/cpt-container-flex-option.vue'
import cpt_container_grid_option from '@/components/container/options/cpt-container-grid-option.vue'
import cpt_container_empty_option from '@/components/container/options/cpt-container-empty-option.vue'
import cpt_container_draggable_option from '@/components/container/options/cpt-container-draggable-option.vue'
import cpt_containers_draggable_tab_option from '@/components/container/options/cpt-containers-draggable-tab-option.vue'
import cpt_common_option from '@/components/cpt-common-option.vue'
import basicLineChart_option from '@/components/echarts/options/basicLineChart-option.vue'
import areaChart_option from '@/components/echarts/options/areaChart-option.vue'
import BarLineChart_option from '@/components/echarts/options/BarLineChart-option'
import basicBarChart_Option from '@/components/echarts/options/basicBarChart-option'
import basicHistogramChart_option from '@/components/echarts/options/basicHistogramChart-option'
import groupedBarChart_option from '@/components/echarts/options/groupedBarChart-option'
import groupedBarLineChart_option from '@/components/echarts/options/groupedBarLineChart-option.vue'
import groupedHistogramChart_option from '@/components/echarts/options/groupedHistogramChart-option'
import stackedBarLineChart_option from '@/components/echarts/options/stackedBarLineChart-option.vue'
import pieChart_option from '@/components/echarts/options/pieChart-option'
import radarChart_option from '@/components/echarts/options/radarChart-option'
import ringChart_option from '@/components/echarts/options/ringChart-option'
import roseChart_option from '@/components/echarts/options/roseChart-option'
import stackedAreaChart_option from '@/components/echarts/options/stackedAreaChart-option'
import stackedBarChart_option from '@/components/echarts/options/stackedBarChart-option'
import stackedHistogramChart_option from '@/components/echarts/options/stackedHistogramChart-option'
import stackedLineChart_option from '@/components/echarts/options/stackedLineChart-option'
import customComponentOption from './element/options/customComponentOption.vue'
import tableCompOption from './element/options/tableCompOption.vue'
import tabCompOption from './element/options/tabCompOption.vue'
import selectCompOption from './element/options/selectCompOption.vue'
import progressBarOption from './element/options/progressBarOption.vue'
import cptTitleOption from './element/options/cpt-title-option.vue'
let cptOptionsList = [
  cpt_button_option,
  cpt_text_option,
  cpt_image_option,
  cpt_carousel_option,
  cpt_chart_column_option,
  cpt_chart_pie_option,
  cpt_chart_clock_option,
  cpt_dataV_border_option,
  cpt_dataV_scrollTable_option,
  cpt_dataV_scrollList_option,
  cpt_chart_mapGc_option,
  cpt_chart_mapMigrate_option,
  cpt_dataV_waterLevel_option,
  cpt_dataV_decoration_option,
  cpt_chart_line_option,
  cpt_dataV_digitalFlop_option,
  cpt_dataV_percentPond_option,
  cpt_iframe_option,
  cpt_chart_tdColumn_option,
  cpt_dataV_activeRing_option,
  cpt_chart_gauge_option,
  cpt_num_option,
  cpt_rect_num_option,
  cpt_scroll_table_option,
  cpt_map_option,
  cpt_menu_option,
  cpt_popUp_option,
  text_container_group_option,
  cpt_container_flex_option,
  cpt_container_grid_option,
  cpt_container_empty_option,
  cpt_container_draggable_option,
  cpt_containers_draggable_tab_option,
  cpt_common_option,
  basicLineChart_option,
  areaChart_option,
  BarLineChart_option,
  basicBarChart_Option,
  basicHistogramChart_option,
  groupedBarChart_option,
  groupedHistogramChart_option,
  groupedBarLineChart_option,
  pieChart_option,
  radarChart_option,
  ringChart_option,
  roseChart_option,
  stackedAreaChart_option,
  stackedBarChart_option,
  stackedHistogramChart_option,
  stackedLineChart_option,
  stackedBarLineChart_option,
  customTextGroupOption,
  customComponentOption,
  tableCompOption,
  tabCompOption,
  selectCompOption,
  progressBarOption,
  cptTitleOption
]

cptOptionsList.forEach((ele) => {
  Vue.component(ele.name, ele)
})
