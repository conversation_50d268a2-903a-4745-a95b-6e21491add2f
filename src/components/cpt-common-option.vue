<template>
  <el-form :model="attribute" :rules="rules" label-width="80px" size="mini">
    <el-form-item label="名称" prop="name">
      <el-input v-model="attribute.name" />
    </el-form-item>
    <el-form-item label="类名" prop="className">
      <el-input v-model="attribute.className" />
    </el-form-item>
    <el-form-item label="授权角色" prop="authorizeRole">
      <el-select v-model="attribute.authorizeRole" multiple collapse-tags>
        <el-option v-for="item in allRole" :value="item.id" :label="item.roleName"></el-option>
      </el-select>
      <el-button icon="el-icon-setting" @click="showRoleDialog"></el-button>
      <el-button
        style="color: #fff; width: 40%"
        @click="BatchAuthorize(allRole.map((item) => item.id))"
        >全部授权</el-button
      >
      <el-button
        style="color: #fff; width: 40%"
        @click="BatchUnAuthorize(allRole.map((item) => item.id))"
        >全部取消授权</el-button
      >
    </el-form-item>
    <roleDialog
      ref="roleDialog"
      :authorizeList="attribute.authorizeRole"
      @changeAuthorizeStatus="changeAuthorizeStatus"
      @BatchAuthorize="BatchAuthorize"
      @BatchUnAuthorize="BatchUnAuthorize"
    />
  </el-form>
</template>

<script>
import roleDialog from './dialog/roleDialog.vue'
import { mapState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
export default {
  name: 'CptCommonOption',
  components: { roleDialog },
  props: { attribute: Object },
  data() {
    let validatorClassName = (rule, value, callback) => {
      if (/[\u4e00-\u9fa5]/.test(value)) {
        callback(new Error('名称不能包含中文'))
      }
      callback()
    }
    return {
      rules: {
        className: [{ validator: validatorClassName, trigger: 'change' }]
      }
    }
  },
  computed: {
    ...mapState(useDesignerStore, ['allRole'])
  },
  methods: {
    showRoleDialog() {
      this.$refs.roleDialog.show()
    },
    changeAuthorizeStatus(id) {
      if (!this.attribute.authorizeRole) this.attribute.authorizeRole = [] // 防止权限功能开发前配置的组件报错
      const rolesSet = new Set(this.attribute.authorizeRole)
      rolesSet.has(id) ? rolesSet.delete(id) : rolesSet.add(id)
      this.$set(this.attribute, 'authorizeRole', Array.from(rolesSet))
    },
    BatchAuthorize(ids) {
      const authorizeRole = this.attribute.authorizeRole || []
      this.$nextTick(() => {
        this.$set(this.attribute, 'authorizeRole', Array.from(new Set([...authorizeRole, ...ids])))
      })

      this.$message.success('批量授权成功')
    },
    BatchUnAuthorize(ids) {
      const authorizeRole = this.attribute.authorizeRole || []
      this.$set(
        this.attribute,
        'authorizeRole',
        authorizeRole.filter((item) => {
          return !ids.includes(item)
        })
      )

      this.$message.success('批量取消授权成功')
    },
    selectAllRole() {}
  }
}
</script>

<style scoped>
.el-button {
  background: #409eff;
  margin-left: revert;
}
::v-deep .el-icon-setting {
  color: #fff;
}
</style>
