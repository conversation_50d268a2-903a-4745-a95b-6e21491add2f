<template>
  <div>
    <cptEchartCommonOption :attribute="attribute" />
    <el-form label-width="80px" size="mini">
      <el-form-item label="柱指标数">
        <el-input-number v-model="attribute.barColumns" :min="1" />
      </el-form-item>
      <el-form-item label="线指标数">
        <el-input-number v-model="attribute.lineColumns" :min="1" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import cptEchartCommonOption from './cpt-echart-common-option.vue'
export default {
  name: 'GroupedBarLineChartOption',
  components: { cptEchartCommonOption },
  props: { attribute: Object }
}
</script>

<style scoped></style>
