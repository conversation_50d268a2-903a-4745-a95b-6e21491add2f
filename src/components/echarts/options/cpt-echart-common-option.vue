<!-- echarts图表通用属性表单 -->
<template>
  <el-form label-width="80px" size="mini">
    <a-collapse expandIconPosition="right">
      <a-collapse-panel key="数据" header="数据" forceRender>
        <el-form-item label="绑定指标集">
          <a-tree-select
            v-model="attribute.indexSet"
            show-search
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :replaceFields="replaceFields"
            :treeData="indexSetTree"
            :disabled="!!currentCpt.cptIndexId"
            placeholder="请选择绑定指标集"
            allow-clear
            tree-default-expand-all
            treeNodeFilterProp="title"
            @change="onIndexSetChange"
          ></a-tree-select>
        </el-form-item>
        <el-form-item label="渲染维度">
          <el-select v-model="attribute.dimenssionUsed" multiple>
            <el-option
              v-for="column in dimenssionListAll"
              :key="column.id"
              :value="column.sourceColumn"
              :label="column.sourceColumn"
              >{{ column.sourceColumn + '（' + column.indexName + '）' }}</el-option
            >
          </el-select>
        </el-form-item>
        <el-form-item label="渲染指标">
          <el-select v-model="attribute.indexUsed" multiple>
            <el-option
              v-for="column in indexListAll"
              :key="column.id"
              :value="column.sourceColumn"
              :label="column.sourceColumn"
              >{{ column.sourceColumn + '（' + column.indexName + '）' }}</el-option
            >
          </el-select>
        </el-form-item>
      </a-collapse-panel>
      <a-collapse-panel
        v-if="!specialChart.includes(currentCpt.cptKey)"
        key="边距"
        header="边距"
        forceRender
      >
        <!-- <el-form-item label="图表名称">
      <el-input v-model="attribute.chartTitle" />
    </el-form-item> -->
        <el-form-item label="上边距">
          <el-input v-model="attribute.gridTop" />
        </el-form-item>
        <el-form-item label="右边距">
          <el-input v-model="attribute.gridRight" />
        </el-form-item>
        <el-form-item label="下边距">
          <el-input v-model="attribute.gridBottom" />
        </el-form-item>
        <el-form-item label="左边距">
          <el-input v-model="attribute.gridLeft" />
        </el-form-item>
      </a-collapse-panel>
      <a-collapse-panel
        v-if="!specialChart.includes(currentCpt.cptKey)"
        key="坐标轴"
        header="坐标轴"
        forceRender
      >
        <el-form-item label="坐标轴标签颜色" label-width="110px">
          <el-color-picker v-model="attribute.axisLabelColor" show-alpha></el-color-picker>
        </el-form-item>
        <el-form-item label="坐标轴名称颜色" label-width="110px">
          <el-color-picker v-model="attribute.nameTextColor" show-alpha></el-color-picker>
        </el-form-item>
        <el-form-item label="坐标轴线颜色" label-width="110px">
          <el-color-picker v-model="attribute.axisLineColor" show-alpha></el-color-picker>
        </el-form-item>
      </a-collapse-panel>
      <a-collapse-panel
        v-if="!specialChart.includes(currentCpt.cptKey)"
        key="滚动条"
        header="滚动条"
        forceRender
      >
        <el-form-item label="是否滚动展示" label-width="110px">
          <a-radio-group v-model="attribute.scroll">
            <a-radio :value="1"> 是 </a-radio>
            <a-radio :value="0"> 否 </a-radio>
          </a-radio-group>
        </el-form-item>
        <el-form-item v-if="attribute.scroll" label="单次展示数量">
          <el-input-number v-model="attribute.scrollNum" :min="1" />
        </el-form-item>
        <div v-if="attribute.scroll">
          <el-form-item label="是否自动滚动">
            <a-radio-group v-model="attribute.autoScroll">
              <a-radio :value="1"> 是 </a-radio>
              <a-radio :value="0"> 否 </a-radio>
            </a-radio-group>
          </el-form-item>
          <el-form-item v-if="attribute.scroll" label="滚动条样式" style="position: relative">
            <el-button
              style="position: absolute; top: 60px; left: -75px"
              @click="convertCssToJs('scrollStyle', attribute.scrollStyle)"
              >css转js</el-button
            >
            <monaco-editor
              ref="jsCodeEditor"
              v-model="attribute.scrollStyle"
              language="javascript"
              width="100%"
              height="200px"
              :options="editorOptions"
            ></monaco-editor>
          </el-form-item>
        </div>
      </a-collapse-panel>
      <a-collapse-panel
        v-for="item in currentCpt.queryCompList?.filter((queryComp) => !!queryComp.isShow)"
        :key="item.id"
        :header="'查询组件-' + item.queryName"
        forceRender
      >
        <el-form-item label="top">
          <el-input-number v-model="attribute.queryTop" />
        </el-form-item>
        <el-form-item label="left">
          <el-input-number v-model="attribute.queryLeft" />
        </el-form-item>
      </a-collapse-panel>
    </a-collapse>
  </el-form>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { getAction } from '@/api/manage'
export default {
  name: 'BasicLineChartOption',
  props: { attribute: Object },
  data() {
    return {
      dimenssionListAll: [],
      indexListAll: [],
      editorOptions: {
        tabSize: 6,
        fontSize: 14,
        showPrintMargin: false, //去除编辑器里的竖线
        formGroupList: []
      },
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      }
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, [
      'currentCpt',
      'currentCptId',
      'componentData',
      'indexSetTree'
    ]),
    specialChart: () => {
      return ['pieChart', 'roseChart', 'ringChart', 'radarChart']
    }
  },
  created() {
    this.getIndexSetInfo()
    this.$nextTick(() => {
      if (this.$refs.jsCodeEditor) {
        this.$refs.jsCodeEditor.resize()
      }
    })
  },
  methods: {
    editorInit() {
      // language
      require('brace/mode/javascript')
      // theme
      require('brace/theme/chrome')
    },
    convertCssToJs(column, cssStr) {
      try {
        this.attribute[column] = this.$convertCssToJs(cssStr)
      } catch (e) {}
    },
    handleShowChange(val) {
      if (val === 1) {
        this.$refs.jsCodeEditor.editor.getSession().setMode('ace/mode/javascript')
        this.$refs.jsCodeEditor.editor.setTheme('ace/theme/chrome')
        if (!this.attribute.scrollStyle) this.attribute.scrollStyle = 'style = {\n\n}'
      }
    },
    onIndexSetChange() {
      this.attribute.dimenssionUsed = []
      this.attribute.indexUsed = []
      this.getIndexSetInfo(false)
    },
    getIndexSetInfo(fill = true) {
      if (this.currentCpt.cptIndexId) this.attribute.indexSet = this.currentCpt.cptIndexId
      else if (!this.attribute.indexSet && !this.currentCpt.cptIndexId)
        this.attribute.indexSet = this.currentCpt.pIndexSetId
      getAction('/index/scCockpitIndexGroupConfig/queryById', {
        id: this.attribute.indexSet
      }).then((res) => {
        if (res.success) {
          this.$nextTick(() => {
            this.dimenssionListAll = res.result.groupColumnList.filter(
              (column) => column.indexType === 'dimenssion'
            )
            this.indexListAll = res.result.groupColumnList.filter(
              (column) => column.indexType === 'index'
            )
            if (!fill) return
            if (!this.attribute.dimenssionUsed.length)
              this.attribute.dimenssionUsed = this.dimenssionListAll.map(
                (dimenssion) => dimenssion.sourceColumn
              )
            if (!this.attribute.indexUsed.length)
              this.attribute.indexUsed = this.indexListAll.map((index) => index.sourceColumn)
          })
        }
      })
    }
  }
}
</script>

<style scoped></style>
