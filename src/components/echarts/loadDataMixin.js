import { getAction, postAction } from '@/api/manage'
import { getDictItemsFromCache } from '@/api/api'
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import queryComp from './queryComp.vue'
export const loadDataMixin = {
  components: { queryComp },
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({})
    },
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uuid: '',
      chartOption: {},
      chart: undefined,
      querys: [], // 可能有多个查询控件绑定
      queryParam: {},
      dimensionData: [],
      indexData: [],
      scrollInterval: null
    }
  },
  watch: {
    // option: {
    // 需要即时渲染则打开注释
    //   handler(newObj) {
    //     this.loadData()
    //   },
    //   deep: true
    // },
    width() {
      this.chart.resize()
    },
    height() {
      this.chart.resize()
    },
    indexId(val) {
      val && this.loadData()
    },

    paramsForUnBindingComp: {
      handler(val) {
        val && this.loadData()
      },
      deep: true
    },
    combinedScrollVars: {
      handler(val) {
        if (!val || this.id !== this.currentCptId) return
        let scrollStyle = { show: false }
        try {
          if (val.scroll) {
            scrollStyle = new Function('return ' + val.scrollStyle)()
            scrollStyle.endValue = scrollStyle.startValue + val.scrollNum - 1
            scrollStyle.show = true
          }
        } catch (error) {}
        this.option = Object.assign(this.option, { dataZoom: scrollStyle })
        // this.dimensionData.length && this.loadChart()
      },
      deep: true
    },
    combinedLocationVars: {
      handler(val) {
        if (
          !val ||
          !Object.values(val).filter((value) => !!value).length ||
          this.id !== this.currentCptId
        )
          return
        if (!this.option.grid) this.option = Object.assign(this.option, { grid: val })
        else {
          this.option.grid.top = val.top || ''
          this.option.grid.bottom = val.bottom || ''
          this.option.grid.left = val.left || ''
          this.option.grid.right = val.right || ''
        }

        // this.dimensionData.length && this.loadChart()
      },
      deep: true
    },
    combinedAxisStyle: {
      handler(val) {
        if (!val || !this.option.xAxis || this.id !== this.currentCptId) return
        if (!this.option.xAxis.axisLabel)
          this.option.xAxis.axisLabel = { color: val.axisLabelColor }
        else this.option.xAxis.axisLabel.color = val.axisLabelColor
        if (!this.option.xAxis.nameTextStyle)
          this.option.xAxis.nameTextStyle = { color: val.nameTextColor }
        else this.option.xAxis.nameTextStyle.color = val.nameTextColor
        if (!this.option.xAxis.axisLine)
          this.option.xAxis.axisLine = { lineStyle: { color: val.axisLineColor } }
        else this.option.xAxis.axisLine.lineStyle.color = val.axisLineColor

        if (Array.isArray(this.option.yAxis)) {
          this.option.yAxis.map((item) => {
            if (!item.axisLabel) item.axisLabel = { color: val.axisLabelColor }
            else item.axisLabel.color = val.axisLabelColor
            if (!item.nameTextStyle) item.nameTextStyle = { color: val.nameTextColor }
            else item.nameTextStyle.color = val.nameTextColor
            if (!item.axisLine) item.axisLine = { lineStyle: { color: val.axisLineColor } }
            else
              item.axisLine = Object.assign(item.axisLine, {
                lineStyle: { color: val.axisLineColor }
              })
          })
        } else {
          if (!this.option.yAxis.axisLabel)
            this.option.yAxis.axisLabel = { color: val.axisLabelColor }
          else this.option.yAxis.axisLabel.color = val.axisLabelColor
          if (!this.option.yAxis.nameTextStyle)
            this.option.yAxis.nameTextStyle = { color: val.nameTextColor }
          else this.option.yAxis.nameTextStyle.color = val.nameTextColor
          if (!this.option.yAxis.axisLine)
            this.option.yAxis.axisLine = { lineStyle: { color: val.axisLineColor } }
          else this.option.yAxis.axisLine.lineStyle.color = val.axisLineColor
        }
        // this.dimensionData.length && this.loadChart()
      },
      deep: true
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
  },
  mounted() {
    this.chart = this.$echarts.init(this.$refs.chart)
    this.refreshCptData()
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const query = {
          value: columnInfo.option,
          column: columnInfo.columns[index].name
        }
        const foundIndex = this.querys.findIndex((item) => item.column === query.column)
        if (foundIndex < 0) this.querys.push(query)
        else this.querys[foundIndex].value = query.value
        this.loadData(this.querys)
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off('onTabClick')
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCpt', 'currentCptId', 'componentData']),
    combinedScrollVars() {
      // 返回一个依赖多个变量的值
      return {
        scroll: this.currentCpt.cptOption?.attribute.scroll,
        scrollStyle: this.currentCpt.cptOption?.attribute.scrollStyle,
        scrollNum: this.currentCpt.cptOption?.attribute.scrollNum
      }
    },
    combinedLocationVars() {
      return {
        top: this.currentCpt.cptOption?.attribute.gridTop,
        bottom: this.currentCpt.cptOption?.attribute.gridBottom,
        left: this.currentCpt.cptOption?.attribute.gridLeft,
        right: this.currentCpt.cptOption?.attribute.gridRight
      }
    },
    combinedAxisStyle() {
      return {
        axisLabelColor: this.currentCpt.cptOption?.attribute.axisLabelColor,
        nameTextColor: this.currentCpt.cptOption?.attribute.nameTextColor,
        axisLineColor: this.currentCpt.cptOption?.attribute.axisLineColor
      }
    }
  },
  methods: {
    refreshCptData() {
      this.loadData()
      // pollingRefresh(this.uuid, this.option.cptDataForm, this.loadData)
    },
    async loadData(querys = []) {
      let dataRes, dimensionList, indexList
      // 已绑定组件
      if (!this.indexId && !this.option.attribute.indexSet) return
      const indexSetInfoRes = await getAction('/index/scCockpitIndexGroupConfig/queryById', {
        id: this.indexId || this.option.attribute.indexSet
      })
      const indexSetInfo = indexSetInfoRes.result
      // 判断是否有dimenssionUsed和indexUsed字段，防止之前配置的echarts图无法渲染，等所有echarts图都配置这两字段后可删除三元运算
      if (!this.option.attribute.indexUsed) {
        indexList = indexSetInfo.groupColumnList.filter((column) => {
          column.indexType === 'index'
        })
      } else {
        indexList = this.option.attribute.indexUsed.map((index) => {
          return indexSetInfo.groupColumnList.filter((column) => index === column.sourceColumn)[0]
        })
      }
      if (!indexList.length) return
      if (!this.option.attribute.dimenssionUsed) {
        dimensionList = indexSetInfo.groupColumnList.filter((column) => {
          return (
            column.indexType === 'dimenssion' &&
            !indexSetInfoRes.result.queryColumnList.some(
              (item) => item.sourceColumn === column.sourceColumn
            )
          )
        })
      } else {
        dimensionList = this.option.attribute.dimenssionUsed.map((index) => {
          return indexSetInfo.groupColumnList.filter((column) => index === column.sourceColumn)[0]
        })
      }
      dataRes = await postAction(
        `/index/scCockpitIndexGroupConfig/previewData?id=${
          this.indexId || this.option.attribute.indexSet
        }`,
        {
          connType: 'And',
          pageNo: 1,
          pageSize: 1000,
          where: querys.length
            ? querys.map((query) => {
                return { column: query.column, value: query.value, operator: 'eq' }
              })
            : []
        }
      )

      let data = dataRes.result.records
      this.dimensionData = dimensionList.map((dimension) => {
        return {
          name: dimension.columnComment || dimension.indexName,
          data: data.map((element) => {
            return element[dimension.columnName || dimension.sourceColumn]
          })
        }
      })
      this.indexData = indexList.map((index, i) => {
        return {
          name: index.columnComment || index.indexName,
          data: data.map((element) => {
            return element[index.columnName || index.sourceColumn]
          }),
          unit: this.getUnit(index, data, i)
        }
      })
      this.loadChart()
    },
    getUnit(index, data, i) {
      if (index.unit) return index.unit // 已绑定指标集的组件，存在unit字段，可直接赋值
      if (this.paramsForUnBindingComp.unitList) {
        if (this.currentCpt.cptOption && this.currentCpt.cptOption.attribute.barColumns) {
          // 分组柱线图和堆叠柱线图需要根据柱个数设置多个单位
          return data[0][
            this.paramsForUnBindingComp.unitList[
              i < this.currentCpt.cptOption.attribute.barColumns ? 0 : 1
            ]
          ]
        } else {
          // 其余最多设置两个指标单位即可
          return data[0][this.paramsForUnBindingComp.unitList[i]]
        }
      }
      return ''
    },
    getWhere(queryColumnList) {
      return queryColumnList
        .map((item) => {
          if (item.queryType === 'input')
            return {
              column: item.sourceColumn,
              operator: item.queryOperator,
              value: item.dbDefaultVal
            }
          else if (item.queryType === 'select') {
            return {
              column: item.sourceColumn,
              operator: 'eq',
              value: item.dbDefaultVal
            }
          } else if (item.queryType === 'datepicker') {
            const dateFormatList = getDictItemsFromCache('date_format')
            const time = dateFormatList.filter((dateFormat) => {
              return dateFormat.value === item.dateFormatStr
            })[0]
            return [
              {
                column: item.sourceColumn,
                operator: 'gt',
                value: this.adjustDate(+item.startTime, time.value)
              },
              {
                column: item.sourceColumn,
                operator: 'lt',
                value: this.adjustDate(+item.endTime, time.value)
              }
            ]
          }
        })
        .flat()
    },
    /**
     * 日期计算
     * @param dateStr 原日期字符串
     * @param value 加减的数值
     * @param unit 加减的日期单位
     */
    adjustDate(value, unit) {
      // 解析原始日期
      const date = new Date() // 替换为兼容格式
      // 根据单位进行增减操作
      switch (unit.toLowerCase()) {
        case 'year':
          date.setFullYear(date.getFullYear() + value)
          break
        case 'month':
          date.setMonth(date.getMonth() + value)
          break
        case 'day':
          date.setDate(date.getDate() + value)
          break
        case 'hour':
          date.setHours(date.getHours() + value)
          break
        case 'minute':
          date.setMinutes(date.getMinutes() + value)
          break
        case 'second':
          date.setSeconds(date.getSeconds() + value)
          break
        default:
          throw new Error('Invalid time unit')
      }

      // 格式化为字符串
      const pad = (num) => num.toString().padStart(2, '0')
      return (
        [date.getFullYear(), pad(date.getMonth() + 1), pad(date.getDate())].join('-') +
        ' ' +
        [pad(date.getHours()), pad(date.getMinutes()), pad(date.getSeconds())].join(':')
      )
    }
  }
}
