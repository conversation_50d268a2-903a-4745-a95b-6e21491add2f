<!-- 饼图 -->
<template>
  <common-echart-dom
    :id="id"
    ref="chart"
    :width="width"
    :height="height"
    :option="option"
    :indexId="indexId"
    :paramsForUnBindingComp="paramsForUnBindingComp"
    @chartOption="loadChart"
  />
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({})
    },
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  methods: {
    loadChart(chartOption) {
      if (!this.$refs.chart.dimensionData.length) {
        chartOption.series[0].data = this.$refs.chart.indexData.map((item, index) => {
          return {
            value: item.data[0],
            name: item.name
          }
        })
        chartOption.legend.data = this.$refs.chart.indexData.map((item) => item.name)
        chartOption.series = chartOption.series.filter((item) => item.data.length)
      } else {
        chartOption.series[0].data = this.$refs.chart.dimensionData[0].data.map((item, index) => {
          return {
            value: this.$refs.chart.indexData[0].data[index],
            name: item
          }
        })
        chartOption.legend.data = this.$refs.chart.dimensionData[0].data
      }
      chartOption.tooltip.valueFormatter = (value) =>
        `${value}${this.$refs.chart.indexData[0].unit}`
      this.$refs.chart.chart.setOption(chartOption)
    }
  }
}
</script>

<style scoped></style>
