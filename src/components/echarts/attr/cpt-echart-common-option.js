// echarts图表通用属性
export default {
  attribute: {
    chartTitle: '标题一',
    scroll: 0,
    scrollNum: 0,
    autoScroll: 0,
    scrollStyle: `style = {
  bottom: 0,
  type: 'slider',
  showDetail: false,
  show: true,
  startValue: 0,
  height: 17, // 高度
  handleSize: '100%', // 手柄的大小
  handleIcon:
    'path://M50 0 C22.4 0 0 22.4 0 50 C0 77.6 22.4 100 50 100 C77.6 100 100 77.6 100 50 C100 22.4 77.6 0 50 0 Z', // 圆形手柄
  handleStyle: {
    color: '#447bcf', // 手柄颜色
    borderColor: '#fff', // 手柄边框颜色
    borderWidth: 1
  },
  fillerColor: 'rgba(85, 147, 253,0.6)', // 选中范围的填充颜色
  backgroundColor: 'rgba(47, 69, 84, 0.1)', // 背景色
  borderColor: '#ddd', // 边框颜色
  brushSelect: false,
  zoomLock: true
}`,
    gridTop: '15%',
    gridBottom: '15%',
    gridRight: '15%',
    gridLeft: '15%',
    axisLabelColor: 'rgba(0, 0, 0, 1)',
    nameTextColor: 'rgba(0, 0, 0, 1)',
    axisLineColor: 'rgba(0, 0, 0, 1)',
    showQuery: 0,
    queryTop: 0,
    queryLeft: 0,
    indexSet: undefined,
    dimenssionUsed: [],
    indexUsed: []
  }
}
