<!-- 雷达图 -->
<template>
  <common-echart-dom
    :id="id"
    ref="chart"
    :width="width"
    :height="height"
    :option="option"
    :indexId="indexId"
    :paramsForUnBindingComp="paramsForUnBindingComp"
    @chartOption="loadChart"
  />
</template>

<script>
export default {
  name: 'Radar<PERSON>hart',
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({})
    },
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  methods: {
    loadChart(chartOption) {
      // let chartOption = JSON.parse(JSON.stringify(this.option))
      for (const i in this.$refs.chart.dimensionData[0].data) {
        chartOption.radar.indicator[i] = {
          name: this.$refs.chart.dimensionData[0].data[i],
          min: 0
        }
      }
      this.$refs.chart.indexData.forEach((data, i) => {
        chartOption.series[0].data[i] = {
          value: data.data,
          name: data.name
        }
        chartOption.legend.data[i] = data.name
      })
      this.$refs.chart.chart.setOption(chartOption)
    }
  }
}
</script>

<style scoped></style>
