<template>
  <div style="display: flex; align-items: end">
    <div>
      <div
        v-for="item in queryCompList.filter((queryComp) => queryComp.isShow)"
        :key="item.id"
        class="query-content"
        style="height: 40px"
      >
        <el-input v-if="item.queryType === 'input'" v-model="item.dbDefaultVal">
          <!-- <el-button slot="append" icon="el-icon-search" @click="search"></el-button> -->
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            :style="{ cursor: 'pointer' }"
            @click="() => $emit('queryInfo', queryCompList)"
          ></i>
        </el-input>
        <el-select
          v-if="item.queryType === 'select'"
          v-model="item.dbDefaultVal"
          @change="() => $emit('queryInfo', queryCompList)"
        >
          <el-option
            v-for="option in getDictOptions(item.dictCode)"
            :key="option.value"
            :label="option.name"
            :value="option.value"
          ></el-option>
        </el-select>
        <div v-if="item.queryType === 'datepicker'" style="display: flex; align-items: center">
          <el-date-picker
            v-model="item.startTime"
            allowClear
            show-time
            placeholder="请选择日期"
            :format="getDateFormatStr(item.dateFormatStr)"
            @change="handleDateChange"
          />
          &nbsp;-&nbsp;
          <el-date-picker
            v-model="item.endTime"
            allowClear
            show-time
            placeholder="请选择日期"
            :format="getDateFormatStr(item.dateFormatStr)"
            @change="handleDateChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getDictItemsFromCache } from '@/api/api'
export default {
  name: 'QueryComp',
  props: {
    queryCompList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      equal: '等于',
      optionRuleArray: [
        { id: 'eq', name: '等于' },
        { id: 'like', name: '包含' },
        { id: 'right_like', name: '以..开始' },
        { id: 'left_like', name: '以..结尾' },
        { id: 'in', name: '在...中' },
        { id: 'ne', name: '不等于' },
        { id: 'gt', name: '大于' },
        { id: 'ge', name: '大于等于' },
        { id: 'lt', name: '小于' },
        { id: 'le', name: '小于等于' }
      ]
    }
  },
  mounted() {
    if (this.queryCompList.length)
      for (let comp of this.queryCompList) {
        if (comp.queryType !== 'datepicker') continue
        comp.startTime = this.adjustDate(comp.startTime, comp.dateFormatStr)
        comp.endTime = this.adjustDate(comp.endTime, comp.dateFormatStr)
      }
  },
  methods: {
    getDictOptions(dictCode) {
      return getDictItemsFromCache(dictCode)
    },
    getDateFormatStr(dateFormatStr) {
      const list = getDictItemsFromCache('date_format')
      return list.filter((item) => {
        return item.value === dateFormatStr
      })[0].text
    },
    adjustDate(value, unit) {
      // 解析原始日期
      const date = new Date() // 替换为兼容格式
      // 根据单位进行增减操作
      switch (unit.toLowerCase()) {
        case 'year':
          date.setFullYear(date.getFullYear() + value)
          break
        case 'month':
          date.setMonth(date.getMonth() + value)
          break
        case 'day':
          date.setDate(date.getDate() + value)
          break
        case 'hour':
          date.setHours(date.getHours() + value)
          break
        case 'minute':
          date.setMinutes(date.getMinutes() + value)
          break
        case 'second':
          date.setSeconds(date.getSeconds() + value)
          break
        default:
          throw new Error('Invalid time unit')
      }

      // 格式化为字符串
      const pad = (num) => num.toString().padStart(2, '0')
      return (
        [date.getFullYear(), pad(date.getMonth() + 1), pad(date.getDate())].join('-') +
        ' ' +
        [pad(date.getHours()), pad(date.getMinutes()), pad(date.getSeconds())].join(':')
      )
    },
    handleDateChange(date) {
      console.log('date', date)
    }
  }
}
</script>
<style lang="less" scoped>
.query-content {
  display: flex;
}
</style>
