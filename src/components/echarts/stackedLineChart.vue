<!-- 堆叠折线图 -->
<template>
  <common-echart-dom
    :id="id"
    ref="chart"
    :width="width"
    :height="height"
    :option="option"
    :indexId="indexId"
    :paramsForUnBindingComp="paramsForUnBindingComp"
    @chartOption="loadChart"
  />
</template>

<script>
export default {
  name: 'StackedLineChart',
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({})
    },
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  methods: {
    loadChart(chartOption) {
      chartOption.xAxis.data = this.$refs.chart.dimensionData[0].data
      chartOption.xAxis.name = this.$refs.chart.dimensionData[0].name
      chartOption.yAxis.name = '单位: ' + this.$refs.chart.indexData[0].unit
      this.$refs.chart.indexData.forEach((data, i) => {
        if (!chartOption.series[i]) chartOption.series[i] = { ...chartOption.series[i - 1] }
        chartOption.series[i].data = data.data
        chartOption.series[i].name = data.name
        chartOption.legend.data[i] = data.name
      })
      chartOption.series.splice(this.$refs.chart.indexData.length)
      chartOption.tooltip.valueFormatter = (value) =>
        `${value}${this.$refs.chart.indexData[0].unit}`
      this.$refs.chart.chart.setOption(chartOption)
    }
  }
}
</script>

<style scoped>
.queryComp {
  position: absolute;
  top: -20px;
}
</style>
