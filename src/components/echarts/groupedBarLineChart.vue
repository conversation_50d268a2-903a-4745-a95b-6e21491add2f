<template>
  <common-echart-dom
    :id="id"
    ref="chart"
    :width="width"
    :height="height"
    :option="option"
    :indexId="indexId"
    :paramsForUnBindingComp="paramsForUnBindingComp"
    @chartOption="loadChart"
  />
</template>
<script>
export default {
  name: 'GroupedBarLineChart',
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({})
    },
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  methods: {
    loadChart(chartOption) {
      const { barColumns, lineColumns, ...rest } = this.$refs.chart.currentCpt.cptOption.attribute
      chartOption.xAxis.data = this.$refs.chart.dimensionData[0].data
      chartOption.xAxis.name = this.$refs.chart.dimensionData[0].name
      chartOption.yAxis[0].name = '单位: ' + this.$refs.chart.indexData[0]?.unit
      chartOption.yAxis[1].name =
        '单位: ' + this.$refs.chart.indexData[this.$refs.chart.indexData.length - 1]?.unit
      this.$refs.chart.indexData.forEach((data, i) => {
        if (!chartOption.series[i]) chartOption.series[i] = { ...chartOption.series[i - 1] }
        chartOption.series[i].data = data.data
        chartOption.series[i].name = data.name
        chartOption.legend.data[i] = data.name
        if (i < barColumns) {
          chartOption.series[i].type = 'bar'
          chartOption.series[i].yAxisIndex = 0
        } else {
          chartOption.series[i].type = 'line'
          chartOption.series[i].yAxisIndex = 1
        }
      })
      chartOption.series.splice(this.$refs.chart.indexData.length)

      this.$refs.chart.chart.setOption(chartOption)
    }
  }
}
</script>
