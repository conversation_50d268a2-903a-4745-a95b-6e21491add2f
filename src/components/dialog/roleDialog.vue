<template>
  <a-drawer
    title="角色授权"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    destroyOnClose
    @close="handleClose"
  >
    <div style="margin-bottom: 20px">
      <el-button type="primary" @click="authorizeAll">全部授权</el-button>
      <el-button type="primary" @click="unAuthorizeAll">全部取消授权</el-button>
    </div>

    <a-table
      ref="table"
      style="height: 500px"
      size="middle"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="confirmLoading"
      @change="handleTableChange"
    >
      <template slot="action" slot-scope="text, record">
        <a @click="onChangeAuthorizeStatus(record.id)">{{
          authorizeList.includes(record.id) ? '取消授权' : '授权'
        }}</a>
      </template>
    </a-table>
  </a-drawer>
</template>
<script>
import { getAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'RoleDialog',
  mixins: [JeecgListMixin],
  props: {
    authorizeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      roleList: [],
      columns: [
        {
          title: '角色编码',
          align: 'center',
          dataIndex: 'roleCode'
        },
        {
          title: '角色名称',
          align: 'center',
          dataIndex: 'roleName'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/sys/role/list'
      }
    }
  },
  methods: {
    show() {
      this.visible = true
      getAction(this.url.list, { pageSize: 100 }).then((res) => {
        this.allRole = res.result.records
      })
      // this.getRoleList()
    },
    onChangeAuthorizeStatus(id) {
      this.$message.success(this.authorizeList.includes(id) ? '取消授权成功' : '授权成功')
      this.$emit('changeAuthorizeStatus', id)
    },
    authorizeAll() {
      this.$emit(
        'BatchAuthorize',
        this.allRole.map((item) => item.id)
      )
    },
    unAuthorizeAll() {
      this.$emit(
        'BatchUnAuthorize',
        this.allRole.map((item) => item.id)
      )
    },

    handleClose() {
      this.visible = false
    }
  }
}
</script>
