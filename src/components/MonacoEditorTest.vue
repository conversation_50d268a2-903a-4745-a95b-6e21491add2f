<template>
  <div style="padding: 20px;">
    <h2>Monaco Editor 测试</h2>
    <div style="margin-bottom: 20px;">
      <a-button @click="changeLanguage">切换语言 ({{ currentLanguage }})</a-button>
      <a-button @click="changeTheme" style="margin-left: 10px;">切换主题 ({{ currentTheme }})</a-button>
      <a-button @click="resizeEditor" style="margin-left: 10px;">调整大小</a-button>
    </div>
    
    <div style="margin-bottom: 20px;">
      <h3>当前内容：</h3>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ editorContent }}</pre>
    </div>
    
    <monaco-editor
      ref="monacoEditor"
      v-model="editorContent"
      :language="currentLanguage"
      :theme="currentTheme"
      width="100%"
      height="400px"
      :options="editorOptions"
      @init="onEditorInit"
      @input="onEditorInput"
      @focus="onEditorFocus"
      @blur="onEditorBlur"
    />
  </div>
</template>

<script>
import MonacoEditor from './MonacoEditor.vue'

export default {
  name: 'MonacoEditorTest',
  components: {
    MonacoEditor
  },
  data() {
    return {
      editorContent: `function hello() {
  console.log("Hello Monaco Editor!");
  return "Welcome to the new editor!";
}

// 这是一个测试函数
hello();`,
      currentLanguage: 'javascript',
      currentTheme: 'vs',
      editorOptions: {
        tabSize: 2,
        fontSize: 14,
        showPrintMargin: false,
        enableBasicAutocompletion: true,
        enableLiveAutocompletion: true,
        enableSnippets: true
      },
      languages: ['javascript', 'css', 'html', 'json'],
      themes: ['vs', 'vs-dark', 'hc-black'],
      currentLanguageIndex: 0,
      currentThemeIndex: 0
    }
  },
  methods: {
    onEditorInit(editor) {
      console.log('Monaco Editor 初始化完成:', editor)
    },
    
    onEditorInput(value) {
      console.log('编辑器内容变化:', value)
    },
    
    onEditorFocus() {
      console.log('编辑器获得焦点')
    },
    
    onEditorBlur() {
      console.log('编辑器失去焦点')
    },
    
    changeLanguage() {
      this.currentLanguageIndex = (this.currentLanguageIndex + 1) % this.languages.length
      this.currentLanguage = this.languages[this.currentLanguageIndex]
      
      // 根据语言切换示例代码
      switch (this.currentLanguage) {
        case 'javascript':
          this.editorContent = `function hello() {
  console.log("Hello Monaco Editor!");
  return "Welcome to the new editor!";
}`
          break
        case 'css':
          this.editorContent = `.container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
}`
          break
        case 'html':
          this.editorContent = `<!DOCTYPE html>
<html>
<head>
  <title>Monaco Editor Test</title>
</head>
<body>
  <h1>Hello World!</h1>
</body>
</html>`
          break
        case 'json':
          this.editorContent = `{
  "name": "monaco-editor-test",
  "version": "1.0.0",
  "description": "Testing Monaco Editor"
}`
          break
      }
    },
    
    changeTheme() {
      this.currentThemeIndex = (this.currentThemeIndex + 1) % this.themes.length
      this.currentTheme = this.themes[this.currentThemeIndex]
    },
    
    resizeEditor() {
      this.$refs.monacoEditor.resize()
      console.log('编辑器大小已调整')
    }
  }
}
</script>

<style scoped>
h2, h3 {
  color: #333;
}

pre {
  max-height: 100px;
  overflow-y: auto;
}
</style>
