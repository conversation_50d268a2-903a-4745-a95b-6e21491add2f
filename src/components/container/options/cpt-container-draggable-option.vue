<template>
  <div>
    <el-form ref="form" label-width="80px" size="mini">
      <el-form-item label="绑定指标集">
        <a-tree-select
          v-model="attribute.indexSet"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :replaceFields="replaceFields"
          :treeData="indexSetTree"
          placeholder="请选择绑定指标集"
          allow-clear
          tree-default-expand-all
          treeNodeFilterProp="title"
          @change="onIndexSetChange"
        ></a-tree-select>
      </el-form-item>
      <el-form-item label="是否为模块">
        <a-radio-group v-model="attribute.isModule">
          <a-radio :value="true"> 是 </a-radio>
          <a-radio :value="false"> 否 </a-radio>
        </a-radio-group>
      </el-form-item>
      <el-form-item label="是否显示账期">
        <a-radio-group v-model="attribute.showFootTimeInfo">
          <a-radio :value="true"> 是 </a-radio>
          <a-radio :value="false"> 否 </a-radio>
        </a-radio-group>
      </el-form-item>
      <el-form-item label="有无AI分析功能">
        <a-radio-group v-model="attribute.hasAi">
          <a-radio :value="true"> 有 </a-radio>
          <a-radio :value="false"> 无 </a-radio>
        </a-radio-group>
      </el-form-item>
      <el-form-item label="背景颜色">
        <el-color-picker v-model="attribute.backgroundColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item label="背景图片">
        <j-image-upload v-model="attribute.backgroundImage"></j-image-upload>
        <el-button @click="showGallery">图库选择</el-button>
      </el-form-item>
      <el-form-item label="background (!important)">
        <el-input v-model="attribute.background"></el-input>
      </el-form-item>
      <el-form-item label="边框颜色">
        <el-color-picker v-model="attribute.borderColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item label="边框宽度">
        <el-input-number v-model="attribute.borderWidth"></el-input-number>
      </el-form-item>
      <el-form-item label="边框弧度">
        <el-input-number v-model="attribute.borderRadius"></el-input-number>
      </el-form-item>
      <el-form-item label="边框样式">
        <el-select v-model="attribute.borderStyle" placeholder="请选择边框样式">
          <el-option label="solid" value="solid" />
          <el-option label="dashed" value="dashed" />
          <el-option label="dotted" value="dotted" />
          <el-option label="double" value="double" />
        </el-select>
      </el-form-item>

      <el-form-item label="是否为弹窗">
        <a-radio-group v-model="attribute.isPopup">
          <a-radio :value="true"> 是 </a-radio>
          <a-radio :value="false"> 否 </a-radio>
        </a-radio-group>
      </el-form-item>
      <!-- <el-form-item label="有无ai总结功能">
        <a-radio-group v-model="attribute.hasAi">
          <a-radio :value="true"> 有 </a-radio>
          <a-radio :value="false"> 无 </a-radio>
        </a-radio-group>
      </el-form-item> -->
      <el-form-item label="模块关键词">
        <el-input
          v-model="attribute.moduleDes"
          type="textarea"
          placeholder="请输入模块关键词"
          :rows="3"
        ></el-input>
      </el-form-item>
      <el-form-item label="大模型提示词">
        <el-input
          v-model="attribute.llmPromption"
          type="textarea"
          :rows="4"
          :title="attribute.llmPromption"
          placeholder="请输入大模型提示词"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <gallery ref="gallery" @confirmCheck="confirmCheck" />
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import { useDesignerStore } from '@/stores/designer'
import { mapWritableState } from 'pinia'
export default {
  name: 'CptContainerDraggableOption',
  props: {
    attribute: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      indexSetInfo: {},
      editorOptions: {
        tabSize: 4,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      },
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      }
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCptId', 'indexSetTree'])
  },
  methods: {
    onIndexSetChange(val) {
      getAction('/index/scCockpitIndexGroupConfig/queryById', { id: val }).then((res) => {
        this.indexSetInfo = res.result
        // if (this.indexSetInfo.indexGroupType === 'sql') {
        //   this.attribute.body = ''
        // }
      })
      this.$bus.$emit('indexSetId', { indexSetId: val, pid: this.currentCptId })
    },
    showGallery() {
      this.$refs.gallery.opened()
    },
    confirmCheck(fileUrl) {
      this.attribute.backgroundImage = fileUrl
    }
  }
}
</script>
