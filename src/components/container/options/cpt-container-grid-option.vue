<template>
  <div>
    <el-form label-width="80px" size="mini">
      <el-form-item label="列数">
        <el-input-number v-model="attribute.gridColumns" :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="行数">
        <el-input-number v-model="attribute.gridRows" :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="列间距">
        <el-input-number v-model="attribute.columnGap"></el-input-number>
      </el-form-item>
      <el-form-item label="行间距">
        <el-input-number v-model="attribute.rowGap"></el-input-number>
      </el-form-item>
      <el-form-item label="水平对齐">
        <el-select v-model="attribute.justifyItems" placeholder="请选择水平对齐方式">
          <el-option label="start" value="start" />
          <el-option label="end" value="end" />
          <el-option label="center" value="center" />
          <el-option label="stretch" value="stretch" />
        </el-select>
      </el-form-item>
      <el-form-item label="垂直对齐">
        <el-select v-model="attribute.alignItems" placeholder="请选择垂直对齐方式">
          <el-option label="start" value="start" />
          <el-option label="end" value="end" />
          <el-option label="center" value="center" />
          <el-option label="stretch" value="stretch" />
        </el-select>
      </el-form-item>
      <el-form-item label="整体水平">
        <el-select v-model="attribute.justifyContent" placeholder="请选择整体水平分布">
          <el-option label="start" value="start" />
          <el-option label="end" value="end" />
          <el-option label="center" value="center" />
          <el-option label="space-around" value="space-around" />
          <el-option label="space-between" value="space-between" />
          <el-option label="space-evenly" value="space-evenly" />
        </el-select>
      </el-form-item>
      <el-form-item label="整体垂直">
        <el-select v-model="attribute.alignContent" placeholder="请选择整体垂直分布">
          <el-option label="start" value="start" />
          <el-option label="end" value="end" />
          <el-option label="center" value="center" />
          <el-option label="space-around" value="space-around" />
          <el-option label="space-between" value="space-between" />
          <el-option label="space-evenly" value="space-evenly" />
          <el-option label="stretch" value="stretch" />
        </el-select>
      </el-form-item>
      <el-form-item label="自动填充">
        <el-select v-model="attribute.autoFill" placeholder="请选择自动填充方式">
          <el-option label="auto-fill" value="auto-fill" />
          <el-option label="auto-fit" value="auto-fit" />
          <el-option label="none" value="none" />
        </el-select>
      </el-form-item>
      <el-form-item label="子容器数">
        <el-input-number v-model="attribute.childrenNum" :min="0"></el-input-number>
      </el-form-item>
      <el-form-item label="背景颜色">
        <el-color-picker v-model="attribute.backgroundColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item label="背景图片">
        <j-image-upload v-model="attribute.backgroundImage"></j-image-upload>
      </el-form-item>
      <el-form-item label="上内边距">
        <el-input-number v-model="attribute.paddingTop"></el-input-number>
      </el-form-item>
      <el-form-item label="下内边距">
        <el-input-number v-model="attribute.paddingBottom"></el-input-number>
      </el-form-item>
      <el-form-item label="左内边距">
        <el-input-number v-model="attribute.paddingLeft"></el-input-number>
      </el-form-item>
      <el-form-item label="右内边距">
        <el-input-number v-model="attribute.paddingRight"></el-input-number>
      </el-form-item>
      <el-form-item label="边框颜色">
        <el-color-picker v-model="attribute.borderColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item label="边框宽度">
        <el-input-number v-model="attribute.borderWidth"></el-input-number>
      </el-form-item>
      <el-form-item label="边框样式">
        <el-select v-model="attribute.borderStyle" placeholder="请选择边框样式">
          <el-option label="solid" value="solid" />
          <el-option label="dashed" value="dashed" />
          <el-option label="dotted" value="dotted" />
          <el-option label="double" value="double" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'CptContainerGridOption',
  props: {
    attribute: {
      type: Object,
      required: true
    }
  }
}
</script>
