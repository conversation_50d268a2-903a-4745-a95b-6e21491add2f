<template>
  <div>
    <el-form label-width="90px" size="mini">
      <el-form-item label="子容器数">
        <el-input-number v-model="attribute.childrenNum" :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="flex方向">
        <el-select v-model="attribute.flexDirection" placeholder="请选择flex方向">
          <el-option label="row" value="row" />
          <el-option label="column" value="column" />
        </el-select>
      </el-form-item>
      <el-form-item label="自动换行">
        <el-select v-model="attribute.flexWrap" placeholder="请选择是否自动换行">
          <el-option label="wrap" value="wrap" />
          <el-option label="nowrap" value="nowrap" />
        </el-select>
      </el-form-item>
      <el-form-item label="水平布局">
        <el-select v-model="attribute.justifyContent" placeholder="请选择水平布局方式">
          <el-option label="start" value="start" />
          <el-option label="end" value="end" />
          <el-option label="space-around" value="space-around" />
          <el-option label="space-between" value="space-between" />
          <el-option label="space-evenly" value="space-evenly" />
          <el-option label="center" value="center" />
        </el-select>
      </el-form-item>
      <el-form-item label="垂直布局">
        <el-select v-model="attribute.alignItems" placeholder="请选择垂直布局方式">
          <el-option label="start" value="start" />
          <el-option label="end" value="end" />
          <el-option label="stretch" value="stretch" />
          <el-option label="baseline" value="baseline" />
          <el-option label="center" value="center" />
        </el-select>
      </el-form-item>
      <el-form-item label="子容器间距">
        <el-input-number v-model="attribute.gap"></el-input-number>
      </el-form-item>
      <el-form-item label="背景颜色">
        <el-color-picker v-model="attribute.backgroundColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item label="背景图片">
        <j-image-upload v-model="attribute.backgroundImage"></j-image-upload>
      </el-form-item>
      <el-form-item label="上内边距">
        <el-input-number v-model="attribute.paddingTop"></el-input-number>
      </el-form-item>
      <el-form-item label="下内边距">
        <el-input-number v-model="attribute.paddingBottom"></el-input-number>
      </el-form-item>
      <el-form-item label="左内边距">
        <el-input-number v-model="attribute.paddingLeft"></el-input-number>
      </el-form-item>
      <el-form-item label="右内边距">
        <el-input-number v-model="attribute.paddingRight"></el-input-number>
      </el-form-item>
      <el-form-item label="边框颜色">
        <el-color-picker v-model="attribute.borderColor" show-alpha></el-color-picker>
      </el-form-item>
      <el-form-item label="边框宽度">
        <el-input-number v-model="attribute.borderWidth"></el-input-number>
      </el-form-item>
      <el-form-item label="边框样式">
        <el-select v-model="attribute.borderStyle" placeholder="请选择边框样式">
          <el-option label="solid" value="solid" />
          <el-option label="dashed" value="dashed" />
          <el-option label="dotted" value="dotted" />
          <el-option label="double" value="double" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否为模块">
        <a-radio-group v-model="attribute.isModule">
          <a-radio :value="true"> 是 </a-radio>
          <a-radio :value="false"> 否 </a-radio>
        </a-radio-group>
      </el-form-item>
      <el-form-item label="是否为弹窗">
        <a-radio-group v-model="attribute.isPopup">
          <a-radio :value="true"> 是 </a-radio>
          <a-radio :value="false"> 否 </a-radio>
        </a-radio-group>
      </el-form-item>
      <el-form-item label="模块关键词">
        <el-input
          v-model="attribute.moduleDes"
          type="textarea"
          placeholder="请输入模块关键词"
          :rows="3"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'CptContainerFlexOption',
  props: {
    attribute: {
      type: Object,
      required: true
    }
  }
}
</script>
