<template>
  <el-form label-width="90px" size="mini">
    <el-form-item label="绑定指标集">
      <a-tree-select
        v-model="attribute.indexSet"
        show-search
        style="width: 100%"
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        :replaceFields="replaceFields"
        :treeData="indexSetTree"
        placeholder="请选择绑定指标集"
        allow-clear
        tree-default-expand-all
        treeNodeFilterProp="title"
        @change="onIndexSetChange"
      ></a-tree-select>
    </el-form-item>
    <el-form-item label="背景颜色">
      <el-color-picker v-model="attribute.backgroundColor" show-alpha></el-color-picker>
    </el-form-item>
    <el-form-item label="背景图片">
      <j-image-upload v-model="attribute.backgroundImage"></j-image-upload>
      <el-button @click="showGallery">图库选择</el-button>
    </el-form-item>
    <el-form-item label="边框颜色">
      <el-color-picker v-model="attribute.borderColor" show-alpha></el-color-picker>
    </el-form-item>
    <el-form-item label="边框宽度">
      <el-input-number v-model="attribute.borderWidth"></el-input-number>
    </el-form-item>
    <el-form-item label="边框弧度">
      <el-input-number v-model="attribute.borderRadius"></el-input-number>
    </el-form-item>
    <el-form-item label="边框样式">
      <el-select v-model="attribute.borderStyle" placeholder="请选择边框样式">
        <el-option label="solid" value="solid" />
        <el-option label="dashed" value="dashed" />
        <el-option label="dotted" value="dotted" />
        <el-option label="double" value="double" />
      </el-select>
    </el-form-item>
    <el-form-item label="关联容器">
      <el-select
        v-model="attribute.innerContainer"
        multiple
        placeholder="按序选择内部关联容器或组件"
      >
        <el-option
          v-for="item in currentCpt.children || []"
          :key="item.id"
          :label="item.cptOption.attribute.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="tab选项来源">
      <el-radio-group v-model="attribute.tabType">
        <el-radio :label="0">按钮</el-radio>
        <el-radio :label="1">标签</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="tab选项来源">
      <el-radio-group v-model="tabOrigin" @input="onTabOriginChange">
        <el-radio :label="1">指标集</el-radio>
        <el-radio :label="0">自定义</el-radio>
      </el-radio-group>
    </el-form-item>
    <div v-if="!!tabOrigin">
      <el-form-item label="tab选项来源指标集">
        <a-tree-select
          v-model="attribute.tabIndexSet"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :replaceFields="replaceFields"
          :treeData="indexSetTree"
          placeholder="请选择tab选项绑定指标集"
          allow-clear
          tree-default-expand-all
          treeNodeFilterProp="title"
          @change="ontabIndexSetChange"
        ></a-tree-select>
      </el-form-item>
      <el-form-item label="tab选项来源字段">
        <el-select v-model="attribute.tabColumn">
          <el-option
            v-for="item in tabColumnList"
            :key="item.id"
            :value="item.sourceColumn"
            :label="item.sourceColumn"
            >{{ item.sourceColumn }}</el-option
          >
        </el-select>
      </el-form-item>
    </div>
    <div v-else>
      <el-form-item label="tab数量">
        <el-input-number v-model="attribute.tabNum" @change="handleTabNumChange"></el-input-number>
      </el-form-item>
      <el-form-item v-for="item in attribute.tabNum" :key="item" :label="`tab${item}内容`">
        <el-input v-model="attribute.tabContent[item - 1]"></el-input>
      </el-form-item>
    </div>
    <el-form-item v-if="!attribute.tabType" label="tab未激活样式" style="position: relative">
      <el-button
        style="position: absolute; top: 30px; left: -75px"
        @click="convertCssToJs('tabStyle', attribute.tabStyle)"
        >css转js</el-button
      >
      <monaco-editor
        ref="tabStyle"
        v-model="attribute.tabStyle"
        language="javascript"
        width="100%"
        height="200px"
        :options="editorOptions"
      ></monaco-editor>
    </el-form-item>
    <el-form-item v-if="!attribute.tabType" label="tab激活样式" style="position: relative">
      <el-button
        style="position: absolute; top: 30px; left: -75px"
        @click="convertCssToJs('activeTabStyle', attribute.activeTabStyle)"
        >css转js</el-button
      >
      <monaco-editor
        ref="activeTabStyle"
        v-model="attribute.activeTabStyle"
        language="javascript"
        width="100%"
        height="200px"
        :options="editorOptions"
      ></monaco-editor>
    </el-form-item>
    <el-form-item label="是否为模块">
      <el-radio-group v-model="attribute.isModule">
        <el-radio :label="true">是</el-radio>
        <el-radio :label="false">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="是否为弹窗">
      <el-radio-group v-model="attribute.isPopup">
        <el-radio :label="true">是</el-radio>
        <el-radio :label="false">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="模块关键词">
      <el-input
        v-model="attribute.moduleDes"
        type="textarea"
        placeholder="请输入模块关键词"
        :rows="3"
      ></el-input>
    </el-form-item>
    <el-form-item label="大模型提示词">
      <el-input
        v-model="attribute.llmPromption"
        type="textarea"
        :rows="4"
        :title="attribute.llmPromption"
        placeholder="请输入大模型提示词"
      >
      </el-input>
    </el-form-item>
    <gallery ref="gallery" @confirmCheck="confirmCheck" />
  </el-form>
</template>
<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { getAction } from '@/api/manage'
export default {
  name: 'CptContainersDraggableTabOption',
  props: {
    attribute: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      editorOptions: {
        tabSize: 4,
        fontSize: 14,
        showPrintMargin: false //去除编辑器里的竖线
      },
      indexSetInfo: {},
      replaceFields: {
        children: 'childList',
        title: 'groupName',
        key: 'id',
        value: 'id'
      },
      tabOrigin: 1,
      tabColumnList: []
    }
  },
  watch: {
    attribute: {
      handler(val) {
        // console.log('val', val)
      },
      deep: true
    }
  },
  mounted() {
    if (!this.attribute.tabType) {
      this.$nextTick(() => {
        this.$refs.tabStyle && this.$refs.tabStyle.editor.resize()
        this.$refs.activeTabStyle && this.$refs.activeTabStyle.editor.resize()
      })
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, [
      'currentCpt',
      'currentCptId',
      'componentData',
      'indexSetTree'
    ])
  },
  methods: {
    handleTabNumChange(val) {
      // eslint-disable-next-line vue/no-mutating-props
      this.attribute.tabContent.splice(val)
    },
    onIndexSetChange(val) {
      getAction('/index/scCockpitIndexGroupConfig/queryById', { id: val }).then((res) => {
        this.indexSetInfo = res.result
        this.$bus.$emit('indexSetId', { indexSetId: val, pid: this.currentCptId })
      })
    },
    ontabIndexSetChange(val) {
      getAction('/index/scCockpitIndexGroupConfig/queryById', {
        id: val
      }).then((res) => {
        if (res.success) {
          this.tabColumnList = res.result.groupColumnList
        }
      })
    },
    onTabOriginChange(val) {
      if (val) {
        this.attribute.tabContent = []
        this.attribute.tabNum = 0
      } else {
        this.attribute.tabContent = []
        this.attribute.tabIndexSet = ''
        this.attribute.tabColumn = ''
      }
    },
    convertCssToJs(column, cssStr) {
      try {
        this.attribute[column] = this.$convertCssToJs(cssStr)
      } catch (e) {}
    },
    showGallery() {
      this.$refs.gallery.opened()
    },
    confirmCheck(fileUrl) {
      this.attribute.backgroundImage = fileUrl
    }
  }
}
</script>
