<template>
  <div
    class="container-draggable"
    :style="{
      width: `${$attrs.width}px`,
      height: `${$attrs.height}px`,
      backgroundImage: option.attribute.backgroundImage
        ? `url('${getFileAccessHttpUrl(option.attribute.backgroundImage)}')`
        : 'none',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center center',
      backgroundSize: '100% 100%',
      backgroundColor: option.attribute.backgroundColor,
      ...(option.attribute.background && {
        background: `${option.attribute.background} !important`
      }),
      borderColor: option.attribute.borderColor,
      borderWidth: `${option.attribute.borderWidth}px`,
      borderStyle: option.attribute.borderStyle,
      borderRadius: option.attribute.borderRadius + 'px',
      outline: isDragOver ? '1px dashed #409eff' : 'none'
    }"
    @dragover.prevent.stop="handleDragOver"
    @drop.prevent.stop="handleDrop"
    @dragleave.prevent.stop="handleDragLeave"
    @dragenter.prevent.stop="handleDragEnter"
    @mousedown="handleContainerMouseDown"
  >
    <button v-if="option.attribute.isPopup" class="close-btn" @click="close">关闭</button>
    <Shape
      v-for="item in containerComponentData"
      v-show="item.cptOption.attribute.visible"
      :key="item.id"
      :style="{
        width: `${item.cptWidth}px`,
        height: `${item.cptHeight}px`,
        top: Math.round(item.cptY) + 'px',
        left: Math.round(item.cptX) + 'px'
      }"
      :active="currentCptId === item.id"
      :element="item"
      :containerScale="containerScale"
      :isPreview="isPreview"
      class="shape-in-draggable"
      @dragstop="handleShapeOperationEnd(item)"
      @resizestop="handleShapeOperationEnd(item)"
      @contextmenu.native.prevent="handleComponentContextMenu($event, item)"
    >
      <component
        :is="item.cptKey"
        :id="item.id"
        :ref="item.id"
        :width="Math.round(item.cptWidth)"
        :height="Math.round(item.cptHeight)"
        :option="item.cptOption"
        :indexId="item.cptIndexId"
        :paramsForUnBindingComp="item.paramsToLoadDataWithoutBindingIndexSet"
        :isPreview="isPreview"
        :pIndexSetData="indexSetData"
        :pIndexSetId="indexSetId"
      />
    </Shape>
    <MarkLine
      v-show="containerComponentData.findIndex((item) => item.id === currentCptId) !== -1"
    />
    <div
      v-if="option.attribute.showFootTimeInfo"
      class="footer"
      style="width: 100%; position: absolute; bottom: 0; padding: 10px 5%"
    >
      <div class="separator"></div>
      <div :id="uuid" class="grid-container">
        <div>数据来源：{{ footerTimeInfo?.dataOrigin }}</div>
        <div>更新时间：{{ footerTimeInfo?.dataTime }}</div>
        <div>更新周期：{{ footerTimeInfo?.dataFrequency }}</div>
        <div>数据期别：{{ footerTimeInfo?.syncTime }}</div>
      </div>
    </div>
    <img
      v-show="option.attribute.hasAi"
      src="@/assets/img/AI.png"
      alt=""
      style="height: 105px; width: 177px; position: absolute; right: 0; top: -13px"
    />
  </div>
</template>

<script>
import { mapWritableState, mapActions } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { getAction, postAction, getFileAccessHttpUrl } from '@/api/manage'
import { findNodeById } from '@/utils/util'
import { handleSpecialForm } from '@/views/designer/components/handleSpecialForm'
import Shape from '@/views/designer/Shape.vue'
import MarkLine from '@/views/designer/MarkLine.vue'
import cptOptions from '@/components/options'
import { chartItem } from '@/views/designer/components/constant.js'
import { cloneDeep } from 'lodash-es'
import { getDictItemsFromCache } from '@/api/api'
export default {
  name: 'CptContainerDraggable',
  components: {
    Shape,
    MarkLine
  },
  props: {
    id: {
      type: String,
      required: true
    },
    option: {
      type: Object,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    containerScale: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      uuid: '',
      isDragOver: false,
      queryOption: [],
      indexSetData: null,
      indexSetId: '',
      footerTimeInfo: null,
      cptOptions,
      url: {
        previewData: '/index/scCockpitIndexGroupConfig/previewData',
        queryIndexSet: '/module/scCockpitModuleConfig/queryByIndexId'
      }
    }
  },
  watch: {
    'option.attribute.showFootTimeInfo': {
      handler: function (val) {
        if (val) {
          this.getFootTimeInfo()
        }
      }
    },
    'option.attribute.indexSet': {
      handler: function (val) {
        if (val) {
          this.getFootTimeInfo()
        }
      }
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, [
      'currentCptId',
      'componentData',
      'currentCpt',
      'selectedComponentIds'
    ]),
    containerComponentData() {
      return findNodeById(this.id, this.componentData)?.children ?? []
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
    this.indexSetId = this.option.attribute.indexSet
    this.getIndexSetData()
    this.getFootTimeInfo()
    this.$bus.$on('pid', (pid) => {
      this.id === pid && this.refreshCptData()
    })
    this.$bus.$on('indexSetId', (data) => {
      if (this.id === data.pid) {
        this.indexSetId = data.indexSetId
        this.getIndexSetData()
      }
    })
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const foundIndex = this.queryOption.findIndex(
          (item) => item.column === columnInfo.columns[index].name
        )
        if (foundIndex < 0)
          this.queryOption.push({
            value: columnInfo.option,
            column: columnInfo.columns[index].name
          })
        else this.queryOption[foundIndex].value = columnInfo.option
        this.getIndexSetData()
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off('pid')
    this.$bus.$off('indexSetId')
    this.$bus.$off('onTabClick')
  },
  methods: {
    ...mapActions(useDesignerStore, ['pushHistory']),
    refreshCptData() {
      const refName = this.currentCptId
      const cptRef = this.$refs[refName]
      let componentInstance = Array.isArray(cptRef) ? cptRef[0] : cptRef
      if (componentInstance.$refs.chart) componentInstance = componentInstance.$refs.chart
      if (!componentInstance.refreshCptData) {
        this.$message.warning('当前图层还未实现refreshCptData方法')
      } else {
        componentInstance.refreshCptData() //刷新子组件数据，refs为组建名加index
      }
    },
    getIndexSetData() {
      const params = {
        connType: 'And',
        pageNo: 1,
        pageSize: 1000,
        where: this.queryOption.length
          ? this.queryOption.map((item) => {
              return { column: item.column, value: item.value, operator: 'eq' }
            })
          : []
      }
      if (this.indexSetId) {
        postAction(
          `/index/scCockpitIndexGroupConfig/previewData?id=${this.indexSetId}`,
          params
        ).then((res) => {
          this.indexSetData = res.result.records
        })
      }
      const node = findNodeById(this.id, this.componentData)
      if (node && node.children) {
        for (const child of node.children) {
          child.pIndexSetId = this.indexSetId
        }
      }
    },
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    handleMouseDown(e, item) {
      this.currentCptId = item.id
      this.currentCpt = item
    },
    addComponent(cpt) {
      const node = findNodeById(this.id, this.componentData)
      if (node) {
        this.$set(node, 'children', node.children?.slice().concat(cpt) || [cpt])
        this.pushHistory('添加组件', cpt.id)
      }
    },
    hideCpt(item) {
      console.log('🚀 ~ hideCpt ~ item:', item)
      item.cptOption.attribute.visible = !item.cptOption.attribute.visible
      this.pushHistory(item.cptOption.attribute.visible ? '显示组件' : '隐藏组件', item.id)
    },
    copyCpt(item) {
      // 递归函数，用于为组件及其子组件生成新的 ID
      const generateNewIds = (component, pid) => {
        component.pid = pid
        component.id = `${component.cptKey}-${require('nanoid').nanoid(12)}`
        if (component.children && component.children.length > 0) {
          component.children.forEach((child) => generateNewIds(child, component.id))
        }
      }

      let copyItem = JSON.parse(JSON.stringify(item))
      copyItem.cptX = item.cptX + 30 // 复制的组件向右下偏移
      copyItem.cptY = item.cptY + 30

      // 为复制的顶层组件及其所有子组件生成新 ID
      generateNewIds(copyItem, copyItem.pid || '')

      const node = findNodeById(this.id, this.componentData)
      node.children.push(copyItem)
      this.currentCptId = copyItem.id // 聚焦到复制的组件
      this.pushHistory('复制组件', copyItem.id)
    },
    delCpt(cptId) {
      const node = findNodeById(this.id, this.componentData)
      if (node) {
        this.$set(
          node,
          'children',
          node.children.filter((item) => item.id !== cptId)
        )
        this.pushHistory('删除组件', cptId)
      }
    },
    handleDragEnter(event) {
      this.isDragOver = true
    },
    handleDragLeave(event) {
      this.isDragOver = false
    },
    handleDragOver(event) {
      // 允许拖拽
      event.dataTransfer.dropEffect = 'copy'
      this.isDragOver = true
    },
    async handleDrop(e) {
      this.isDragOver = false
      const cptConfig = e.dataTransfer.getData('config')

      //从组件栏丢下组件
      if (cptConfig) {
        let config = JSON.parse(cptConfig)
        let option = {}
        const compTemplateInfoRes = await getAction('/component/scCockpitIndexComponent/list', {
          componentName: config.name
        })
        const compTemplateInfo = compTemplateInfoRes.result.records[0]
        if (compTemplateInfo) {
          try {
            option = new Function('return ' + compTemplateInfo.jsData)() // 字符串转对象，并去掉了'option = ''
          } catch (error) {
            console.error('解析字符串代码时出错:', error)
          }
        }
        if (config.option.attribute.indexSet) config.option.attribute.indexSet = this.indexSetId
        config.option = Object.assign(config.option, option)
        if (config.option.cptDataForm) {
          if (!config.option.cptDataForm.apiUrl) {
            config.option.cptDataForm.apiUrl = '/design/test'
          }
          if (!config.option.cptDataForm.sql) {
            config.option.cptDataForm.sql = 'select username from sys_user limit 1'
          }
        }
        let component = {
          cptTitle: config.name,
          icon: config.icon,
          cptKey: config.cptKey,
          cptOptionKey: config.cptOptionKey ? config.cptOptionKey : config.cptKey + '-option',
          cptOption: config.option,
          cptCssData: {
            width: config.width ? config.width : 400,
            height: config.height ? config.height : 300
          },
          paramsToLoadDataWithoutBindingIndexSet: {
            tableName: '',
            indexList: [],
            dimensionList: [],
            dataSourceCode: '',
            unitList: []
          },
          cptX: Math.round(e.offsetX),
          cptY: Math.round(e.offsetY),
          cptZ: 100,
          cptWidth: config.width ? config.width : 702,
          cptHeight: config.height ? config.height : 300,
          id: `${config.cptKey}-${require('nanoid').nanoid(12)}`,
          pid: this.id,
          pIndexSetId: this.indexSetId || ''
        }
        this.addComponent(component)
        handleSpecialForm(component)
        this.$emit('showConfigBar', component)

        // this.cacheChoices = {} //多选清空
        // this.showConfigBar({}, cpt, this.containerComponentData.length - 1) //丢下组件后刷新组件属性栏
        // this.$refs['configBar'].showCptConfig()
      } else {
        const indicatorId = e.dataTransfer.getData('indicatorId')
        // 拖拽绑定组件，此时无copyDom
        getAction(this.url.queryIndexSet, { indexId: indicatorId }).then((res) => {
          if (res.success) {
            const cpt = JSON.parse(res.result.moduleData).componentList[0]
            let option = {}
            try {
              option = new Function('return ' + cpt.jsData)() // 字符串转对象，并去掉了'option = ''
            } catch (error) {
              console.error('解析字符串代码时出错:', error)
            }
            const attribute = this.getAttribute(cpt.componentType)
            let component = {
              cptTitle: cpt.titleName,
              cptKey: cpt.componentType,
              // cptOptionKey: containerComponentData.componentType + '-option',
              cptOption: {
                ...option,
                attribute: {
                  ...attribute,
                  bgImage: cpt.bgImage || ''
                }
              },
              cptCssData: JSON.parse(cpt.cssData || '{}'),
              cptX: Math.round(e.offsetX),
              cptY: Math.round(e.offsetY),
              cptZ: 100,
              cptWidth: cpt.jsData ? JSON.parse(cpt.cssData).width : 400,
              cptHeight: cpt.cssData ? JSON.parse(cpt.cssData).height : 300,
              cptIndexId: cpt.indexId,
              // cptData: data,
              // cptIndexList: cpt.indexList,
              // cptDimensionList: cpt.dimensionList,
              isBindingComponent: true,
              pid: this.id,
              pIndexSetId: this.indexSetId || '',
              id: `${cpt.componentType}-${require('nanoid').nanoid(12)}`
            }
            handleSpecialForm(component)
            this.addComponent(component)
            this.$emit('showConfigBar', component)
            // this.cacheChoices = {} //多选清空
            // this.showConfigBar({}, cpt, this.containerComponentData.length - 1) //丢下组件后刷新组件属性栏
            // this.$refs['configBar'].showCptConfig()
          } else {
            this.$message.warning('组件数据查询失败')
          }
        })
      }
    },
    getAttribute(componentType) {
      for (const { children } of this.cptOptions) {
        if (!children) continue
        const found = children.find((child) => child.cptKey === componentType)
        if (found) {
          return cloneDeep(found.option.attribute)
        }
      }
      for (const { content } of chartItem) {
        if (!content) continue
        const found = content.find((child) => child.cptKey === componentType)
        if (found) {
          return cloneDeep(found.option.attribute)
        }
      }
    },
    getFootTimeInfo() {
      if (!this.option.attribute.showFootTimeInfo) return
      postAction(this.url.previewData + '?id=' + this.indexSetId, {
        pageNo: 1,
        pageSize: 1000,
        connType: 'And'
      }).then((res) => {
        if (res.success && res.result.updateInfo) {
          this.footerTimeInfo = res.result.updateInfo
          const dataFrequencyDict = getDictItemsFromCache('data_frequency')
          this.footerTimeInfo.dataFrequency = dataFrequencyDict.filter(
            (item) => item.value === this.footerTimeInfo.dataFrequency
          )[0]?.text
          this.option.attribute.updateInfo = this.footerTimeInfo
        } else {
          this.footerTimeInfo = null
          this.option.attribute.updateInfo = null
        }
      })
    },
    open() {
      this.$emit('popup', true)
    },
    close() {
      this.$emit('popup', false)
    },
    handleShapeOperationEnd(item) {
      // 检查是否有多个组件被选中
      const selectedIds = this.selectedComponentIds
      if (selectedIds.length > 1 && selectedIds.includes(item.id)) {
        // 多个组件一起移动，记录所有选中的组件
        this.pushHistory('移动/调整组件', selectedIds)
      } else {
        // 单个组件操作
        this.pushHistory('移动/调整组件', item.id)
      }
    },
    // 右键菜单相关方法
    handleComponentContextMenu(event, component) {
      // 预览模式下不显示右键菜单
      if (this.isPreview) {
        return
      }

      event.preventDefault()
      event.stopPropagation() // 阻止事件冒泡到父组件

      // 创建自定义事件，传递给主设计器处理
      const customEvent = new CustomEvent('container-component-contextmenu', {
        detail: {
          clientX: event.clientX,
          clientY: event.clientY,
          component: component,
          containerMethods: {
            hideCpt: this.hideCpt.bind(this),
            copyCpt: this.copyCpt.bind(this),
            delCpt: this.delCpt.bind(this)
          }
        },
        bubbles: true
      })

      // 触发自定义事件
      event.target.dispatchEvent(customEvent)
    },
    // 处理容器内部点击，关闭右键菜单
    handleContainerMouseDown(event) {
      // 如果点击的不是组件，则关闭右键菜单
      if (event.target === event.currentTarget) {
        // 发送关闭右键菜单的事件
        const closeEvent = new CustomEvent('close-context-menu', {
          bubbles: true
        })
        event.target.dispatchEvent(closeEvent)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container-draggable {
  position: relative;
  overflow: hidden;
}

.container-draggable {
  position: relative;
  isolation: isolate;
}
.close-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10000;
}
.separator {
  border: 1px dashed #cbcbcb;
  margin-bottom: 20px;
}
.grid-container {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24px;
  color: #999999;
  line-height: 24px;
  font-style: normal;
  display: grid;
  grid-template-columns: repeat(2, auto);
  row-gap: 22px;
  justify-content: space-between;
  > div:nth-child(even) {
    text-align: right;
  }
  > div:nth-child(odd) {
    text-align: left;
  }
}
</style>
