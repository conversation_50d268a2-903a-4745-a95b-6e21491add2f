<template>
  <div
    style="width: 100%; height: 100%"
    :style="{
      display: 'grid',
      gridTemplateColumns: option.attribute.gridColumns
        ? `repeat(${option.attribute.gridColumns}, 1fr)`
        : 'auto',
      gridTemplateRows: option.attribute.gridRows
        ? `repeat(${option.attribute.gridRows}, 1fr)`
        : 'auto',
      gridAutoFlow: 'row',
      columnGap: `${option.attribute.columnGap}px`,
      rowGap: `${option.attribute.rowGap}px`,
      justifyItems: option.attribute.justifyItems,
      alignItems: option.attribute.alignItems,
      justifyContent: option.attribute.justifyContent,
      alignContent: option.attribute.alignContent,
      gridAutoColumns:
        option.attribute.autoFill !== 'none'
          ? `${option.attribute.autoFill}(minmax(100px, 1fr))`
          : 'auto',
      paddingTop: `${option.attribute.paddingTop}px`,
      paddingRight: `${option.attribute.paddingRight}px`,
      paddingBottom: `${option.attribute.paddingBottom}px`,
      paddingLeft: `${option.attribute.paddingLeft}px`,
      background: option.attribute.backgroundImage
        ? `url(${getFileAccessHttpUrl(
            option.attribute.backgroundImage
          )}) no-repeat center center/100% 100%`
        : 'none',
      borderColor: option.attribute.borderColor,
      backgroundColor: option.attribute.backgroundColor,
      borderWidth: `${option.attribute.borderWidth}px`,
      borderStyle: option.attribute.borderStyle,
      outline: isDragOver ? '2px dashed #409eff' : 'none'
    }"
    @dragover.prevent.stop="handleDragOver"
    @drop.prevent.stop="handleDrop"
    @dragleave.prevent.stop="handleDragLeave"
    @dragenter.prevent.stop="handleDragEnter"
    @mousedown="handleContainerMouseDown"
  >
    <div
      v-for="(item, index) in componentData"
      :key="item.id"
      :ref="'div' + item.cptKey + index"
      class="cptDivInner"
      @mousedown.stop="handleMouseDown($event, item)"
      @contextmenu.prevent="handleComponentContextMenu($event, item)"
    >
      <div class="component-container" style="width: 100%; height: 100%">
        <component
          :is="item.cptKey"
          :id="item.id"
          :ref="item.id"
          :width="Math.round(item.cptWidth)"
          :height="Math.round(item.cptHeight)"
          :option="item.cptOption"
          :data="item.cptData"
          :columns="{ index: item.cptIndexList, dimension: item.cptDimensionList }"
          :children="item.children"
          :style="{ border: currentCptId === item.id ? '1px dashed #000000' : 'none' }"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getAction, postAction, getFileAccessHttpUrl } from '@/api/manage'
import Placeholder from '@/views/designer/components/Placeholder.vue'
import commonAttribute from '@/views/designer/components/commonAttribute.js'
import { handleSpecialForm } from '@/views/designer/components/handleSpecialForm'
export default {
  name: 'CptContainerGrid',
  components: {
    Placeholder
  },
  props: {
    id: {
      type: String,
      required: true
    },
    option: {
      type: Object,
      required: true
    },
    children: {
      type: Array,
      default: () => []
    },
    currentCptId: String
  },
  data() {
    return {
      isDragOver: false,
      componentData: [],
      nextNotEmptyCptIndex: 0,
      url: {
        previewData: '/index/scCockpitIndexGroupConfig/previewData',
        queryModule: '/module/scCockpitModuleConfig/queryById'
      }
    }
  },
  computed: {
    childrenNum() {
      return this.option.attribute.gridColumns * this.option.attribute.gridRows
    }
  },
  watch: {
    childrenNum: {
      handler(newVal, oldVal = 0) {
        let len = newVal - oldVal
        while (len < 0) {
          const cpt = this.componentData.splice(len, 1)[0]
          if (cpt.cptKey !== 'Placeholder') {
            this.nextNotEmptyCptIndex -= 1
          }
          len += 1
        }
        while (len > 0) {
          this.componentData.push({
            id: require('nanoid').nanoid(12),
            cptKey: 'Placeholder'
          })
          len -= 1
        }
        // 更新子组件到option.children
        this.saveComponentsToOption()
      },
      immediate: true
    },
    componentData: {
      handler() {
        this.saveComponentsToOption()
      },
      deep: true
    }
  },
  created() {
    let childrenLen = this.children.length
    this.nextNotEmptyCptIndex = childrenLen
    for (let i = 0; i < childrenLen; i++) {
      this.$set(this.componentData, i, this.children[i])
    }
  },
  mounted() {
    // 监听子组件更新事件
    this.$bus.$on('updateChildren', (sourceComponentId, children) => {
      const container = this.componentData.find((item) => item.id === sourceComponentId)
      if (container) {
        // 如果找到容器，则更新其子组件数据
        if (!container.children) {
          this.$set(container, 'children', [])
        }
        container.children = children
      }
    })
    this.$bus.$on('pid', (pid) => {
      this.id === pid && this.refreshCptData()
    })
  },
  beforeDestroy() {
    this.$bus.$off('updateChildren')
  },
  methods: {
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    refreshCptData() {
      const refName = this.currentCptId
      if (!this.$refs[refName][0].refreshCptData) {
        this.$message.warning('当前图层还未实现refreshCptData方法')
      } else {
        this.$refs[refName][0].refreshCptData() //刷新子组件数据，refs为组建名加index
      }
    },
    saveComponentsToOption() {
      // 过滤掉占位符组件，只保存实际组件
      const children = this.componentData.filter((item) => item.cptKey !== 'Placeholder')
      // 通过事件通知父组件更新子组件，传递容器ID
      this.$bus.$emit('updateChildren', this.id, children)
    },
    handleMouseDown(e, item) {
      if (item.cptKey === 'Placeholder') {
        return
      }
      e.stopPropagation()
      this.$bus.$emit('showConfigBar', item)
    },
    delCpt(cpt, index) {
      this.componentData.splice(index, 1)
      this.nextNotEmptyCptIndex -= 1
      // 删除组件后，添加一个Placeholder组件
      this.componentData.push({
        id: require('nanoid').nanoid(12),
        cptKey: 'Placeholder'
      })
    },
    handleDragEnter(event) {
      this.isDragOver = true
    },
    handleDragLeave(event) {
      this.isDragOver = false
    },
    handleDragOver(event) {
      // 检查是否已达到最大子组件数量
      if (this.nextNotEmptyCptIndex >= this.childrenNum) {
        // 如果已达到最大数量，不允许拖放
        event.dataTransfer.dropEffect = 'none'
        return
      }

      // 允许拖拽
      event.dataTransfer.dropEffect = 'copy'
      this.isDragOver = true
    },
    addComponent(cpt) {
      // this.componentData.push(cpt)
      this.$set(this.componentData, this.nextNotEmptyCptIndex, cpt)
      this.nextNotEmptyCptIndex += 1
      // 更新子组件到option.children
      this.saveComponentsToOption()
    },
    handleDrop(e) {
      this.isDragOver = false
      const cptConfig = e.dataTransfer.getData('config')
      // 检查是否已达到最大子组件数量
      if (this.nextNotEmptyCptIndex >= this.childrenNum) {
        console.warn('已达到最大子组件数量限制，忽略此拖放操作')
        return
      }

      //从组件栏丢下组件
      if (cptConfig) {
        let config = JSON.parse(cptConfig)
        if (config.option.cptDataForm) {
          if (!config.option.cptDataForm.apiUrl) {
            config.option.cptDataForm.apiUrl = '/design/test'
          }
          if (!config.option.cptDataForm.sql) {
            config.option.cptDataForm.sql = 'select username from sys_user limit 1'
          }
        }
        this.$nextTick(() => {})
        let cpt = {
          cptTitle: config.name,
          icon: config.icon,
          cptKey: config.cptKey,
          cptOptionKey: config.cptOptionKey ? config.cptOptionKey : config.cptKey + '-option',
          cptOption: config.option,
          cptCssData: {
            width: config.width ? config.width : 400,
            height: config.height ? config.height : 300
          },
          cptX: Math.round(e.offsetX),
          cptY: Math.round(e.offsetY),
          cptZ: 100,
          cptWidth: config.width ? config.width : 400,
          cptHeight: config.height ? config.height : 300,
          id: `${config.cptKey}-${require('nanoid').nanoid(12)}`,
          pid: this.id
        }
        this.addComponent(cpt)
        // this.cacheChoices = {} //多选清空
        // this.showConfigBar({}, cpt, this.componentData.length - 1) //丢下组件后刷新组件属性栏
        // this.$refs['configBar'].showCptConfig()
      } else {
        const indicatorId = e.dataTransfer.getData('indicatorId')
        // 拖拽绑定组件，此时无copyDom
        getAction(this.url.queryModule, { id: indicatorId }).then((res) => {
          if (res.success) {
            const componentData = JSON.parse(res.result.moduleData).componentList[0]
            postAction(this.url.previewData + `?id=${componentData.indexId}`, {
              connType: '',
              pageNo: 1,
              pageSize: 1000,
              where: []
            }).then((dataRes) => {
              if (dataRes.success) {
                const data = dataRes.result.records
                console.log()

                let cpt = {
                  cptTitle: componentData.titleName,
                  cptKey: componentData.componentType,
                  // cptOptionKey: componentData.componentType + '-option',
                  cptOption: {
                    ...JSON.parse(componentData.jsData || '{}'),
                    attribute: {
                      ...commonAttribute.attribute,
                      bgImage: componentData.bgImage || ''
                    }
                  },
                  cptCssData: JSON.parse(componentData.cssData || '{}'),
                  cptX: Math.round(e.offsetX),
                  cptY: Math.round(e.offsetY),
                  cptZ: 100,
                  cptWidth: componentData.jsData ? JSON.parse(componentData.cssData).width : 400,
                  cptHeight: componentData.cssData ? JSON.parse(componentData.cssData).height : 300,
                  cptData: data,
                  cptIndexList: componentData.indexList,
                  cptDimensionList: componentData.dimensionList,
                  isBindingComponent: true,
                  pid: this.id,
                  id: `${componentData.componentType}-${require('nanoid').nanoid(12)}`
                }
                handleSpecialForm(cpt)
                this.addComponent(cpt)
                // this.cacheChoices = {} //多选清空
                // this.showConfigBar({}, cpt, this.componentData.length - 1) //丢下组件后刷新组件属性栏
                // this.$refs['configBar'].showCptConfig()
              } else {
                this.$message.warning('组件数据查询失败')
              }
            })
          } else {
            this.$message.warning('组件信息查询失败')
          }
        })
      }
    },
    // 右键菜单相关方法
    handleComponentContextMenu(event, component) {
      if (component.cptKey === 'Placeholder') {
        return
      }

      // 预览模式下不显示右键菜单
      if (this.isPreview) {
        return
      }

      event.preventDefault()
      event.stopPropagation()

      const customEvent = new CustomEvent('container-component-contextmenu', {
        detail: {
          clientX: event.clientX,
          clientY: event.clientY,
          component: component,
          containerMethods: {
            hideCpt: this.hideCpt.bind(this),
            copyCpt: this.copyCpt.bind(this),
            delCpt: this.delCpt.bind(this)
          }
        },
        bubbles: true
      })

      event.target.dispatchEvent(customEvent)
    },
    // 处理容器内部点击，关闭右键菜单
    handleContainerMouseDown(event) {
      if (event.target === event.currentTarget) {
        const closeEvent = new CustomEvent('close-context-menu', {
          bubbles: true
        })
        event.target.dispatchEvent(closeEvent)
      }
    },
    // 添加缺失的方法
    hideCpt(item) {
      item.cptOption.attribute.visible = !item.cptOption.attribute.visible
    },
    copyCpt(item) {
      const generateNewIds = (component, pid) => {
        component.pid = pid
        component.id = `${component.cptKey}-${require('nanoid').nanoid(12)}`
        if (component.children && component.children.length > 0) {
          component.children.forEach((child) => generateNewIds(child, component.id))
        }
      }

      let copyItem = JSON.parse(JSON.stringify(item))
      copyItem.cptX = item.cptX + 30
      copyItem.cptY = item.cptY + 30

      generateNewIds(copyItem, copyItem.pid || '')

      // 找到第一个空位置放置复制的组件
      const emptyIndex = this.componentData.findIndex((comp) => comp.cptKey === 'Placeholder')
      if (emptyIndex !== -1) {
        this.$set(this.componentData, emptyIndex, copyItem)
        this.nextNotEmptyCptIndex += 1
      }
    }
  }
}
</script>

<style scoped>
.cptDivInner {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
