<template>
  <div
    style="display: flex"
    :style="{
      width: `${$attrs.width}px`,
      height: `${$attrs.height}px`,
      alignItems: option.attribute.alignItems,
      justifyContent: option.attribute.justifyContent,
      flexDirection: option.attribute.flexDirection,
      flexWrap: option.attribute.flexWrap,
      gap: `${option.attribute.gap}px`,
      paddingTop: `${option.attribute.paddingTop}px`,
      paddingRight: `${option.attribute.paddingRight}px`,
      paddingBottom: `${option.attribute.paddingBottom}px`,
      paddingLeft: `${option.attribute.paddingLeft}px`,
      background: option.attribute.backgroundImage
        ? `url(${getFileAccessHttpUrl(
            option.attribute.backgroundImage
          )}) no-repeat center center/100% 100%`
        : 'none',
      backgroundColor: option.attribute.backgroundColor,
      borderColor: option.attribute.borderColor,
      borderWidth: `${option.attribute.borderWidth}px`,
      borderStyle: option.attribute.borderStyle,
      outline: isDragOver ? '2px dashed #409eff' : 'none',
      position: 'relative',
      isolation: 'isolate'
    }"
    @dragover.prevent.stop="handleDragOver"
    @drop.prevent.stop="handleDrop"
    @dragleave.prevent.stop="handleDragLeave"
    @dragenter.prevent.stop="handleDragEnter"
    @mousedown="handleContainerMouseDown"
  >
    <button v-if="option.attribute.isPopup" class="close-btn" @click="close">关闭</button>
    <div
      v-for="(item, index) in containerComponentData"
      :key="item.id"
      class="cptDivInner"
      @mousedown="handleMouseDown($event, item)"
      @contextmenu.prevent="handleComponentContextMenu($event, item)"
    >
      <div class="component-container" style="width: 100%; height: 100%">
        <component
          :is="item.cptKey"
          :id="item.id"
          :ref="item.id"
          :width="Math.round(item.cptWidth)"
          :height="Math.round(item.cptHeight)"
          :option="item.cptOption"
          :indexId="item.cptIndexId"
          :paramsForUnBindingComp="item.paramsToLoadDataWithoutBindingIndexSet"
          :isPreview="isPreview"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { useDesignerStore } from '@/stores/designer'
import { findNodeById } from '@/utils/util'
import { getAction, postAction, getFileAccessHttpUrl } from '@/api/manage'
import Placeholder from '@/views/designer/components/Placeholder.vue'
import { handleSpecialForm } from '@/views/designer/components/handleSpecialForm'
import commonAttribute from '@/views/designer/components/commonAttribute.js'
import cptOptions from '@/components/options'
import { chartItem } from '@/views/designer/components/constant.js'
export default {
  name: 'CptContainerFlex',
  components: {
    Placeholder
  },
  props: {
    id: {
      type: String,
      required: true
    },
    option: {
      type: Object,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isDragOver: false,
      containerComponentData: [],
      nextNotEmptyCptIndex: 0, // 下一个非空组件的索引
      url: {
        previewData: '/index/scCockpitIndexGroupConfig/previewData',
        queryIndexSet: '/module/scCockpitModuleConfig/queryByIndexId'
      }
    }
  },
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCptId', 'currentCpt', 'componentData']),
    childrenNum() {
      return this.option.attribute.childrenNum
    }
  },
  watch: {
    componentData: {
      handler(newVal) {
        const children = findNodeById(this.id, newVal)?.children ?? []
        this.nextNotEmptyCptIndex = children.length
        this.containerComponentData = children
        // for (let i = 0; i < children.length; i++) {
        //   this.$set(this.containerComponentData, i, children[i])
        // }
      },
      deep: true
    }
    // childrenNum: {
    //   handler(newVal, oldVal = 0) {
    //     let len = newVal - oldVal
    //     while (len < 0) {
    //       const cpt = this.containerComponentData.splice(len, 1)[0]
    //       if (cpt.cptKey !== 'Placeholder') {
    //         this.nextNotEmptyCptIndex -= 1
    //       }
    //       len += 1
    //     }
    //     while (len > 0) {
    //       this.containerComponentData.push({
    //         id: require('nanoid').nanoid(12),
    //         cptKey: 'Placeholder'
    //       })
    //       len -= 1
    //     }
    //     // 更新子组件到option.children
    //     this.saveComponentsToOption()
    //   },
    //   immediate: true
    // }
  },
  created() {
    const children = findNodeById(this.id, this.componentData)?.children ?? []
    this.nextNotEmptyCptIndex = children.length
    for (let i = 0; i < children.length; i++) {
      this.$set(this.containerComponentData, i, children[i])
    }
    this.$bus.$on('pid', (pid) => {
      this.id === pid && this.refreshCptData()
    })
  },
  methods: {
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    refreshCptData() {
      const refName = this.currentCptId
      if (!this.$refs[refName][0].refreshCptData) {
        this.$message.warning('当前图层还未实现refreshCptData方法')
      } else {
        this.$refs[refName][0].refreshCptData() //刷新子组件数据，refs为组建名加index
      }
    },
    addComponent(cpt) {
      // this.containerComponentData.push(cpt)
      this.$set(this.containerComponentData, this.nextNotEmptyCptIndex, cpt)
      this.nextNotEmptyCptIndex += 1
      // 更新子组件到option.children
      this.saveComponentsToOption()
    },
    delCpt(cpt, index) {
      this.containerComponentData.splice(index, 1)
      this.nextNotEmptyCptIndex -= 1
      // 删除组件后，添加一个Placeholder组件
      this.containerComponentData.push({
        id: require('nanoid').nanoid(12),
        cptKey: 'Placeholder'
      })
      this.saveComponentsToOption()
    },
    saveComponentsToOption() {
      // 过滤掉占位符组件，只保存实际组件
      const children = this.containerComponentData.filter((item) => item.cptKey !== 'Placeholder')
      const node = findNodeById(this.id, this.componentData)
      if (node) {
        this.$set(node, 'children', children)
      }
    },
    handleMouseDown(e, item) {
      if (item.cptKey === 'Placeholder') {
        return
      }
      e.stopPropagation()
      this.currentCptId = item.id
      this.currentCpt = item
    },
    handleDragEnter(event) {
      this.isDragOver = true
    },
    handleDragLeave(event) {
      this.isDragOver = false
    },
    handleDragOver(event) {
      // 检查是否已达到最大子组件数量
      if (this.nextNotEmptyCptIndex >= this.childrenNum) {
        // 如果已达到最大数量，不允许拖放
        event.dataTransfer.dropEffect = 'none'
        return
      }

      // 允许拖拽
      event.dataTransfer.dropEffect = 'copy'
      this.isDragOver = true
    },
    async handleDrop(e) {
      this.isDragOver = false
      const cptConfig = e.dataTransfer.getData('config')
      // 检查是否已达到最大子组件数量
      if (this.nextNotEmptyCptIndex >= this.childrenNum) {
        console.warn('已达到最大子组件数量限制，忽略此拖放操作')
        return
      }

      //从组件栏丢下组件
      if (cptConfig) {
        let config = JSON.parse(cptConfig)
        let option = {}
        const compTemplateInfoRes = await getAction('/component/scCockpitIndexComponent/list', {
          componentName: config.name
        })
        const compTemplateInfo = compTemplateInfoRes.result.records[0]
        if (compTemplateInfo) {
          try {
            option = new Function('return ' + compTemplateInfo.jsData)() // 字符串转对象，并去掉了'option = ''
          } catch (error) {
            console.error('解析字符串代码时出错:', error)
          }
        }
        config.option = Object.assign(config.option, option)
        if (config.option.cptDataForm) {
          if (!config.option.cptDataForm.apiUrl) {
            config.option.cptDataForm.apiUrl = '/design/test'
          }
          if (!config.option.cptDataForm.sql) {
            config.option.cptDataForm.sql = 'select username from sys_user limit 1'
          }
        }
        this.$nextTick(() => {})
        let component = {
          cptTitle: config.name,
          icon: config.icon,
          cptKey: config.cptKey,
          cptOptionKey: config.cptOptionKey ? config.cptOptionKey : config.cptKey + '-option',
          cptOption: config.option,
          cptCssData: {
            width: config.width ? config.width : 400,
            height: config.height ? config.height : 300
          },
          paramsToLoadDataWithoutBindingIndexSet: {
            tableName: '',
            indexList: [],
            dimensionList: [],
            dataSourceCode: '',
            unitList: []
          },
          cptX: Math.round(e.offsetX),
          cptY: Math.round(e.offsetY),
          cptZ: 100,
          cptWidth: config.width ? config.width : 400,
          cptHeight: config.height ? config.height : 300,
          id: `${config.cptKey}-${require('nanoid').nanoid(12)}`,
          pid: this.id
        }
        this.addComponent(component)
        // this.$refs['configBar'].showCptConfig()
      } else {
        const indicatorId = e.dataTransfer.getData('indicatorId')
        // 拖拽绑定组件，此时无copyDom
        getAction(this.url.queryIndexSet, { indexId: indicatorId }).then((res) => {
          if (res.success) {
            const cpt = JSON.parse(res.result.moduleData).componentList[0]
            let option = {}
            try {
              option = new Function('return ' + cpt.jsData)() // 字符串转对象，并去掉了'option = ''
            } catch (error) {
              console.error('解析字符串代码时出错:', error)
            }
            const attribute = this.getAttribute(cpt.componentType)
            let component = {
              cptTitle: cpt.titleName,
              cptKey: cpt.componentType,
              // cptOptionKey: componentData.componentType + '-option',
              cptOption: {
                ...option,
                attribute: {
                  ...attribute,
                  bgImage: cpt.bgImage || ''
                }
              },
              cptCssData: JSON.parse(cpt.cssData || '{}'),
              cptX: Math.round(e.offsetX),
              cptY: Math.round(e.offsetY),
              cptZ: 100,
              cptWidth: cpt.jsData ? JSON.parse(cpt.cssData).width : 400,
              cptHeight: cpt.cssData ? JSON.parse(cpt.cssData).height : 300,
              cptIndexId: cpt.indexId,
              // cptData: data,
              // cptIndexList: cpt.indexList,
              // cptDimensionList: cpt.dimensionList,
              isBindingComponent: true,
              pid: this.id,
              id: `${cpt.componentType}-${require('nanoid').nanoid(12)}`
            }
            handleSpecialForm(component)
            this.addComponent(component)
            // this.$refs['configBar'].showCptConfig()
          } else {
            this.$message.warning('组件信息查询失败')
          }
        })
      }
    },
    getAttribute(componentType) {
      for (const { children } of cptOptions) {
        if (!children) continue
        const found = children.find((child) => child.cptKey === componentType)
        if (found) return found.option.attribute
      }
      for (const { content } of chartItem) {
        if (!content) continue
        const found = content.find((child) => child.cptKey === componentType)
        if (found) return found.option.attribute
      }
    },
    open() {
      this.$emit('popup', true)
    },
    close() {
      this.$emit('popup', false)
    },
    // 右键菜单相关方法
    handleComponentContextMenu(event, component) {
      if (component.cptKey === 'Placeholder') {
        return
      }

      // 预览模式下不显示右键菜单
      if (this.isPreview) {
        return
      }

      event.preventDefault()
      event.stopPropagation()

      const customEvent = new CustomEvent('container-component-contextmenu', {
        detail: {
          clientX: event.clientX,
          clientY: event.clientY,
          component: component,
          containerMethods: {
            hideCpt: this.hideCpt.bind(this),
            copyCpt: this.copyCpt.bind(this),
            delCpt: this.delCpt.bind(this)
          }
        },
        bubbles: true
      })

      event.target.dispatchEvent(customEvent)
    },
    // 处理容器内部点击，关闭右键菜单
    handleContainerMouseDown(event) {
      if (event.target === event.currentTarget) {
        const closeEvent = new CustomEvent('close-context-menu', {
          bubbles: true
        })
        event.target.dispatchEvent(closeEvent)
      }
    },
    // 添加缺失的方法
    hideCpt(item) {
      item.cptOption.attribute.visible = !item.cptOption.attribute.visible
    },
    copyCpt(item) {
      const generateNewIds = (component, pid) => {
        component.pid = pid
        component.id = `${component.cptKey}-${require('nanoid').nanoid(12)}`
        if (component.children && component.children.length > 0) {
          component.children.forEach((child) => generateNewIds(child, component.id))
        }
      }

      let copyItem = JSON.parse(JSON.stringify(item))
      copyItem.cptX = item.cptX + 30
      copyItem.cptY = item.cptY + 30

      generateNewIds(copyItem, copyItem.pid || '')

      // 添加到容器中
      this.addComponent(copyItem)
    }
  }
}
</script>

<style scoped>
.cptDivInner {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
}

.close-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10000;
}
</style>
