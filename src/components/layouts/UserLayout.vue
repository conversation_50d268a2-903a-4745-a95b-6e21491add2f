<template>
  <div v-if="showFlag" id="userLayout" :class="['user-layout-wrapper', device]">
    <div class="user-layout-bg"></div>
    <div class="container">
      <div class="top"></div>

      <div class="login-container">
        <div class="login-title">
          <p>{{ systemName }}</p>
          <p>thematic application scenarios Support platform</p>
        </div>
        <div class="login-box">
          <div class="login-main">
            <route-view></route-view>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="links">
          <!-- <a href="http://doc.jeecg.com" target="_blank">帮助</a>
          <a href="https://github.com/zhangdaiscott/jeecg-boot" target="_blank">隐私</a>
          <a href="https://github.com/zhangdaiscott/jeecg-boot/blob/master/LICENSE" target="_blank">条款</a> -->
        </div>
        <div class="copyright">
          <!-- Copyright &copy; 2019 <a href="http://www.jeecg.com" target="_blank">JEECG开源社区</a> 出品 -->
          Copyright &copy; {{ year }} {{ footText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RouteView from '@/components/layouts/RouteView'
import { mixinDevice } from '@/utils/mixin.js'
import config from '@/defaultSettings'

export default {
  name: 'UserLayout',
  components: { RouteView },
  mixins: [mixinDevice],
  data() {
    return {
      systemName: config.systemName,
      footText: config.footText,
      year: new Date().getFullYear(),
      showFlag: process.env.VUE_APP_SSO != 'true',
    }
  },
  mounted() {
    document.body.classList.add('userLayout')

    this.$ls.set('loginSystemPath', this.$route.fullPath)
  },
  beforeDestroy() {
    document.body.classList.remove('userLayout')
  },
}
</script>

<style lang="less" scoped>
#userLayout.user-layout-wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  min-width: 1200px;
  min-height: 700px;

  .user-layout-bg {
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: #07112c url('~@/assets/login/bg.png') no-repeat center center / 100% 100%;
  }

  &.mobile {
    .container {
      .main {
        max-width: 368px;
        width: 98%;
      }
    }
  }

  .container {
    width: 100%;
    min-height: 100%;
    // background: #f0f2f5 url(~@/assets/background.svg) no-repeat 50%;
    background-size: 100%;
    // padding: 110px 0 144px;
    position: relative;

    a {
      text-decoration: none;
    }

    .top {
      text-align: center;

      .header {
        height: 44px;
        line-height: 44px;

        .badge {
          position: absolute;
          display: inline-block;
          line-height: 1;
          vertical-align: middle;
          margin-left: -12px;
          margin-top: -10px;
          opacity: 0.8;
        }

        .logo {
          height: 44px;
          vertical-align: top;
          margin-right: 16px;
          border-style: none;
        }

        .title {
          font-size: 33px;
          color: rgba(0, 0, 0, 0.85);
          font-family: 'Chinese Quote', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
            'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
            'Segoe UI Symbol';
          font-weight: 600;
          position: relative;
          top: 2px;
        }
      }
      .desc {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        margin-top: 12px;
        margin-bottom: 40px;
      }

      .header-img {
        min-width: 420px;
        width: 30vw;
        margin-top: 5vh;

        @media (max-height: 700px) {
          & {
            margin-top: 2vh;
          }
        }
      }
    }

    .login-container {
      position: absolute;
      width: 523px;
      top: 50%;
      margin-top: -350px;
      height: 665px;
      right: 13.54vw;

      // @media (max-width: 1400px) {
      //   & {
      //     right: 16vw;
      //   }
      // }

      .login-title {
        height: 94px;
        margin: 0 -5px 0 -25px;
        padding-left: 104px;
        padding-top: 14px;
        background: url('~@/assets/login/logo.png') no-repeat left center;

        & > p {
          color: #ffffff;
          font-size: 40px;
          font-weight: bold;
          color: #ffffff;
          line-height: 45px;
          letter-spacing: 4px;
          margin-bottom: 0px;

          &:last-of-type {
            text-transform: uppercase;
            font-size: 16px;
            letter-spacing: 0px;
          }
        }
      }

      .login-box {
        width: 100%;
        // min-height: 500px;
        // margin-top: -5vh;

        .login-main {
          overflow: hidden;
          margin-top: 40px;
          border: 1px solid #c3f8ff;
          box-shadow: 0 0 80px 0 #1e73d9;
          border-radius: 8px;
          background-color: transparent;
          padding: 30px;
        }
      }
    }

    .main {
      // min-width: 260px;
      // width: 368px;
      // margin: 0 auto;
    }

    .footer {
      position: fixed;
      width: 100%;
      bottom: 0;
      padding: 0 16px;
      margin: 48px 0 24px;
      text-align: center;

      .links {
        margin-bottom: 8px;
        font-size: 14px;
        a {
          color: rgba(0, 0, 0, 0.45);
          transition: all 0.3s;
          &:not(:last-child) {
            margin-right: 40px;
          }
        }
      }
      .copyright {
        color: #ffffff;
        font-size: 16px;
      }
    }
  }
}
</style>
