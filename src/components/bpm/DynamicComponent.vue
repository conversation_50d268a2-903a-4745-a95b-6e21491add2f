
<template>
  <component
    :is="comp"
    v-if="comp"
    ref="compModel"
    :formData="formData"
    @ok="callBackOk"
    @close="callBackClose"></component>
</template>
<script>
  export default {
    name: 'DynamicComponent',
    props: ['path','formData'],
    data () {
      return {
        compName: this.path
      }
    },
    computed: {
      comp: function () {
        return () => import(`@/views/${this.compName}.vue`)
      }
    },
    methods: {
      add () {
        this.$refs.compModel.add();
      },
      callBackClose () {
        this.$emit('close');
      },
      handleOk () {
        this.$refs.compModel.handleOk();
      },
      callBackOk(){
        this.$emit('ok');
        this.close();
      },
    }
  }
</script>