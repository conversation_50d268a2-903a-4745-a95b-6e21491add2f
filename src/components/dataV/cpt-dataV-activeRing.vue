<template>
  <dv-active-ring-chart :key="refreshFlagKey" :config="config" style="width: 100%; height: 100%" />
</template>

<script>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData'

export default {
  name: 'CptDataVActiveRing',
  props: {
    width: Number,
    height: Number,
    option: Object
  },
  data() {
    return {
      uuid: null,
      config: {},
      refreshFlagKey: null
    }
  },
  watch: {
    'option.attribute': {
      handler() {
        this.loadData()
      },
      deep: true //深度监听
    },
    width() {
      this.refreshFlagKey = require('nanoid').nanoid(12)
    },
    height() {
      this.refreshFlagKey = require('nanoid').nanoid(12)
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
    this.refreshCptData()
  },
  methods: {
    refreshCptData() {
      pollingRefresh(this.uuid, this.option.cptDataForm, this.loadData)
      this.refreshFlagKey = require('nanoid').nanoid(12)
    },
    loadData() {
      getDataJson(this.option.cptDataForm).then((res) => {
        this.config = JSON.parse(JSON.stringify(this.option.attribute))
        this.config.data = res
      })
    }
  }
}
</script>

<style scoped></style>

