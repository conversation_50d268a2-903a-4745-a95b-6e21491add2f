<template>
  <dv-percent-pond :key="refreshFlagKey" :config="pondConfig" style="width: 100%; height: 100%" />
</template>

<script>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData'

export default {
  name: 'CptDataVPercentPond',
  props: {
    width: Number,
    height: Number,
    option: Object
  },
  data() {
    return {
      uuid: null,
      pondConfig: {},
      refreshFlagKey: null //强制刷新视图
    }
  },
  watch: {
    'option.attribute': {
      handler() {
        this.loadData()
      },
      deep: true //深度监听
    },
    width() {
      this.refreshFlagKey = require('nanoid').nanoid(12)
    },
    height() {
      this.refreshFlagKey = require('nanoid').nanoid(12)
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
    this.refreshFlagKey = require('nanoid').nanoid(12)
    this.refreshCptData()
  },
  methods: {
    refreshCptData() {
      pollingRefresh(this.uuid, this.option.cptDataForm, this.loadData)
    },
    loadData() {
      getDataJson(this.option.cptDataForm).then((res) => {
        let tempConfig = JSON.parse(JSON.stringify(this.option.attribute))
        tempConfig.value = res.value
        tempConfig.lineDash = [tempConfig.lineWidth, tempConfig.lineSpace]
        this.pondConfig = tempConfig
        this.refreshFlagKey = require('nanoid').nanoid(12) //强制刷新视图 报错为dataV组件内部bug
      })
    }
  }
}
</script>

<style scoped></style>

