<template>
  <el-form label-width="100px" size="mini">
    <el-form-item label="边框粗细">
      <el-input-number v-model="attribute.borderWidth" :min="1" :max="100"/>
    </el-form-item>
    <el-form-item label="边框圆角">
      <el-input-number v-model="attribute.borderRadius" :min="0" :max="100"/>
    </el-form-item>
    <el-form-item label="内边距">
      <el-input-number v-model="attribute.borderGap" :min="0" :max="100"/>
    </el-form-item>
    <el-form-item label="局部渐变">
      <el-switch v-model="attribute.localGradient"/>
    </el-form-item>
    <el-form-item label="线条粗细">
      <el-input-number v-model="attribute.lineWidth" :min="0" :max="50"/>
    </el-form-item>
    <el-form-item label="线条间隔">
      <el-input-number v-model="attribute.lineSpace" :min="0" :max="20"/>
    </el-form-item>
    <!--    <el-form-item label="颜色1">
      <el-color-picker v-model="attribute.colors[0]" show-alpha/>
    </el-form-item>
    <el-form-item label="颜色2">
      <el-color-picker v-model="attribute.colors[1]" show-alpha/>
    </el-form-item>-->
  </el-form>
</template>

<script>
export default {
  name: "CptDataVPercentPondOption",
  props: { attribute: Object },
}
</script>

<style scoped>

</style>
