<template>
  <component
    :is="option.attribute.decorationType"
    :key="refreshFlagKey"
    :style="{ width: width + 'px', height: height + 'px', color: option.attribute.textColor }"
    :color="[option.attribute.color1, option.attribute.color2]"
    >{{ option.attribute.text }}</component
  >
</template>

<script>
export default {
  name: 'CptDataVDecoration',
  props: {
    width: Number,
    height: Number,
    option: Object
  },
  data() {
    return {
      refreshFlagKey: require('nanoid').nanoid(12)
    }
  },
  watch: {
    option: {
      handler() {
        this.refreshFlagKey = require('nanoid').nanoid(12) //强制刷新视图
      },
      deep: true //深度监听
    },
    width() {
      this.refreshFlagKey = require('nanoid').nanoid(12)
    }
  }
}
</script>

<style scoped></style>

