<template>
  <dv-scroll-ranking-board
    :config="config"
    :style="{ width: width + 'px', height: height + 'px' }"
  />
</template>

<script>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData'

export default {
  name: 'CptDataVScrollList',
  props: {
    width: Number,
    height: Number,
    option: Object
  },
  data() {
    return {
      config: {},
      uuid: null
    }
  },
  watch: {
    'option.attribute': {
      handler() {
        this.loadData()
      },
      deep: true //深度监听
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
    this.refreshCptData()
  },
  methods: {
    refreshCptData() {
      pollingRefresh(this.uuid, this.option.cptDataForm, this.loadData)
    },
    loadData() {
      getDataJson(this.option.cptDataForm).then((res) => {
        this.config = JSON.parse(JSON.stringify(this.option.attribute))
        this.config.data = res
      })
    }
  }
}
</script>

<style scoped></style>

