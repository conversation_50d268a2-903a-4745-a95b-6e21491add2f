module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    commonjs: true,
    es6: true,
    amd: true
  },
  extends: ['plugin:vue/recommended', 'eslint:recommended'],
  parserOptions: {
    parser: '@babel/eslint-parser'
  },
  rules: {
    // 0表示不处理，1表示警告，2表示错误并退出
    'generator-star-spacing': 'off',
    'no-mixed-operators': 'off',
    'vue/max-attributes-per-line': 'off',
    'vue/attribute-hyphenation': 'off',
    'vue/html-self-closing': 'off',
    'vue/component-name-in-template-casing': 'off',
    'vue/html-closing-bracket-spacing': 'off',
    'vue/singleline-html-element-content-newline': 'off',
    'vue/no-unused-components': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/no-use-v-if-with-v-for': 'warn',
    'vue/html-closing-bracket-newline': 'off',
    'vue/no-parsing-error': 'off',
    'vue/multi-word-component-names': 'off',
    'no-tabs': 'off',
    indent: ['off', 2],
    'no-console': 'off',
    'space-before-function-paren': 'off',
    semi: 'off',
    'no-unused-vars': 'off'
  }
}
